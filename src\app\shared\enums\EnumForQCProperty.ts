export const EnumForQCProperty = {
    ITEM_DIMENSION: 'ITEM_DIMENSION',
    ITEM_WEIGHT: 'ITEM_WEIGHT',
    ITEM_WEIGHT_WITH_BOX: 'ITEM_WEIGHT_WITH_BOX',
    CARTON_DIMENSION: 'CARTON_DIMENSION',
    CARTON_WEIGHT: 'CARTON_WEIGHT',
    COLOR: 'COLOR',
    PACKING_TYPES: 'PACKING_TYPES'
} as const

export const EnumForTicketType = {
    BT: "BT",
    ST: "ST",
    WT: "WT",
    SC: "SC",
    QC_CHECK: "QC_CHECK",
    REAL_PHOTO: "REAL_PHOTO",
    CAPTURE_DIMENSION: "CAPTURE_DIMENSION"
} as const

export const EnumForTicketTypeLabel = {
    BT: "Branch",
    ST: "Stock Transfer",
    WT: "Warehouse",
    SC: "Stock check",
    QC_CHECK: "QC Check",
    REAL_PHOTO: "Real Photo",
    CAPTURE_DIMENSION: "Capture Dimension"
} as const
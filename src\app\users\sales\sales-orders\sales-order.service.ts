import { computed, inject, Injectable, signal, TemplateRef } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { EnumForInquiryType } from "@enums/EnumForInquiryType";
import { EnumForOrderUnit } from "@enums/EnumForOrderUnit.enum";
import { EnumForSalesOrderSaveStatus } from "@enums/EnumForSalesOrderSaveStatus";
import { EnumForSalesOrderTabs } from "@enums/EnumForSalesOrderTabs";
import { createObjectSignal, createArraySignal, isNotNilOrEmpty } from "@libs";
import { SalesPagination } from "@modal/request/SalesPagination";
import { InquiryListing, InquiryListingItem } from "@modal/SalesInquiry";
import { SalesOrderItemDraftListing, SODraftPage } from "@modal/SODraft";
import { NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { NgbModalService } from "@service/ngb-modal.service";
import { UtilsService } from "@service/utils.service";
import dayjs from "dayjs";
import saveAs from "file-saver";
import moment from "moment";
import { CountdownConfig } from "ngx-countdown";
import { map, Subject, takeUntil, tap } from "rxjs";
import { UPCOMING_SO_STOCK } from "src/app/shared/constants/constant";

interface DropdownData {
    customerLead: any[];
    inquiryType: any[];
}

@Injectable()
export class SalesOrderService {

    private modalService = inject(NgbModalService);
    utilsService = inject(UtilsService);

    noteControl = new FormControl('', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)]));

    extendForm = new FormGroup({
        time: new FormControl(null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_DIGIT_SEMICOLON), Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])),
        note: new FormControl(null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)]))
    })

    enumForTabs = EnumForSalesOrderTabs;
    enumForInquiryType = EnumForInquiryType;
    enumForOrderUnit = EnumForOrderUnit;
    enumForSalesOrderSaveStatus = EnumForSalesOrderSaveStatus

    paginationRequest = createObjectSignal({} as SalesPagination);
    inquiryList = createArraySignal([] as InquiryListing[]);
    inquiryListObj = createObjectSignal({} as InquiryListing);

    draftList = createArraySignal([] as SODraftPage[]);
    draftListObj = createObjectSignal({} as SODraftPage);
    itemObj = createObjectSignal({} as SalesOrderItemDraftListing);

    dropdown: DropdownData = {
        customerLead: [],
        inquiryType: []
    };

    selectedTab = signal<string>(this.enumForTabs.INQUIRY);
    isInquiryListEmpty = computed(() => (this.inquiryList.get() || []).length === 0);
    isDraftListEmpty = computed(() => (this.draftList.get() || []).length === 0);
    flagForExpandAll = computed(() => this.inquiryList.get().every(item => item.isExpand))
    inquiryAllCollappsed = computed(() => this.inquiryList.get().every(item => !item.isExpand))

    itemDisplayName = computed(() => this.itemObj.get()?.itemInfo?.displayName);
    salesOrderId = computed(() => this.draftListObj.get()?.salesOrderNo);

    activeTabQtyTab: number = 0;

    destroy$ = new Subject<void>();
    isExpandedIDs: { parentIds: number[] } = { parentIds: [] };
    isUpcomingTab = false;
    pagination: any;

    selectedInquiryItemIds: number[] = [];
    convertToSOIds: number[] = [];
    private dateCallCount = 0;
    totalSum = signal<number>(0);

    constructor() {
    }

    initPagination = () => {

        if (this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_INQUIRY])) {
            this.selectedTab.set(this.enumForTabs.INQUIRY)
        }

        if (this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_SO]) && !this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_INQUIRY])) {
            this.selectedTab.set(this.enumForTabs.DRAFT)
        }

        let ls_param = null
        ls_param = JSON.parse(localStorage.getItem('param'))

        if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
            if (ls_param.pageName === 'SO') {
                this.selectedTab.set(ls_param.selectedTab);
            }
        }

        this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: "100", orderStatus: this.selectedTab(), showOnlyHold: false }));
        this.onChangeTab(this.selectedTab() ? this.selectedTab() : this.enumForTabs.INQUIRY);
        this.pageRequiredData();
    }

    onChangeTab(tab: string): void {
        this.dateCallCount = 0; // Reset counter for new tab
        this.selectedTab.set(tab);
        this.onClear();
        this.isExpandedIDs.parentIds = []
    }

    onRefresh = () => {
        switch (this.selectedTab()) {
            case this.enumForTabs.INQUIRY:
                this.getCustomerWiseListing()
                break;
            case this.enumForTabs.STOCK_CHECK:
                break;
            case this.enumForTabs.DRAFT:
                this.getDraftListing()
                this.pageRequiredData();
                break;
            case this.enumForTabs.SO_CREATED:
                this.getDraftListing()
                this.pageRequiredData();
                break;
            case this.enumForTabs.PROFORMA_INVOICE:
                break;
            case this.enumForTabs.OTHER_BRANCH:
                break;
            case this.enumForTabs.PACKING:
                break;
            case this.enumForTabs.SHIPPED:
                break;
            case this.enumForTabs.DELIVERED:
                break;
            case this.enumForTabs.COMPLETED:
                break;
        }
    }

    getCustomerWiseListing = () => {
        const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData'])

        this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_LISTING, param, null, true).pipe(
            tap((res) => {
                this.pagination = res.pagination;
                this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'] }));
            }),
            map((res) => res?.['content']),
            tap((data: InquiryListing[]) => {
                if (!this.utilsService.isNullUndefinedOrBlank(data)) {
                    this.inquiryList.set(data.map(item => ({ ...item, items: [] })));
                    this.expandOnFilter(data, 2);
                }
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    getInquiryByCustomerId = (customerId: number, index: number) => {

        const current = this.inquiryList.get()[index];
        if (current?.loadedChild) return;

        const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange'])
        param.customerId = customerId;

        this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_BY_CUSTOMER_ID, param, null, true).pipe(
            tap((data: InquiryListingItem[]) => {
                this.inquiryList.updateAt(index, a => ({
                    ...a,
                    items: data.map(item => ({ ...item, followUpDateTime: item?.followUpDateTime ? moment(item.followUpDateTime) : null })),
                    loadedChild: true
                }))
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    expandOnFilter = (list: InquiryListing[] = this.inquiryList.get(), expandCount: number = 5) => {
        const { searchText, fromDate, toDate, customerId, inquiryType } = this.paginationRequest.get();
        const hasFilters = searchText || fromDate || toDate || customerId || inquiryType;

        if (hasFilters && list.length > 0) {
            this.inquiryList.update(items =>
                items.map((item, index) => ({ ...item, isExpand: index < expandCount }))
            );

            const limitedList = list.slice(0, expandCount);
            for (const item of limitedList) {
                const index = this.inquiryList.get().findIndex(i => i.customerId === item.customerId);
                if (index !== -1) {
                    this.getInquiryByCustomerId(item.customerId, index);
                }
            }
        }
    };

    pageRequiredData = () => {
        this.utilsService.get(this.utilsService.serverVariableService.INQUIRY_PAGE_REQ_DATA, null, true).pipe(
            tap((data: DropdownData) => {
                this.dropdown = data;
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Filter
    onChangeFilter = (event: any, type: 'date' | 'search' | 'customerId' | 'inquiryType' | 'showOnlyHold', isDateClear?: boolean, isAllClear = false) => {
        switch (type) {
            case 'date':
                this.dateCallCount++;

                // Skip first 2 calls
                if (this.dateCallCount <= 2 && (!event || (event?.start === null && event?.end === null)) && !isAllClear) {
                    return;
                }
                if (event?.start === null && event?.end === null && !isDateClear) {
                    return;
                }
                const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
                const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
                this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
                break;
            case 'search':
                this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
                break;
            case 'customerId':
                this.paginationRequest.update(a => ({ ...a, customerId: event }));
                break;
            case 'inquiryType':
                this.paginationRequest.update(a => ({ ...a, inquiryType: event }));
                break;
            case 'showOnlyHold':
                this.paginationRequest.update(a => ({ ...a, showOnlyHold: event }));
                break;
        }
        this.destroy$.complete()
        if (!isAllClear) {
            this.tabWiseApi();
        }
    }

    tabWiseApi = () => {
        switch (this.selectedTab()) {
            case this.enumForTabs.INQUIRY:
                this.getCustomerWiseListing();
                break;
            case this.enumForTabs.DRAFT:
                this.getDraftListing();
                break;
            case this.enumForTabs.SO_CREATED:
                this.getDraftListing();
                break;
        }
    }

    // Filters Clear
    onClear = () => {
        this.dateCallCount = 0;
        switch (this.selectedTab()) {
            case this.enumForTabs.INQUIRY:
            case this.enumForTabs.DRAFT:
            case this.enumForTabs.SO_CREATED:
                this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null, searchText: null, customerId: null, inquiryType: null, showOnlyHold: false }));
                this.onChangeFilter(null, 'date', true, true);
                this.tabWiseApi();
                break;
        }
    }

    onClearDateOnly = () => {
        this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null }));
        this.onChangeFilter(null, 'date', true);
    }

    onExpand = (index: number, item: InquiryListing) => {
        this.inquiryList.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
        if (this.inquiryList.get()[index].isExpand) {
            this.getInquiryByCustomerId(item.customerId, index)
        }
    }

    onExpandToggle = () => {
        this.inquiryList.update(a => a.map(item => ({ ...item, isExpand: !item.isExpand })))
    }

    expandCollapseIdsSave = (isInquiry: boolean, array: InquiryListing[] | SODraftPage[]) => {

        const id = isInquiry ? 'customerId' : 'id'
        if (array?.length === 0) {
            return;
        }

        const parentIds: number[] = [];
        for (const item of array) {
            if (item.isExpand && !parentIds.includes(item[id])) {
                parentIds.push(item[id]);
            }
        }
        this.isExpandedIDs = { parentIds };
    }

    addPageSizeData(event: any) {
        this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
        this.getCustomerWiseListing();
    }

    pageNumber(event: any) {
        this.paginationRequest.update(a => ({ ...a, pageNo: event }));
        this.getCustomerWiseListing();
    }

    onUpdateSalesManagerPrice = (item: InquiryListingItem, parentIndex: number, childIndex: number, event: any, isCopied: boolean) => {

        let value = isCopied ? +item.customerRequestedPrice : event?.target?.value;
        this.updateCommonSalesPrice(parentIndex, childIndex, value)
    }

    updateCommonSalesPrice = (parentIndex: number, childIndex: number, value: number) => {
        this.inquiryList.updateAt(parentIndex, a => ({ ...a, items: a.items.map((child, j) => j === childIndex ? { ...child, salesManagerPrice: value } : child) }))
    }

    onUpdateFollowUpDateTime = (item: InquiryListingItem, parentIndex: number, childIndex: number, event: any) => {
        this.inquiryList.updateAt(parentIndex, a => ({ ...a, items: a.items.map((child, j) => j === childIndex ? { ...child, followUpDateTime: event } : child) }))
    }

    onSubmitResetSalesPriceChanges = (isReset: boolean) => {

        if (isReset) {
            this.inquiryList.update(a => a.map(item => item.isExpand ? ({ ...item, items: item.items.map(child => ({ ...child, salesManagerPrice: null, followUpDateTime: null })) }) : item))
            return;
        }

        const API = `${this.utilsService.serverVariableService.INQUIRY_UPDATE_SALES_MANAGER_PRICE}`;

        let param = []
        for (const item of this.inquiryList.get()) {
            for (const child of item.items) {
                const obj = {
                    inquiryItemId: child.id,
                    salesManagerPrice: child.salesManagerPrice > 0 ? +child.salesManagerPrice : null,
                    followUpDateTime: (child.followUpDateTime ? moment(child.followUpDateTime).format('YYYY-MM-DD HH:mm:00') : null)
                }
                if (item.isExpand) {
                    param.push(obj)
                }
            }
        }
        this.utilsService.post(API, param, { toast: true }).pipe(takeUntil(this.destroy$)).subscribe()
    }

    openDeleteSalesInqModal = (item: InquiryListing, content: TemplateRef<any>) => {
        this.inquiryListObj.set(item);
        this.modalService.open(content);
    }

    onDeleteSalesInq = (modal: NgbModalRef) => {
        const customerId = this.inquiryListObj.get().customerId;
        this.utilsService.delete(`${this.utilsService.serverVariableService.INQUIRY_SALES_DELETE}?customerId=${customerId}`, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getCustomerWiseListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Export Excel
    exportReport = () => {
        const { searchText, fromDate, toDate, inquiryType, customerId } = this.paginationRequest.get()
        const param = { searchText, fromDate, toDate, inquiryType, customerId }

        let API = null;
        let name = null;
        switch (this.selectedTab()) {
            case this.enumForTabs.INQUIRY:
                API = this.utilsService.serverVariableService.INQUIRY_EXPORT;
                name = 'Inquiry Sheet';
                break;
            case this.enumForTabs.DRAFT:
                API = this.utilsService.serverVariableService.SO_DRAFT_EXPORT;
                name = 'SO Draft Sheet';
                break;
        }

        this.utilsService.exportReport(param, API).subscribe((data: any) => {
            saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), `${name}`);
        });
    }

    getDraftListing = () => {

        this.paginationRequest.update(a => ({ ...a, orderStatus: this.selectedTab() === this.enumForTabs.DRAFT ? this.enumForSalesOrderSaveStatus.DRAFT : this.enumForSalesOrderSaveStatus.CREATED }));

        const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData'])

        this.utilsService.post(this.utilsService.serverVariableService.SO_DRAFT_PAGE, param, null, true).pipe(
            tap((res) => {
                this.pagination = res.pagination;
                this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'] }));
            }),
            map((res) => res?.['content']),
            tap((data: SODraftPage[]) => {
                this.draftList.set(data || []);
                if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs.parentIds) && !this.utilsService.isNullUndefinedOrBlank(data)) {
                    this.draftList.update(list =>
                        list.map(parent => {
                            const isParentExpanded = this.isExpandedIDs.parentIds.includes(parent.id);
                            return {
                                ...parent,
                                isExpand: isParentExpanded,
                            }
                        })
                    );
                }
                const { searchText, fromDate, toDate, customerId, inquiryType } = this.paginationRequest.get();
                const hasFilters = searchText || fromDate || toDate || customerId || inquiryType;

                if (hasFilters && this.draftList.get().length > 0) {
                    this.draftList.update(items => items.map((item) => ({ ...item, isExpand: true })))
                }
                this.draftList.update(items => items.map(item => ({ ...item, config: this.getConfig(item) })))

                localStorage.removeItem('param')
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    getDuration(duration) {
        if (duration > 86400) {
            return 'DDd HH:mm:ss';
        } else  if (duration > 3600) {
            return 'HH:mm:ss';
        } else {
            return 'mm:ss';
        }
    }

    getConfig(item: SODraftPage): CountdownConfig {
        let duration = item.holdDuration;
        return {
            leftTime: duration,
            format: this.getDuration(duration),
            formatDate: ({ date, formatStr }) => {
                let duration = Number(date || 0);
                return CountdownTimeUnits.reduce((current, [name, unit]) => {
                    if (current.indexOf(name) !== -1) {
                        const v = Math.floor(duration / unit);
                        duration -= v * unit;
                        return current.replace(new RegExp(`${name}+`, 'g'), (match: string) => {
                            // When days is empty
                            if (name === 'D' && v <= 0) {
                                return '';
                            }
                            return v.toString().padStart(match.length, '0');
                        });
                    }
                    return current;
                }, formatStr);
            },
        };
    }

    draftExpand = (index: number) => {
        this.draftList.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
    }

    handleEvent = (event: any, index: number) => {
        if (event.action === 'done' && this.draftList.get()[index].holdDuration) {
            setTimeout(() => {
                this.getDraftListing();
            }, 1000);
        }
    }

    // Delete 
    openDeleteSOModal = (item: SODraftPage, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.modalService.open(content);
    }

    onDeleteSO = (modal: NgbModalRef) => {
        const id = this.draftListObj.get().id;
        this.utilsService.delete(`${this.utilsService.serverVariableService.SO_SALES_ORDER_SAVE}${id}`, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    redirectTo = (id?: number, isMoveToSO = false): void => {
        const { pageNo, pageSize, searchText, fromDate, toDate } = this.paginationRequest.get();

        const param = {
            pageNo, pageSize, searchText, fromDate, toDate, selectedTab: this.selectedTab(), pageName: 'SO'
        }

        localStorage.setItem('param', JSON.stringify(param));

        const currentTab = this.selectedTab();
        const moveToSoRoute = id && isMoveToSO ? `/users/sales/sales-orders/move-to-so-created/${id}` : `/users/sales/sales-orders/edit-draft/${id}`

        const routes = {
            withId: {
                [this.enumForTabs.DRAFT]: moveToSoRoute,
                [this.enumForTabs.SO_CREATED]: `/users/sales/sales-orders/edit-sales-order/${id}`
            },
            withoutId: {
                [this.enumForTabs.DRAFT]: '/users/sales/sales-orders/new-sales-order',
                [this.enumForTabs.SO_CREATED]: '/users/sales/sales-orders/new-sales-order',
                [this.enumForTabs.INQUIRY]: '/users/sales/sales-orders/new-inquiry'
            }
        };

        const routeCategory = id ? 'withId' : 'withoutId';
        const targetRoute = routes[routeCategory][currentTab];

        if (targetRoute) {
            this.utilsService.redirectTo(targetRoute);
        }
    }

    //Release Hold
    openReleaseHoldModal = (item: SODraftPage, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.noteControl.reset();
        this.modalService.open(content, { windowClass: 'modal-lg' });
    }

    onReleaseHold = (modal: NgbModalRef) => {
        const id = this.draftListObj.get().id;
        const notes = this.noteControl.value;

        const param = { id, notes }

        this.utilsService.post(`${this.utilsService.serverVariableService.SO_HOLD_RELEASE}`, param, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    //Extend Hold
    openExtendHoldModal = (item: SODraftPage, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.extendForm.reset();
        this.modalService.open(content, { windowClass: 'modal-lg' });
    }

    onExtendHold = (modal: NgbModalRef) => {
        const id = this.draftListObj.get().id;
        const holdMins = this.extendForm.get('time')?.value as number;
        const holdReason = this.extendForm.get('note')?.value as string;

        const param = { id, holdMins, holdReason }

        this.utilsService.post(`${this.utilsService.serverVariableService.SO_EXTEND_HOLD}`, param, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    onOpenQty = (child: SalesOrderItemDraftListing) => {
        const onlyUpcoming = child.markaQtyOutput.find(a => a.branchName === UPCOMING_SO_STOCK)
        if (child.markaQtyOutput?.length === 1 && onlyUpcoming) {
            onlyUpcoming.branchName === UPCOMING_SO_STOCK ? this.isUpcomingTab = true : this.isUpcomingTab = false
        }
        this.activeTabQtyTab = 0
    }

    onMoveToSo = (id: number) => {
        this.redirectTo(id, true)
    }

    openItemDeleteModal = (item: SODraftPage, child: SalesOrderItemDraftListing, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.itemObj.set(child);
        this.modalService.open(content);
    }

    onDeleteItem = (modal: NgbModalRef) => {
        const id = this.itemObj.get().id;
        this.utilsService.delete(`${this.utilsService.serverVariableService.SO_DELETE_ITEM_LIST}${id}`, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    openCancelSOModal = (item: SODraftPage, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.modalService.open(content);
    }

    onCancelSO = (modal: NgbModalRef) => {
        const id = this.draftListObj.get().id;
        this.utilsService.put(`${this.utilsService.serverVariableService.SO_CANCEL_SO}${id}`, null, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    openBackToDraftModal = (item: SODraftPage, content: TemplateRef<any>) => {
        this.draftListObj.set(item);
        this.modalService.open(content);
    }

    onBackToDraft = (modal: NgbModalRef) => {
        const id = this.draftListObj.get().id;
        this.utilsService.put(`${this.utilsService.serverVariableService.SO_BACK_TO_DRAFT}${id}`, null, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getDraftListing();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Handle item selection for conversion to SO
    onItemSelection = (item: InquiryListingItem, isSelected: boolean) => {
        console.log('onItemSelection called:', { itemId: item.id, isSelected, hasItem: !!item?.item });

        if (item?.item) {
            item.item.isSelected = isSelected;

            if (isSelected) {
                // Add to selected IDs if not already present
                if (!this.selectedInquiryItemIds.includes(item.id)) {
                    this.selectedInquiryItemIds.push(item.id);
                    console.log('Added item ID:', item.id, 'Total selected:', this.selectedInquiryItemIds);
                }
            } else {
                // Remove from selected IDs
                this.selectedInquiryItemIds = this.selectedInquiryItemIds.filter(id => id !== item.id);
                console.log('Removed item ID:', item.id, 'Total selected:', this.selectedInquiryItemIds);
            }

            // Show feedback message
            const count = this.selectedInquiryItemIds.length;
            console.log(`${count} item(s) selected for conversion to Sales Order`);
        } else {
            console.warn('Item does not have item property:', item);
        }
    }

    // Get selected inquiry item IDs
    getSelectedInquiryItemIds = (): number[] => {
        return this.selectedInquiryItemIds;
    }

    // Get count of selected inquiry items
    getSelectedInquiryItemsCount = (): number => {
        return this.selectedInquiryItemIds.length;
    }

    // Check if there are selected IDs for conversion
    hasSelectedInquiryItems = (): boolean => {
        // Check service property first, then sessionStorage as fallback
        if (this.convertToSOIds && this.convertToSOIds.length > 0) {
            return true;
        }

        // Check sessionStorage as fallback
        const storedIds = sessionStorage.getItem('selectedInquiryItemIds');
        if (storedIds) {
            try {
                const ids = JSON.parse(storedIds);
                return Array.isArray(ids) && ids.length > 0;
            } catch (e) {
                console.error('Error parsing stored inquiry IDs:', e);
            }
        }

        return false;
    }

    // Get selected IDs for conversion (used by new-sales-order component)
    getConvertToSOIds = (): number[] => {
        // Return service property if available
        if (this.convertToSOIds && this.convertToSOIds.length > 0) {
            return this.convertToSOIds;
        }

        // Check sessionStorage as fallback
        const storedIds = sessionStorage.getItem('selectedInquiryItemIds');
        if (storedIds) {
            try {
                const ids = JSON.parse(storedIds);
                if (Array.isArray(ids)) {
                    // Restore to service property
                    this.convertToSOIds = ids;
                    console.log('Restored IDs from sessionStorage:', ids);
                    return ids;
                }
            } catch (e) {
                console.error('Error parsing stored inquiry IDs:', e);
            }
        }

        return [];
    }

    // Debug method to test selection
    debugSelection = () => {
        console.log('=== DEBUG SELECTION ===');
        console.log('selectedInquiryItemIds:', this.selectedInquiryItemIds);
        console.log('convertToSOIds:', this.convertToSOIds);
        console.log('inquiryList:', this.inquiryList.get());

        // Check if any items have isSelected = true
        const allItems = this.inquiryList.get().flatMap(customer => customer.items || []);
        const selectedItems = allItems.filter(item => item?.item?.isSelected);
        console.log('Items with isSelected=true:', selectedItems);
        console.log('======================');
    }

    // Clear selected inquiry item IDs
    clearSelectedInquiryItemIds = () => {
        this.selectedInquiryItemIds = [];
        this.convertToSOIds = [];

        // Clear sessionStorage
        sessionStorage.removeItem('selectedInquiryItemIds');
        console.log('Cleared selected inquiry item IDs and sessionStorage');

        // Also clear the selection state in the UI
        this.inquiryList.update(list =>
            list.map(customer => ({
                ...customer,
                items: customer.items.map(item => ({
                    ...item,
                    item: item.item ? { ...item.item, isSelected: false } : item.item
                }))
            }))
        );
    }

    onCovertInquiryToSo = (modal: NgbModalRef) => {
        const customerId = this.inquiryListObj.get().customerId;
        console.log('Converting inquiry to SO for customer:', customerId);

        // Debug the current state
        this.debugSelection();

        this.modalService.close(modal);

        // Store selected inquiry item IDs in the service for the new sales order component
        this.convertToSOIds = this.getSelectedInquiryItemIds();
        console.warn("Selected IDs for conversion:", this.convertToSOIds);

        // Also store in sessionStorage to persist across navigation
        if (this.convertToSOIds.length > 0) {
            sessionStorage.setItem('selectedInquiryItemIds', JSON.stringify(this.convertToSOIds));
            console.log('Stored in sessionStorage:', this.convertToSOIds);
        }

        if (this.convertToSOIds.length === 0) {
            console.error('No items selected for conversion! Please select items first.');
            // Don't return, let's still navigate to see what happens
        }

        // Navigate to new sales order with customer ID as route parameter
        // The selected IDs are already stored in the service and will be accessed by the new component
        this.utilsService.redirectTo(`/users/sales/sales-orders/new-sales-order/inquiry-conversion/customer/${customerId}`);
    }
}


const CountdownTimeUnits: [string, number][] = [
  ['Y', 1000 * 60 * 60 * 24 * 365], // years
  ['M', 1000 * 60 * 60 * 24 * 30], // months
  ['D', 1000 * 60 * 60 * 24], // days
  ['H', 1000 * 60 * 60], // hours
  ['m', 1000 * 60], // minutes
  ['s', 1000], // seconds
  ['S', 1], // million seconds
];
<div class="page-content page-content-with-tabs" [pageAccess]="{page: auditService.utilsService.enumForPage.AUDIT_TICKET, action: auditService.utilsService.enumForPage.VIEW_AUDIT_TICKET, view: true}">
    <div class="page-title-wrapper page-title-with-tab">
        <div class="page-title-left">
            <h4>Inventory Tickets</h4>
        </div>
        <div class="page-title-right">
            <button
                [pageAccess]="{page: auditService.utilsService.enumForPage.AUDIT_TICKET, action: auditService.utilsService.enumForPage.ADD_AUDIT_TICKET}"
                class="btn btn-sm btn-primary btn-icon-text" [routerLink]="['/users/audit-tickets/new-audit-tickets']">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="auditService.onRefresh()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class='nav-tabs-outer nav-new nav-tabs-style2'>
            <ul ngbNav #nav="ngbNav" class="nav-tabs" [destroyOnHide]="true" [activeId]="auditService.selectedTab()"
                (activeIdChange)="auditService.onChangeTab($event)">
                <li [ngbNavItem]="auditService.enumForTabs.NEW">
                    <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-document-text"></i>New
                    </button>
                    <ng-template ngbNavContent>
                        <app-audit-ticket-new />
                    </ng-template>
                </li>
                <li [ngbNavItem]="auditService.enumForTabs.RESOLVED">
                    <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-tick-circle"></i>Resolved
                    </button>
                    <ng-template ngbNavContent>
                        <app-audit-ticket-new />
                    </ng-template>
                </li>
                <li [ngbNavItem]="auditService.enumForTabs.DELETED" *ngIf="false">
                    <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-trash"></i>Deleted
                    </button>
                    <ng-template ngbNavContent>
                        <app-audit-ticket-new />
                    </ng-template>
                </li>
            </ul>
            <div [ngbNavOutlet]="nav" class="tab-content pt-0"></div>
        </div>
    </div>
</div>


<!-- ----------------------------------------------------------------------- -->
<!--                 Create Audit Ticket Forms Modal Start                 -->
<!-- ----------------------------------------------------------------------- -->
<div *ngIf="false" class="modal modal-theme fade" id="auditTicketModal" tabindex="-1" aria-labelledby="auditTicketModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditTicketModalLabel">Create Audit Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Ticket Subject</label>
                            <ng-select placeholder="Ticket Subject" [multiple]="false" [clearable]="false"
                                [items]="[]" bindLabel="name" bindValue="id">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect">
                            <label class="form-label">Assign to</label>
                            <ng-select placeholder="Assign to" [multiple]="false" [clearable]="false" [items]="[]"
                                bindLabel="name" bindValue="id">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Date</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-icon-end">
                                    <i class="th th-outline-calendar"></i>
                                    <input type="text" class="form-control" placeholder="Select Date">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Note</label>
                            <textarea class="form-control" placeholder="Enter Note"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                  Create Audit Ticket Forms Modal End                  -->
<!-- ----------------------------------------------------------------------- -->
<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>QC Checklist Master</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-primary btn-icon-text"
                [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.ADD_MASTER}"
                (click)="openAddEditModal(null, 'Add')">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="getQCList()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <!-- ---------------------------- content-area ----------------------------- -->
    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (input)="onSearch($event)" [(ngModel)]="searchText" type="search" class="form-control"
                            placeholder="Search by name">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="activeFlag" [hideSelected]="false">
                    </ng-select>
                </div>
            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(qcList)" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th *ngFor="let th of qcTH; index as j" [class]="th.class"
                                    [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                                'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                                    (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                    <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                        class="checkbox checkbox-primary checkbox-small">
                                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(qcList)"
                                            (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox"
                                            id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    {{th.displayName}}
                                </th>
                                <!-- <th class="d-flex align-items-center gap-2">
                                    <div class=" checkbox checkbox-primary checkbox-small">
                                        <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    Checklist Name
                                </th>
                                <th>Assigned items</th>
                                <th>Last Updated On</th> -->
                                <th class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    Status</th>
                                <th class="text-center"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of qcList; index as i; trackBy: trackBy">
                                <td class="tbl-user tbl-bold">
                                    <div class="tbl-user-checkbox-srno">
                                        <div class="checkbox checkbox-primary checkbox-small">
                                            <input type="checkbox" id="tbl-checkbox2-{{i}}"
                                                (change)="selectUnselect(item.id, i, item.isSelected)"
                                                [(ngModel)]="item.isSelected" class="material-inputs filled-in" />
                                            <label for="tbl-checkbox2-{{i}}"></label>
                                        </div>
                                        <span>{{item.name}}</span>
                                    </div>
                                </td>
                                <td>{{item.countItem}}</td>
                                <td>{{item.updatedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                <td class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    <div class="switch-box ">
                                        <label class="switch" htmlFor="switch-{{i}}">
                                            <input type="checkbox" id='switch-{{i}}'
                                                (change)="onChangeStatus(item, item.isActive, i)"
                                                [(ngModel)]="item.isActive" />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                </td>
                                <td class="tbl-action"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    <div class="tbl-action-group">
                                        <button class="btn btn-xs btn-light-white btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.EDIT_MASTER}"
                                            (click)="openAddEditModal(item, 'Edit')" ngbTooltip="Edit"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-edit"></i>
                                        </button>
                                        <button *ngIf="!item.isDefault"
                                            class="btn btn-xs btn-light-danger btn-icon"
                                            (click)="openDeleteQCModal(item)" ngbTooltip="Delete" placement="left"
                                            container="body" triggers="hover">
                                            <i class="th th-outline-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(qcList)">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
                [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="qcMasterModal" tabindex="-1" aria-labelledby="qcMasterModalLabel"
    aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered modal-xl modal-dialog-scrollable">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus *ngIf="qcMasterModal">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} QC
                    Checklist</h5>
                <button type="button" class="btn-close" (click)="qcMasterModal.hide()"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row" [formGroup]="qcGroup">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group required">
                            <label class="form-label"> Checklist Name</label>
                            <input id="f" (keyup.enter)="onSaveQC()"
                                [maxlength]="utilsService.validationService.MAX_100" formControlName="qc_name"
                                [(ngModel)]="qcObj.name" type="text" class="form-control"
                                placeholder="Enter Checklist Name">
                            <div class="message error-message"
                                *ngIf="qcGroup.controls['qc_name'].hasError('required') &&  qcGroup.controls['qc_name'].touched">
                                {{utilsService.validationService.CHECKLIST_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!qcGroup.controls['qc_name'].hasError('required') && !qcGroup.controls['qc_name'].valid && qcGroup.controls['qc_name'].touched">
                                {{utilsService.validationService.CHECKLIST_NAME_INVALID}}
                            </div>
                        </div>
                        <div class="form-group d-flex justify-content-between required">
                            <label class="form-label">Status</label>
                            <div class="switch-box">
                                <label class="switch" htmlFor="switch">
                                    <input formControlName="status" type="checkbox" id='switch'
                                        [(ngModel)]="qcObj.isActive" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-tab">
                    <div class='nav-tabs-outer nav-tabs-style2'>
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                <button (click)="onTabChange(enumForTab.CHECKLIST)"
                                    [ngClass]="{'active': selectedTab === enumForTab.CHECKLIST}" class="nav-link"
                                    id="nav-active-tab" data-bs-toggle="tab" data-bs-target="#nav-active" type="button"
                                    role="tab" aria-controls="nav-active" aria-selected="true"> <i
                                        class="th th-outline-task"></i> Checklist</button>
                                <button (click)="onTabChange(enumForTab.A_ITEMS)"
                                    [ngClass]="{'active': selectedTab === enumForTab.A_ITEMS}" class="nav-link"
                                    id="nav-inactive-tab" data-bs-toggle="tab" data-bs-target="#nav-inactive"
                                    type="button" role="tab" aria-controls="nav-inactive" aria-selected="false"><i
                                        class="th th-bold-box"></i> Associate
                                    Items</button>
                            </div>
                        </nav>
                        <div class="tab-content" id="nav-tabContent">
                            <div class="tab-pane fade" id="nav-active" role="tabpanel"
                                [ngClass]="{'show active': selectedTab === enumForTab.CHECKLIST}"
                                aria-labelledby="nav-active-tab">
                                <div class="form-group ">
                                    <label class="form-label"> Check List</label>
                                </div>
                                <div class="card card-theme3" *ngFor="let item of qcObj.checklists; index as i;">
                                    <div class="card-header">
                                        <div class="card-header-left">
                                            <div class="card-icon">
                                                <img src="assets/images/question.svg" alt="valamji">
                                            </div>
                                            <div class="card-header-title">
                                                <h2>Checklist {{i + 1}}</h2>
                                            </div>
                                            <div class="dropdown">
                                                <button type="button" class="btn btn-icon-text btn-outline-white dropdown-toggle dropdown-toggle-qc" data-bs-toggle="dropdown"
                                                    aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}'>
                                                    <i *ngIf="qcObj.checklists[i].icon" class="th"
                                                        [ngClass]="qcObj.checklists[i].icon + ' text-primary'"></i>{{qcObj.checklists[i].selectedField
                                                    ? (qcObj.checklists[i].selectedField | titlecase)
                                                    : 'Select Checklist'}}
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li class="dropdown-header">Checklist Type</li>
                                                    <li *ngFor="let field of fieldDropdown; index as j">
                                                        <a class="dropdown-item"
                                                            (click)="onFieldChange(field.value, i, field.other)">
                                                            <i class="th"
                                                                [ngClass]="field?.other + ' text-primary'"></i>
                                                            {{ field.label | titlecase }}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-header-right">
                                            <div class="button-group">
                                                <button (click)="moveUp(i)" class="btn btn-sm btn-icon btn-trasparent">
                                                    <i class="th th-outline-arrow-up-3" ngbTooltip="Up"
                                                        placement="bottom" container="body"
                                                        triggers="hover"></i></button>
                                                <button (click)="moveDown(i)" class="btn btn-sm btn-icon btn-trasparent"
                                                    ngbTooltip="Down" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-arrow-down"></i></button>
                                            </div>
                                            <div class="button-group">
                                                <button (click)="duplicateChecklist(i)"
                                                    class="btn btn-sm btn-icon btn-trasparent" ngbTooltip="Clone"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-copy"></i></button>
                                                <button (click)="removeChecklist(i)"
                                                    class="btn btn-sm btn-icon btn-trasparent text-danger"
                                                    ngbTooltip="Delete" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash "></i></button>
                                            </div>
                                        </div>
                                    </div>
                                    <ng-container *ngIf="item.isBodyVisible">
                                        <div class="card-body"
                                            *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.INPUT">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group required m-0">
                                                        <label class="form-label"> Input Label</label>
                                                        <input [maxlength]="utilsService.validationService.MAX_200"
                                                            [(ngModel)]="item.label" type="text" class="form-control"
                                                            placeholder="Type here">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body"
                                            *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.RADIO">
                                            <div class="form-group required m-0">
                                                <label class="form-label"> Radio Label</label>
                                                <input [maxlength]="utilsService.validationService.MAX_200"
                                                    [(ngModel)]="item.label" type="text" class="form-control"
                                                    placeholder="Type here">
                                            </div>
                                            <hr>
                                            <div class="drag-control-container" cdkDropList
                                                [cdkDropListData]="item.checkListChildList"
                                                (cdkDropListDropped)="drop($event, i)">
                                                <div class="drag-control-wrapper"
                                                    *ngFor="let child of item.checkListChildList; index as k"
                                                    cdkDragLockAxis="y" cdkDrag cdkDragPreviewContainer="parent"
                                                    cdkDragBoundary=".drag-control-container">
                                                    <div class="drag-control-icon">
                                                        <button cdkDragHandle
                                                            class="btn btn-xs btn-trasparent btn-icon">
                                                            <img src="assets/images/drag.svg" alt="valamji">
                                                        </button>
                                                    </div>
                                                    <div class="form-group">
                                                        <div class=" form-group-icon-start form-group-icon-input">
                                                            <span class="input-icon">
                                                                {{k + 1}}
                                                            </span>
                                                            <input [maxlength]="utilsService.validationService.MAX_200"
                                                                type="text" class="form-control"
                                                                [(ngModel)]="child.value">
                                                        </div>
                                                    </div>
                                                    <div class="drag-control-delete">
                                                        <button (click)="removeOption(i, k)"
                                                            class="btn btn-xs btn-light-danger btn-icon"
                                                            ngbTooltip="Delete" placement="bottom" container="body"
                                                            triggers="hover">
                                                            <i class="th th-outline-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <button (click)="addOption(i)"
                                                    class="btn btn-outline-primary btn-sm">Add Option</button>
                                            </div>
                                        </div>
                                        <div class="card-body"
                                            *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.PHOTOS_VIDEO_UPLOAD">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group required m-0">
                                                        <label class="form-label"> Photo/Video Upload Label</label>
                                                        <input [maxlength]="utilsService.validationService.MAX_200"
                                                            [(ngModel)]="item.label" type="text" class="form-control"
                                                            placeholder="Type here">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body"
                                            *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.DROPDOWN"
                                            cdkDropListGroup cdkScrollable>
                                            <div class="form-group required m-0">
                                                <label class="form-label"> Dropdown Label</label>
                                                <input [maxlength]="utilsService.validationService.MAX_200"
                                                    [(ngModel)]="item.label" type="text" class="form-control"
                                                    placeholder="Type here">
                                            </div>
                                            <hr>
                                            <div class="drag-control-container" cdkDropList
                                                [cdkDropListData]="item.checkListChildList"
                                                (cdkDropListDropped)="drop($event, i)">
                                                <div class="drag-control-wrapper"
                                                    *ngFor="let child of item.checkListChildList; index as k"
                                                    cdkDragLockAxis="y" cdkDrag cdkDragPreviewContainer="parent"
                                                    cdkDragBoundary=".drag-control-container">
                                                    <div class="drag-control-icon">
                                                        <button cdkDragHandle
                                                            class="btn btn-xs btn-trasparent btn-icon">
                                                            <img src="assets/images/drag.svg" alt="valamji">
                                                        </button>
                                                    </div>
                                                    <div class="form-group">
                                                        <div class=" form-group-icon-start form-group-icon-input">
                                                            <span class="input-icon">
                                                                {{k + 1}}
                                                            </span>
                                                            <input type="text" class="form-control"
                                                                [(ngModel)]="child.value"
                                                                [maxlength]="utilsService.validationService.MAX_200">
                                                        </div>
                                                    </div>
                                                    <div class="drag-control-delete">
                                                        <button (click)="removeOption(i, k)"
                                                            class="btn btn-xs btn-light-danger btn-icon"
                                                            ngbTooltip="Delete" placement="bottom" container="body"
                                                            triggers="hover">
                                                            <i class="th th-outline-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <button (click)="addOption(i)"
                                                    class="btn btn-outline-primary btn-sm">Add Option</button>
                                            </div>
                                        </div>
                                        <!-- <div class="card-body"
                                                *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.CHECKBOX">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group required m-0">
                                                            <label class="form-label"> Checkbox Label</label>
                                                            <input [maxlength]="utilsService.validationService.MAX_200" [(ngModel)]="item.label" type="text" class="form-control"
                                                                placeholder="Type here">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div> -->
                                        <div class="card-body"
                                            *ngIf="qcObj.checklists[i].selectedField === enumForFieldType.CHECKBOX">
                                            <div class="form-group required m-0">
                                                <label class="form-label"> Checkbox Label</label>
                                                <input [maxlength]="utilsService.validationService.MAX_200"
                                                    [(ngModel)]="item.label" type="text" class="form-control"
                                                    placeholder="Type here">
                                            </div>
                                            <hr>
                                            <div class="drag-control-container" cdkDropList
                                                [cdkDropListData]="item.checkListChildList"
                                                (cdkDropListDropped)="drop($event, i)">
                                                <div class="drag-control-wrapper"
                                                    *ngFor="let child of item.checkListChildList; index as k"
                                                    cdkDragLockAxis="y" cdkDrag cdkDragPreviewContainer="parent"
                                                    cdkDragBoundary=".drag-control-container">
                                                    <div class="drag-control-icon">
                                                        <button cdkDragHandle
                                                            class="btn btn-xs btn-trasparent btn-icon">
                                                            <img src="assets/images/drag.svg" alt="valamji">
                                                        </button>
                                                    </div>
                                                    <div class="form-group">
                                                        <div class=" form-group-icon-start form-group-icon-input">
                                                            <span class="input-icon">
                                                                {{k + 1}}
                                                            </span>
                                                            <input [maxlength]="utilsService.validationService.MAX_200"
                                                                type="text" class="form-control"
                                                                [(ngModel)]="child.value">
                                                        </div>
                                                    </div>
                                                    <div class="drag-control-delete">
                                                        <button (click)="removeOption(i, k)"
                                                            class="btn btn-xs btn-light-danger btn-icon"
                                                            ngbTooltip="Delete" placement="bottom" container="body"
                                                            triggers="hover">
                                                            <i class="th th-outline-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <button (click)="addOption(i)"
                                                    class="btn btn-outline-primary btn-sm">Add Option</button>
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                                <div class="button">
                                    <button (click)="addChecklist()" class="btn  btn-primary btn-icon-text"> <i
                                            class="th th-outline-tick-circle"></i>
                                        Add New Checklist</button>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="nav-inactive" role="tabpanel"
                                [ngClass]="{'show active': selectedTab === enumForTab.A_ITEMS}"
                                aria-labelledby="nav-inactive-tab">
                                <div class="form-group ">
                                    <label class="form-label">Associate Items</label>
                                </div>
                                <div class="card card-theme  ">
                                    <div class="card-body p-0" [formGroup]="associatedItemGroup">
                                        <div class="table-responsive" formArrayName="items">
                                            <table class="table-theme table-hover table table-bordered ">
                                                <thead class="border-less">
                                                    <tr>
                                                        <th class="d-flex align-items-center gap-2">Item Details</th>
                                                        <th class="w-25">SKU</th>
                                                        <th class="w-25">HSN Code</th>
                                                        <th class="text-center">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr *ngFor="let contact of items.controls; index as i"
                                                        [formGroupName]="i">
                                                        <td class="tbl-user">
                                                            <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less"
                                                                *ngIf="associatedItems[i].isAdd">
                                                                <ng-select class="" appendTo="body"
                                                                    [items]="itemDropdown" bindLabel="displayName"
                                                                    placeholder="Name" [searchFn]="customSearchFn"
                                                                    bindValue="id" [(ngModel)]="associatedItems[i].id"
                                                                    (change)="setValueToAssociateItem(associatedItems[i], i)"
                                                                    formControlName="itemId"
                                                                    [virtualScroll]="true"
                                                                    [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}">
                                                                    <ng-template ng-label-tmp let-item="item">
                                                                        <div class="tbl-user">
                                                                            <div class="tbl-user-checkbox-srno">
                                                                                <div class="tbl-user-wrapper">
                                                                                    <div class="tbl-user-image"
                                                                                        *ngIf="item.image">
                                                                                        <img [src]="item.image ? (utilsService.imgPath + item.image) : ''"
                                                                                            alt="valamji">
                                                                                    </div>
                                                                                    <div class="tbl-user-image"
                                                                                        *ngIf="!item.image">
                                                                                        {{item.displayName?.charAt(0).toUpperCase()}}
                                                                                    </div>
                                                                                    <div class="tbl-user-text-action">
                                                                                        <div class="tbl-user-text">
                                                                                            <p>{{ item.displayName }}
                                                                                            </p>
                                                                                            <span>{{item.skuId}}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </ng-template>
                                                                    <ng-template ng-option-tmp let-item="item">
                                                                        <div class="tbl-user">
                                                                            <div class="tbl-user-checkbox-srno">
                                                                                <div class="tbl-user-wrapper">
                                                                                    <div class="tbl-user-image"
                                                                                        *ngIf="item.image">
                                                                                        <img [src]="item.image ? (utilsService.imgPath + item.image) : ''"
                                                                                            alt="valamji">
                                                                                    </div>
                                                                                    <div class="tbl-user-image"
                                                                                        *ngIf="!item.image">
                                                                                        {{item.displayName?.charAt(0).toUpperCase()}}
                                                                                    </div>
                                                                                    <div class="tbl-user-text-action">
                                                                                        <div class="tbl-user-text">
                                                                                            <p [title]="item.displayName || ''">{{ item.displayName }}
                                                                                            </p>
                                                                                            <span>{{item.skuId}}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </ng-template>
                                                                </ng-select>
                                                            </div>
                                                            <div *ngIf="!associatedItems[i].isAdd">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image"
                                                                            *ngIf="associatedItems[i].image">
                                                                            <img [src]="associatedItems[i].image ? (utilsService.imgPath + associatedItems[i].image) : ''"
                                                                                alt="valamji">
                                                                        </div>
                                                                        <div class="tbl-user-image"
                                                                            *ngIf="!associatedItems[i].image">
                                                                            {{associatedItems[i].displayName?.charAt(0).toUpperCase()}}
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{ associatedItems[i].displayName }}
                                                                                </p>
                                                                                <span>{{associatedItems[i].skuId}}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{associatedItems[i].skuId ? associatedItems[i].skuId :
                                                            '-'}}</td>
                                                        <td>{{associatedItems[i].hsnCode ? associatedItems[i].hsnCode :
                                                            '-'}}</td>
                                                        <td class="tbl-action ">
                                                            <div class="tbl-action-group d-flex justify-content-center">
                                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                                    (click)="openRemoveAssoItemModal(i)"
                                                                    ngbTooltip="Delete" placement="bottom"
                                                                    container="body" triggers="hover">
                                                                    <i class="th th-outline-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr class="tbl-add-new">
                                                        <td colspan="100">
                                                            <button (click)="addRow()"
                                                                class="btn btn-sm btn-link btn-icon-text text-primary">
                                                                <i class="th-bold-add-circle"></i> Add New Row
                                                            </button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="paginationbox">
                                    <app-pagination (pagesizeData)="addPageSizeDataModal($event)"
                                        (pageNumber)="pageNumberModal($event)" [page]="paginationRequest.pageNo"
                                        [pageSize]="paginationRequest.pageSize"
                                        [totalData]="paginationRequest.totalData"></app-pagination>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="onSaveQC()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
                    <button (click)="qcMasterModal.hide()" type="button" class="btn btn-outline-white">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->
<!-- ----------------------------------------------------------------------- -->
<!--                       Associate Item Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="AssociatedeleteModal" tabindex="-1"
    aria-labelledby="AssociatedeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to remove the Associate Item.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="removeAssociateItem()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                    Associate Item Delete Modal                             -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteQcMasterModal" tabindex="-1"
    aria-labelledby="deleteQcMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{qcObj.name}}</b> qc-checklist.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteQC()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="warningMsgModel" tabindex="-1"
    aria-labelledby="deleteQcMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p><b>{{errorMsg}}</b></p>
                        <p>You want to override them?. No, then Delete them manually from list.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">No</button>
                    <button (click)="onSaveQC(true)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->
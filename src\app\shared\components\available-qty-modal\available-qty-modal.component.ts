import { Component, computed, inject, input, output } from '@angular/core';
import { AvailableQtyModalData } from '@modal/SBTBranchStock';
import { UtilsService } from '@service/utils.service';

@Component({
  selector: 'app-available-qty-modal',
  templateUrl: './available-qty-modal.component.html',
  styleUrl: './available-qty-modal.component.scss'
})
export class AvailableQtyModalComponent {

  utilsService = inject(UtilsService);
  value = input.required<number>();
  data = input.required<AvailableQtyModalData[]>();

  grandTotalComputed = computed(() => {
    return {
      cartonQty: this.data().reduce((a, b) => a + b?.cartonQty, 0),
      piecesPerCarton: this.data().reduce((a, b) => a + (b?.piecesPerCarton || 0), 0),
      totalCartonQty: this.data().reduce((a, b) => a + b?.totalCartonQty, 0),
      looseQty: this.data().reduce((a, b) => a + b?.looseQty, 0),
      totalQty: this.data().reduce((a, b) => a + b?.totalQty, 0)
    }
  })

  openModal = output<void>();

  onOpen = () => {
    this.openModal.emit();
  }

}

import { BranchMaster, MarkaStocks } from "./SalesInquiry"

export interface SalesOrder {
    customerId: string,
    countryId: number,
    countryExtensionId: number,
    mobileNo: string
    supplierType: string
    deliveryType: string
    notes: string
    orderStatus: string
    dateTime: string
    expectedDeliveryDate: string
    isIncludeGST: boolean
    isWaitForPayment: boolean
    waitForPaymentOption: string
    paymentDueDate: string
    isNeedRepacking: boolean
    isNeedShrinkPacking: boolean
    repackingAmount: number
    advancePaymentAmount: number
    shippingAddressId: string
    deliveryPersonName: string
    deliveryPersonMobileNo: string
    paymentTermsId: string
    paymentTypeId: string
    customerGSTId: string
    packingTypeMasterId: string
    holdReason: string
    adjustmentAmount: string
    salesOrderItemList: SalesOrderItemList[]
}

export interface SalesOrderItemList {
    itemId: number
    orderUnit: string
    orderQty: number
    totalQty: number
    rate: number
    totalAmount: number
    discount: number
    note: string
    markaWiseQtyList: MarkaWiseQtyList[],
    pcsPerCarton: number,
    hsnCodeMasterId: number,
    hsnCodeId: number,
    //
    hsnDropdown: any[]
    markaData: MarkaStocks[];
    totalMarkaQty: number;
    outOfStock: number;
    saveResMarkaWise: SaveResMarkaWise[]
    itemInfo: {
        displayName: string
        id: number
        image: string
        itemName: string
        skuId: string
    },
    isMarkaSelected: boolean,
    discountPercent: number,
    markaQtyOutput: SaveResMarkaWise[]
    saveResUpcomingQty: SaveResUpcomingQty[]
    upComingQtyList: SaveResUpcomingQty[]
    upcomingQtyTotal: number
    branchMaster: BranchMaster
    isUpcomingSaved: boolean
    isMarkaSaved: boolean
}

export interface SaveResUpcomingQty {
    marka: string
    qty: number
    id: number
    poImportItemId?: number
    importStatus?: string
}

export interface MarkaWiseQtyList {
    marka: string
    warehouseId: number
    qty: number
    branchId?: number
}

export interface SaveResMarkaWise {
    branchId: number
    branchName: string
    markaStocks: MarkaResStocks[]
    itemIndex: number
    totalAvailableQty: number
    availableCartonsQty: number
}

export interface MarkaResStocks {
    marka: string
    pcsPerCarton: number
    totalCartonQty: number
    totalCartons: number
    totalLooseQty: number
    totalQty: number
    expectedDeliveryDate?: string
    warehouseStocks: {
        warehouseName: string
        warehouseId: number
        pcsPerCarton: number
        totalCartonQty: number
        totalCartons: number
        totalLooseQty: number
        totalQty: number
    }[]
}
import { Component, OnInit, input } from '@angular/core';
import { LastSalesInfo } from '@modal/SalesInquiry';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-last-three',
  templateUrl: './last-three.component.html',
  styleUrls: ['./last-three.component.css']
})
export class LastThreeComponent implements OnInit {

  lastThreeInfo = input.required<LastSalesInfo[]>()
  dropdown = input.required<NgbDropdown>()
  
  constructor() { }

  ngOnInit() {
  }

}

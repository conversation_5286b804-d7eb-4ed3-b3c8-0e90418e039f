export interface StockWarehouseApproveSave {
    id: number,
    note: string,
    pickupPersonId: number,
    tempoNo: string,
    contactCountryExtensionId: number,
    mobileNo: string,
    associateItemReqList: SWTItemApproveSave[],
    expectedDate: string
    createdDate: string
    reasonMasterId: number;
    warehouseFromId: number;
    warehouseToId: number;
}

export interface SWTItemApproveSave {
    id: number,
    itemType: string,
    marka: string,
    reqQty: number,
    reqPcsPerCarton: number,
    reqTotalPcs: number,
    itemId: number,
    itemName: string,
    skuId: string,
    originalName: string,
    formattedName: string,
    branchTransferAlert: number,
    averagePrice: number,
    currentStock: number
    mainWarehouseAlert: number
    isDataLoaded: boolean
    sendTotalPcs: number
    sendPcsPerCarton: number
    sendQty: number
    dropLocationId: number
    movementTicket: unknown
    importPurchaseQty: number
    importPurchaseDays: number
    importPurchaseDate: string
    importPurchaseStatus: string
}

export interface SWTMarkaDataApprove {
    marka: string;
    age: number;
    totalLooseQty: number;
    totalCartonQty: number;
    itemName: string
    itemType: string
    skuId: string
    formattedName: string,
    reqQty: number,
    reqPcsPerCarton: number,
    reqTotalPcs: number,
    locations: {
        locationName: string;
        locationType: string;
        looseQty: number;
        cartonQty: number;
        age: number;
        piecesPerCarton: number;
        locationTypeId?: number;
        cartonField?: number;
        looseField?: number
        reqQty?: number
    }[];
}

export interface SWTNonMarkaData {
    itemId: number;
    locations: {
        marka?: string;
        locationType: string;
        locationTypeId: number;
        locationName: string;
        warehouseId: number;
        warehouseName: string;
        looseQty: number;
        cartonQty: number;
        age?: number;
        piecesPerCarton?: number
        cartonField?: number
        looseField?: number
        totalPcs?: number
    }[];
    totalLooseQty: number;
    itemName: string
    itemType: string
    skuId: string
    reqQty: number,
    reqPcsPerCarton: number,
    reqTotalPcs: number,
    totalCartonQty: number;
    formattedName: string
    marka?: string
}

export interface AssoItemMovementTicket {
    qtyCarton: number,
    qtyLoose: number,
    marka: string,
    fromLocationId: number,
    fromLocationType: string,
    toLocationId: number,
    toLocationType: string,
    piecesPerCarton: number,
    itemId: number
}
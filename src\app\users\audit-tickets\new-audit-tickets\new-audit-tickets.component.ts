import { Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild, inject, signal } from '@angular/core';
import { FormControlsOf, createArraySignal } from '@libs';
import { ATDropdown, ATItemDropdown, AuditAttachments, AuditTicketItemReq, AuditTicketLocReq, AuditTicketsSave } from '@modal/AuditTicket';
import { UtilsService } from '@service/utils.service';
import { concatMap, merge, of, Subject, takeUntil, tap } from 'rxjs';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { NgbModalService } from '@service/ngb-modal.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Serialize } from 'cerialize';
import { ActivatedRoute } from '@angular/router';
import { AvailableQtyModalData } from '@modal/SBTBranchStock';

const AisleRack = {
  A: "A",
  R: "R"
} as const

// types for formgroup using interfaces
type AuditForm = FormControlsOf<AuditTicketsSave>
type AuditTicketItemReqListFormControls = FormControlsOf<AuditTicketItemReq>;
type AuditTicketLocReqListFormControls = FormControlsOf<AuditTicketLocReq>;

@Component({
  selector: 'app-new-audit-tickets',
  templateUrl: './new-audit-tickets.component.html',
  styleUrls: ['./new-audit-tickets.component.scss']
})
export class NewAuditTicketsComponent implements OnInit, OnDestroy {

  @ViewChild('itemWiseWarning') itemWiseWarning: TemplateRef<any>;

  utilsService = inject(UtilsService)
  modalService = inject(NgbModalService)

  attachmentsList = createArraySignal<AuditAttachments>([])
  selectedItemIndex = signal<number>(null);
  itemName = signal('');
  locationName = signal('');
  isOpenFromItem = signal<boolean>(false);
  auditTicketId = signal<number>(null);

  dropdown: ATDropdown = {
    branches: [],
    location: [],
    ticketSubject: [],
    warehouse: [],
    filteredWarehouse: [],
    filteredLocation: [],
    users: [],
    items: []
  }

  form: FormGroup<AuditForm>;

  private deleteItemKeys = {
    deleteDocumentIdList: [] as number[],
    deletedOrderItemIdList: [] as number[],
  }
  private destroy$ = new Subject<void>();
  private previousItemWiseValue: boolean = true;

  constructor(private fb: FormBuilder, private route: ActivatedRoute) {
    this.auditTicketId.set(this.route.snapshot.params['id'] ? +this.route.snapshot.params['id'] : null);
  }

  ngOnInit(): void {
    this.initForm()
    this.getRequiredData();

    this.onChangeItemWiseFlag()

    // fill filteredWarehouse from branch 
    this.form.get('branchId')?.valueChanges.pipe(
      tap((value) => {
        this.form.get('warehouseId')?.setValue(null)
        this.dropdown.filteredWarehouse = this.dropdown.warehouse.filter((w) => w.branchId === value) || []
      }),
      takeUntil(this.destroy$)
    ).subscribe()

    // fill location from warehouse
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initForm = () => {
    this.form = this.fb.group<AuditForm>({
      ticketsId: this.fb.control(null),
      id: this.fb.control(null),
      ticketSubjectId: this.fb.control(null, Validators.compose([Validators.required])),
      branchId: this.fb.control(null, Validators.compose([Validators.required])),
      warehouseId: this.fb.control(null, Validators.compose([Validators.required])),
      ticketNote: this.fb.control(null),
      isItemWise: this.fb.control(null),
      isRequiredImage: this.fb.control(null),
      userId: this.fb.control(null),
      auditTicketItemReqList: this.fb.array<FormGroup<AuditTicketItemReqListFormControls>>([]),
      auditTicketLocReqList: this.fb.array<FormGroup<AuditTicketLocReqListFormControls>>([]),
    })

    this.form.patchValue({
      isItemWise: true,
      isRequiredImage: false,
      id: this.auditTicketId(),
    })

    this.addItems()
  }

  get items(): FormArray<FormGroup<AuditTicketItemReqListFormControls>> {
    return this.form.get('auditTicketItemReqList') as FormArray<FormGroup<AuditTicketItemReqListFormControls>>;
  }

  get locations(): FormArray<FormGroup<AuditTicketLocReqListFormControls>> {
    return this.form.get('auditTicketLocReqList') as FormArray<FormGroup<AuditTicketLocReqListFormControls>>;
  }

  addLocations = () => {
    this.locations.push(this.fb.group({
      location: this.fb.control(null, [Validators.required]),
      // rackId: this.fb.control(null),
      value: this.fb.control(null),
      locationId: this.fb.control(null),
      locationType: this.fb.control(null),
    }) as FormGroup<AuditTicketLocReqListFormControls>)
  }

  addItems = () => {
    this.items.push(this.fb.group({
      itemId: this.fb.control(null, [Validators.required]),
      availableTotalCartons: this.fb.control(null),
      availableTotalPieces: this.fb.control(null),
      allLocation: this.fb.control(null),
      availableTotalLooseQty: this.fb.control(null),
      availableTotalQty: this.fb.control(null),
      branchStockList: this.fb.control(null),
    }) as FormGroup<AuditTicketItemReqListFormControls>)
  }

  openRemoveAuditTicketItemModal = (index: number, content: TemplateRef<any>, isOpenFromItem: boolean) => {
    const itemName = this.dropdown.items.find((a) => a.id === this.items.at(index)?.get('itemId')?.value)?.displayName;
    const locationName = this.locations.at(index)?.get('location')?.value;
    this.isOpenFromItem.set(isOpenFromItem);
    if (isOpenFromItem) {
      this.itemName.set(itemName);
    }
    if (!isOpenFromItem) {
      this.locationName.set(locationName);
    }
    this.selectedItemIndex.set(index);
    this.modalService.open(content);
  }

  removeAuditTicketItem = (modal: NgbModalRef) => {
    if (this.isOpenFromItem()) {
      this.items.removeAt(this.selectedItemIndex());
    } else {
      this.locations.removeAt(this.selectedItemIndex());
    }
    this.modalService.close(modal);
  }

  // Required data followed by get data by id (if present)
  getRequiredData = () => {

    const param = {
      isDropLocationData: false,
      isDropFlag: true,
      ticketId: this.auditTicketId()
    }

    this.utilsService.post(this.utilsService.serverVariableService.REQUIRED_DATA_AUDIT_TICKET, param, null, true).pipe(
      tap((res) => {
        this.dropdown['branches'] = res['branches']
         Object.keys(res).filter(v => v !== 'items' && v !== 'branches').forEach(key => {
          this.dropdown[key] = res[key];
          if (this.dropdown[key] && Array.isArray(this.dropdown[key])) {
            this.dropdown[key] = this.utilsService.transformDropdownItems(this.dropdown[key]);
          }
        });
      }),
      concatMap(() => this.auditTicketId() ? this.getDataById() : of(null)),
      tap(() => {
        this.getDropdownActiveInactiveCheck();
        if (!this.auditTicketId()) {
          this.form.patchValue({
            branchId: this.dropdown?.branches?.find(a => a.id === this.utilsService?.defaultBranch?.id)?.id || null
          })
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getDataById = () => {

    const API = `${this.utilsService.serverVariableService.GET_AUDIT_TICKET_DATA}?id=${this.auditTicketId()}`;

    return this.utilsService.get(API, null, true).pipe(
      tap((res) => {
        this.onChangeWarehouse(res)
      }),
      takeUntil(this.destroy$)
    )
  }

  onSelectAttachments = (event: any): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        const allowedExts = ['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'];

        if (allowedExts.includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if ((this.attachmentsList.get() || []).length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }
            this.attachmentsList.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_INVALID_EXTENSION);
        }
      });
    }
  }

  openLink = (link: string, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment = (i: number, file: AuditAttachments) => {
    if (file.id) {
      this.deleteItemKeys.deleteDocumentIdList.push(file.id)
    }
    this.attachmentsList.removeAt(i)
  }

  customSearchFn = (term: string, item: ATItemDropdown) => {
    const lowerCaseTerm = term.toLocaleLowerCase();
    return item?.skuId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
  }

  /// On item Selection

  onItemChange = (selectedItem: ATItemDropdown, index: number) => {
    this.items.at(index)?.patchValue({
      availableTotalCartons: selectedItem.availableTotalCartons || 0,
      availableTotalPieces: selectedItem.availableTotalPieces || 0,
      availableTotalLooseQty: selectedItem.availableTotalLooseQty || 0,
      availableTotalQty: (+selectedItem.availableTotalPieces || 0) + (+selectedItem.availableTotalLooseQty || 0),
      allLocation: selectedItem.allLocation
    })
  }

  onChangeWarehouse = (setData?) => {
    const param = {
      isDropLocationData: false,
      isDropFlag: true,
      warehouseId:  this.form.get('warehouseId')?.value || setData['warehouseId']
    }
    this.utilsService.post(this.utilsService.serverVariableService.REQUIRED_DATA_AUDIT_TICKET, param, null, true).pipe(
      tap((res) => {
        this.dropdown.items = res['items'];
        this.dropdown.location = res['location'];
        setTimeout(() => {
          console.error(this.dropdown);
          if (setData) {
            this.setObjData(setData);
          }
        });
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onChangeItemWiseFlag = () => {
    merge(
      this.form.get('isItemWise').valueChanges,
      this.form.get('warehouseId').valueChanges
    ).pipe(
      tap((value) => {
        let itemWiseValue = this.form.get('isItemWise')?.value;
        const hasExistingData = this.items.value.some(item => item.itemId) || this.locations.value.some(location => location.location);

        if (!hasExistingData) {
          this.locations.clear();
          this.items.clear();
          if (value) {
            this.addItems();
          } else {
            this.addLocations();
          }
          this.previousItemWiseValue = itemWiseValue;
          return;
        }

        this.form.get('isItemWise')?.setValue(this.previousItemWiseValue, { emitEvent: false });
        const modalRef = this.modalService.open(this.itemWiseWarning);

        modalRef.result.then(
          () => {
            this.form.get('isItemWise')?.setValue(itemWiseValue, { emitEvent: false });
            this.locations.clear();
            this.items.clear();
            if (value) {
              this.addItems();
            } else {
              this.addLocations();
            }
            this.previousItemWiseValue = itemWiseValue;
          },
          () => {
            this.form.get('isItemWise')?.setValue(this.previousItemWiseValue, { emitEvent: false });
          }
        );
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onLocationChange = (event: any, index: number) => {
    console.error(event);
    this.locations.at(index)?.patchValue({
      // rackId: event.other_2 === AisleRack.R ? event.value : null,
      // aisleId: event.other_2 === AisleRack.A ? event.value : null,
      locationType: event.other,
      locationId: event.value
    })
  }

  // Helper function to get all validation errors from a FormGroup
getAllErrors(control: AbstractControl): { [key: string]: ValidationErrors } | null {
  if (control.valid) {
    return null;
  }

  const errors: { [key: string]: ValidationErrors } = {};
  if (control instanceof FormGroup) {
    Object.keys(control.controls).forEach(key => {
      const controlErrors = this.getAllErrors(control.get(key)!);
      if (controlErrors) {
        Object.assign(errors, controlErrors);
      }
    });
  }
  // Add control-specific errors
  if (control.errors) {
    errors[control.value] = control.errors; // This part might need adjustment based on what you need to log/store
    // More typically you'd want to associate errors with the control name for a form
  }
  return errors;
}

// Helper function to mark all controls as touched (useful on submit)
markAllAsTouched(formGroup: FormGroup) {
  Object.values(formGroup.controls).forEach(control => {
    control.markAsTouched();
    if (control instanceof FormGroup) {
      this.markAllAsTouched(control);
    }
  });
}

  onSave = () => {

    const formData = new FormData();

    if (this.form.invalid) {
      this.markAllAsTouched(this.form); // Optional: mark all fields as touched to display errors immediately

      const errors = this.getAllErrors(this.form);
      console.log('Form Errors:', errors);
      // this.form.markAllAsTouched();
      return
    }

    if ((this.attachmentsList.get() || []).length > 0) {
      for (const file of this.attachmentsList.get() || []) {
        if (file.file) {
          formData.append('documents', file.file);
        }
      }
    }

    const isItemWise = this.form.get('isItemWise')?.value;

    const target = isItemWise ? this.items : this.locations;
    const typeLabel = isItemWise ? 'Item' : 'Location';
    const keyField = isItemWise ? 'itemId' : 'location';
    const uniqueMsg = isItemWise ? this.utilsService.validationService.ITEMS_SHOULD_BE_UNIQUE : this.utilsService.validationService.LOC_UNIQUE;

    // Check empty
    if (this.utilsService.isEmptyObjectOrNullUndefined(target?.length === 0)) {
      this.utilsService.toasterService.error(`Minimum One ${typeLabel} is required.`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if((this.items.value || []).some(v => v.availableTotalQty === 0)) {
      this.utilsService.toasterService.error('Some items are not available.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    // Check unique
    if (!this.utilsService.isEverythingUnique(target.value, keyField)) {
      this.utilsService.toasterService.error(uniqueMsg, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    console.warn(this.form.get('auditTicketItemReqList')?.value);
    const param = {
      ...this.form.value,
      auditTicketItemReq: isItemWise ? this.form.get('auditTicketItemReqList')?.value.map((v) => {
        return { itemId: v.itemId }
      }) : this.form.get('auditTicketLocReqList')?.value.map((v) => {
        return { 
          locationId: v.locationId,
          locationType: v.locationType,
        }
      }),
      assignToId: this.form.get('userId')?.value || null,
      documents: this.deleteItemKeys.deleteDocumentIdList || []
    }
    console.warn(param)

    const finalObj = this.utilsService.removeKeys(param, ['auditTicketItemReqList', 'auditTicketLocReqList']);
    formData.set('info', JSON.stringify(finalObj));

    this.utilsService.post(this.utilsService.serverVariableService.SAVE_AUDIT_TICKET, formData, { toast: true }, true).pipe(
      tap(() => {
        this.utilsService.redirectTo('/users/audit-tickets/')
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // Setting response to form
  setObjData = (res: any) => {
    const obj = Serialize(res) as AuditTicketsSave;

    if(this.utilsService.isEmptyObjectOrNullUndefined(obj)) {
      return;
    }

    if (obj.documents?.length > 0) {
      this.attachmentsList.set(obj.documents);
    }

    this.form.patchValue({
      ...obj,
    })

    const isItemWise = this.form.get('isItemWise').value;
    switch (isItemWise) {
      case true:
        this.items.clear();
        for (let index = 0; index < obj.auditTicketItemReq?.length; index++) {
          const item = obj.auditTicketItemReq[index];
          this.addItems();

          this.items.at(index)?.patchValue({
            ...item,
          })

          const selectedItem = this.dropdown.items.find((v) => v.id === item.itemId);
          this.onItemChange(selectedItem, index);
        }
        break;
      case false:
        this.locations.clear();
        for (let index = 0; index < obj['location']?.length; index++) {
          const location = obj['location'][index];
          console.warn(location);
          this.addLocations();

          this.locations.at(index)?.patchValue({
            location: location.value,
            locationId: location.value,
            locationType: location.locationType,
          })
        }
        break;
    }
  }

  getDropdownActiveInactiveCheck = () => {
    // this.dropdown.branches = this.utilsService.filterIsActive(this.dropdown?.branches, this.form.get('branchId')?.value ?? null);
    this.dropdown.warehouse = this.utilsService.filterIsActive(this.dropdown?.warehouse, this.form.get('warehouseId')?.value ?? null);
    this.dropdown.users = this.utilsService.filterIsActiveLV(this.dropdown?.users, this.form.get('userId')?.value ?? null);
    this.dropdown.ticketSubject = this.utilsService.filterIsActiveLV(this.dropdown?.ticketSubject, this.form.get('ticketSubjectId')?.value ?? null);
  }

  getAvailableQtyCommonAPI = (itemId: number, index: number) => {

    const param = {
      itemId: itemId,
    }

    const mapAvailableQtyModalData = (res: AvailableQtyModalData[]) => {
      return res.map(a => ({
        ...a,
        looseQty: a.looseQty || 0,
        totalCartonQty: (a.cartonQty || 0) * (a.piecesPerCarton || 0),
        totalQty: ((a.cartonQty || 0) * (a.piecesPerCarton || 0)) + (a.looseQty || 0)
      }));
    };


    this.utilsService.post(this.utilsService.serverVariableService.AVAILABLE_QTY_OF_ITEM_WISE, param, null, true).pipe(
      tap((res: AvailableQtyModalData[]) => {
        this.items.at(index).get('branchStockList').setValue(mapAvailableQtyModalData(res));
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }
}

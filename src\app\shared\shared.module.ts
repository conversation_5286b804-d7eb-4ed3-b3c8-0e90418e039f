import { A11yModule } from "@angular/cdk/a11y";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { ScrollingModule } from "@angular/cdk/scrolling";
import { CommonModule, NgOptimizedImage } from "@angular/common";
import { NgModule, ModuleWithProviders } from "@angular/core";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { NavbarComponent, NotFoundComponent, SessionExpiredComponent, MarkaImagePreviewComponent, UnderMaintenanceComponent, NoRecordComponent, SidebarComponent, PaginationComponent, ItemGrpMasterComponent, DateRangeSelectionComponent, HsnMasterComponent, UnitMasterComponent, CategoryModalComponent, TableColumnFilterDropdownComponent, TableColumnFilterDropdownComponentNew, AttachmentDownloadDropdownComponent, ImageViewModalComponent, AccessDeniedComponent, ControlDemoComponent, CustomerLeadModalComponent } from "@common";
import { DateTimePickerComponent } from "@common/date-time-picker/date-time-picker.component";
import { MarkaUpcomingDropdownComponent } from "@common/marka-upcoming-dropdown/marka-upcoming-dropdown.component";
import { QtySalesModalComponent } from "@common/qty-sales-modal/qty-sales-modal.component";
import { LastThreeComponent } from "@common/sales-modals/last-three/last-three.component";
import { OwlDateTimeModule, OwlNativeDateTimeModule } from "@danielmoncada/angular-datetime-picker";
import { NumberOnlyDirective, NumberFloatOnlyDirective, RoleDirective, CopyTextDirective, DisableControlDirective } from "@directives";
import { NgbTooltipModule, NgbDropdownModule, NgbDatepickerModule, NgbTimepicker, NgbCollapseModule, NgbNavModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { UnderscorePipe, AsterickPipe, IndianCurrencyPipe, BranchFilterPipe, FilterShippingTypePipe, PadNumPipe, SafePipe, SafeHtmlPipe } from "@pipes";
import { NgOtpInputModule } from "ng-otp-input";
import { AutosizeModule } from "ngx-autosize";
import { CountdownModule } from "ngx-countdown";
import { NgxDaterangepickerMd } from "ngx-daterangepicker-material";
import { NgxMaskDirective, NgxMaskPipe, provideEnvironmentNgxMask } from "ngx-mask";
import { NgxPrintModule } from "ngx-print";
import { SlickCarouselModule } from "ngx-slick-carousel";
import { WebcamModule } from "ngx-webcam";
import { SimplebarAngularModule } from "simplebar-angular";
import { SwtMarkaModalComponent } from "./components/swt-marka-modal/swt-marka-modal.component";
import { AvailableQtyModalComponent } from "@common/available-qty-modal/available-qty-modal.component";
import { QuillModule } from "ngx-quill";

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    NgSelectModule,
    SimplebarAngularModule,
    NgOtpInputModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbDatepickerModule,
    NgxDaterangepickerMd.forRoot({
      format: 'DD/MM/YYYY',
      firstDay: 1,
    }),
    QuillModule,
    NgxPrintModule,
    SlickCarouselModule,
    DragDropModule,
    A11yModule,
    NgbTimepicker,
    NgxMaskDirective,
    NgxMaskPipe,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    NgOptimizedImage,
    ScrollingModule,
    AutosizeModule,
    NgbCollapseModule,
    NgbNavModule,
    CountdownModule,
    WebcamModule
  ],
  exports: [
    CommonModule,
    ScrollingModule,
    NgbCollapseModule,
    NgbNavModule,
    AutosizeModule,
    NgOptimizedImage,
    ReactiveFormsModule,
    FormsModule,
    NgbDropdownModule,
    NgSelectModule,
    NavbarComponent,
    NotFoundComponent,
    SessionExpiredComponent,
    MarkaImagePreviewComponent,
    UnderMaintenanceComponent,
    NoRecordComponent,
    SidebarComponent,
    PaginationComponent,
    NgOtpInputModule,
    NgxMaskDirective,
    NgxMaskPipe,
    UnderscorePipe,
    NgbTooltipModule,
    OwlDateTimeModule,
    WebcamModule,
    OwlNativeDateTimeModule,
    SlickCarouselModule,
    NgxDaterangepickerMd,
    ItemGrpMasterComponent,
    DateRangeSelectionComponent,
    AsterickPipe,
    IndianCurrencyPipe,
    NgxPrintModule,
    CountdownModule,
    HsnMasterComponent,
    UnitMasterComponent,
    CategoryModalComponent,
    NgbDatepickerModule,
    NumberOnlyDirective,
    NumberFloatOnlyDirective,
    DragDropModule,
    A11yModule,
    NgbTimepicker,
    TableColumnFilterDropdownComponent,
    TableColumnFilterDropdownComponentNew,
    AttachmentDownloadDropdownComponent,
    ImageViewModalComponent,
    RoleDirective,
    BranchFilterPipe,
    CopyTextDirective,
    FilterShippingTypePipe,
    PadNumPipe,
    SafePipe,
    DateTimePickerComponent,
    ControlDemoComponent,
    DisableControlDirective,
    CustomerLeadModalComponent,
    LastThreeComponent,
    SafeHtmlPipe,
    MarkaUpcomingDropdownComponent,
    QtySalesModalComponent,
    SwtMarkaModalComponent,
    AvailableQtyModalComponent,
    QuillModule,
  ],
  declarations: [AccessDeniedComponent, NavbarComponent, NotFoundComponent, SessionExpiredComponent, MarkaImagePreviewComponent, 
    SidebarComponent, 
    PaginationComponent, 
    AsterickPipe, 
    NumberOnlyDirective, 
    NumberFloatOnlyDirective, 
    NumberFloatOnlyDirective, 
    DateRangeSelectionComponent, 
    TableColumnFilterDropdownComponent,
    UnderMaintenanceComponent,
    NoRecordComponent,
    TableColumnFilterDropdownComponentNew,
    ItemGrpMasterComponent,
    AttachmentDownloadDropdownComponent,
    ImageViewModalComponent,
    HsnMasterComponent,
    UnitMasterComponent,
    CategoryModalComponent,
    RoleDirective,
    BranchFilterPipe,
    CopyTextDirective,
    UnderscorePipe,
    FilterShippingTypePipe,
    IndianCurrencyPipe,
    PadNumPipe,
    SafePipe,
    DateTimePickerComponent,
    ControlDemoComponent,
    DisableControlDirective,
    CustomerLeadModalComponent,
    LastThreeComponent,
    SafeHtmlPipe,
    MarkaUpcomingDropdownComponent,
    QtySalesModalComponent,
    SwtMarkaModalComponent,
    AvailableQtyModalComponent
  ]
})

export class SharedModule {

  /*** This static forRoot block (provides and configures services) is
  * used in case of when we want use some services in one or more components.
  */
  static forRoot(): ModuleWithProviders<any> {
    return {
      ngModule: SharedModule,
      providers: [provideEnvironmentNgxMask({ decimalMarker: '.' })]
    };
  }
}


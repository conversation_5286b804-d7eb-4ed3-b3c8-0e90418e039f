import { Component, inject, OnDestroy, OnInit, signal, TemplateRef, ViewChild } from '@angular/core';
import { UtilsService } from '@service/utils.service';
import { Subject, Subscription, takeUntil, tap } from 'rxjs';
import { RxStompService } from '../../socket_config/rx-stomp.service';
import { NgbModalService } from '@service/ngb-modal.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
declare var window: any;

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css']
})
export class NavbarComponent implements OnInit, OnDestroy {

  @ViewChild('extendReqModal') extendReqModal: TemplateRef<any>;
  private modalExtendReq: NgbModalRef;
  
  private modalService = inject(NgbModalService);
  utilsService = inject(UtilsService);
  rxStompService = inject(RxStompService);

  sidebarSize = false;
  isSidebarToggleIcon: boolean = true;

  isFullscreen: boolean = false;
  logoutModal: any;

  socketSub: Subscription;
  profileSub: Subscription;

  extendTimeMessage = signal<string>(null);

  destroy$ = new Subject<void>();
  constructor() { 
    const userData = JSON.parse(localStorage.getItem('userData'));
    this.rxStompService.configure({connectHeaders: { Authorization: 'Bearer ' + userData?.token2 }});
  }

  ngOnInit() {

    this.socketSub = new Subscription();
    this.profileSub = new Subscription();

    this.logoutModal = new window.bootstrap.Modal(
      document.getElementById('logoutModal')
    );


    const userData = JSON.parse(localStorage.getItem('userData'));
    const token = this.utilsService.decodeToken(this.utilsService.getToken())

    if(!this.utilsService.isEmptyObjectOrNullUndefined(userData)) {
      this.utilsService.username = `${token?.user?.name}`;
      this.utilsService.userId = Number(`${token?.user?.id}`);
      this.utilsService.roleName = `${userData?.roleName}`;
      this.utilsService.userProfilePicture = userData?.profileImageUrl ? `${userData?.profileImageUrl}` : null;
      ///
      this.socketSub = this.rxStompService.watch(`/topic/updateStatus/` + `${this.utilsService.userId}`).subscribe((message) => {
        const msg = JSON.parse(message.body)
        const toastMsg = msg.label ? 'User Shift has ended.' : 'User configuration changed by system administrator, Please re-login to continue.' 
        if (!this.utilsService.isEmptyObjectOrNullUndefined(msg)) {
          if (msg) {
            this.utilsService.logout(false);
            this.utilsService.toasterService.info(toastMsg, '', {
              positionClass: 'toast-top-right',
              closeButton: true,
              timeOut: 10000
            });
          }
        }
      });

      this.profileSub = this.rxStompService.watch(`/topic/profile/` + `${this.utilsService.userId}`).subscribe((message) => {
        const msg = JSON.parse(message.body)
        if (!this.utilsService.isEmptyObjectOrNullUndefined(msg)) {
          if (msg.profileUrl) {
            this.utilsService.userProfilePicture = msg.profileUrl as string;
          }

          if (msg.id == 0) {
            this.utilsService.userProfilePicture = null;
          }
          let data = JSON.parse(localStorage.getItem('userData'));
          data.profileImageUrl = this.utilsService.userProfilePicture
          this.utilsService.storeDataLocally('userData', JSON.stringify(data));
        }
      })

      this.rxStompService.watch(`/topic/forceLogout/` + `${this.utilsService.userId}`).pipe(
        tap((message) => {
          const msg = message.body
          if (!this.utilsService.isEmptyObjectOrNullUndefined(msg)) {
            if (msg) {
              this.utilsService.logout(false);
              this.utilsService.toasterService.info(msg, '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
            }
          }
        }),
        takeUntil(this.destroy$)
      ).subscribe()

      this.rxStompService.watch(`/topic/logoutAlert/` + `${this.utilsService.userId}`).pipe(
        tap((message) => {
          // if any modal open cloase it
          this.openExtendReqModal(this.extendReqModal, message.body)
        }),
        takeUntil(this.destroy$)
      ).subscribe()
    }
  }

  ngOnDestroy(): void {
    this.socketSub.unsubscribe();
    this.profileSub.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  sidebarToggle() {
    this.isSidebarToggleIcon = !this.isSidebarToggleIcon;

    // Method 1
    this.sidebarSize = !this.sidebarSize;
    if (this.sidebarSize === false) {
      document.documentElement.setAttribute('data-sidebar-size', 'lg');
    } else if (this.sidebarSize === true) {
      document.documentElement.setAttribute('data-sidebar-size', 'sm');
    }
  }

  openLogoutModal() {
    this.logoutModal.show();
  }

  confirmLogout() {
    this.logoutModal.hide();
    this.utilsService.logout(true);
  }
  
  // fullscreen from navbar function
  toggleFullscreen(): void {
    const doc: any = document;
    const elem: any = document.documentElement;

    const isInFullScreen =
      doc.fullscreenElement ||
      doc.webkitFullscreenElement ||
      doc.mozFullScreenElement ||
      doc.msFullscreenElement;

    if (!isInFullScreen) {
      // Enter fullscreen
      switch (true) {
        case !!elem.requestFullscreen:
          elem.requestFullscreen();
          break;
        case !!elem.webkitRequestFullscreen:
          elem.webkitRequestFullscreen();
          break;
        case !!elem.mozRequestFullScreen:
          elem.mozRequestFullScreen();
          break;
        case !!elem.msRequestFullscreen:
          elem.msRequestFullscreen();
          break;
      }
      this.isFullscreen = true;
    } else {
      // Exit fullscreen (call only if active)
      switch (true) {
        case !!doc.exitFullscreen:
          doc.exitFullscreen();
          break;
        case !!doc.webkitExitFullscreen:
          doc.webkitExitFullscreen();
          break;
        case !!doc.mozCancelFullScreen:
          doc.mozCancelFullScreen();
          break;
        case !!doc.msExitFullscreen:
          doc.msExitFullscreen();
          break;
      }
      this.isFullscreen = false;
    }
  }

  // Extend Time Request Modal
  openExtendReqModal(content: TemplateRef<any>, message: string) {
    if (this.modalExtendReq) {
      this.modalExtendReq.close();
    }
    this.modalExtendReq = this.modalService.open(content, { backdrop: 'static', keyboard: false });
    this.extendTimeMessage.set(message);
  }

  onConfirmExtend = (modal: NgbModalRef) => {
    this.utilsService.get(this.utilsService.serverVariableService.USER_EXTEND_TIME, { toast: true }, true).pipe(
      tap(() => {
        this.modalExtendReq.close(modal);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }
}

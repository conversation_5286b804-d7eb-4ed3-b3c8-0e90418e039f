<div class="row">
  <div class="inner-title-wrapper">
    <div class="inner-title-left"></div>
    <div class="inner-title-right" *ngIf="regId && isActive && !regObj.isSaveAsDraft">
      <button [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.ADD_PO}"
        [disabled]="selectedItems.length == 0" (click)="createPoWarning(createToPoWarning)" class="btn btn-primary btn-sm btn-icon-text"> <i
          class="th th-outline-add-circle"></i>
        Create PO
      </button>
    </div>
  </div>
  <div class="table-responsive mb-5 reg-associated-table" [formGroup]="regForm">
    <table class="table-theme table-hover table table-bordered table-sticky" formArrayName="items">
      <thead class="border-less">
        <tr>
          <th>
            <div class="d-flex align-items-center gap-2">
              {{selectedItems | json}}
              {{flagForSelectAllItems}}
              <div class=" checkbox checkbox-primary checkbox-small">
                <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(associatedItems)" type="checkbox"
                  id="tbl-checkbox" class="material-inputs filled-in" (change)="selectAll()"
                  [ngModelOptions]="{standalone: true}" [(ngModel)]="flagForSelectAllItems" />
                <label for="tbl-checkbox"></label>
              </div>
              Item Details
            </div>
          </th>

          <th>Supplier SKU</th>
          <th>HSN Code</th>
          <th>Item Dimension</th>
          <th>Item Weight</th>
          <th>Item Dimension <br/> (With Box)</th>
          <th>Item Weight <br/> (With Box)</th>
          <th>Carton Dimension </th>
          <th>Carton Weight </th>
          <th>CBM</th>
          <th>Qty/Carton</th>
          <th>Price/Item <br /> {{getSupplierType() === 'CHINA' ? '(RMB)' : '(₹)'}}</th>
          <th>Price/Carton <br /> {{getSupplierType() === 'CHINA' ? '(RMB)' : '(₹)'}}</th>
          <th>Enter Tag</th>
          <th>Color</th>
          <th>Packing Type</th>
          <th>English <br/> Comment</th>
          <th>China <br/> Comment</th>
          <th>Measurement <br/> Code </th>
          <th class="text-center">Status</th>
          <th class="text-center">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr class="tbl-add-row" *ngFor="let contact of items.controls; index as i" [formGroupName]="i" [attr.id]="'item-' + i">
          <td class="tbl-user tbl-form-group">
            <div class="tbl-user-checkbox-srno">
              <div class="checkbox checkbox-primary checkbox-small" *ngIf="associatedItems[i].id">
                <input [disabled]="!associatedItems[i].isActive" type="checkbox" id="tbl-checkboxAI-{{i}}"
                  class="material-inputs filled-in" [ngModelOptions]="{standalone: true}"
                  (change)="selectUnselect(associatedItems[i].id, i, associatedItems[i].isSelected)"
                  [(ngModel)]="associatedItems[i].isSelected" />
                <label for="tbl-checkboxAI-{{i}}"></label>
              </div>
              <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100"
                *ngIf="!associatedItems[i].id">
                <ng-select (change)="setValueToAssociateItem(associatedItems[i], i)" [appendTo]="'.theme-ngselect'"
                  class="new-ng-width"
                  [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}"
                  formControlName="itemId" [(ngModel)]="associatedItems[i].itemId"
                  [items]="associatedItems[i].itemDropdown" bindLabel="name" placeholder="Select Item"
                  [searchFn]="customSearchFn" bindValue="id" [virtualScroll]="true">
                  <ng-template ng-label-tmp let-item="item">
                    <div class="tbl-user">
                      <div class="tbl-user-checkbox-srno">
                        <div class="tbl-user-wrapper">
                          <div class="tbl-user-image">
                            <img [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                              alt="valamji">
                          </div>
                          <div class="tbl-user-text-action">
                            <div class="tbl-user-text">
                              <p>{{ item.displayName }} </p>
                              <span>{{item.skuId}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                  <ng-template ng-option-tmp let-item="item">
                    <div class="tbl-user">
                      <div class="tbl-user-checkbox-srno">
                        <div class="tbl-user-wrapper">
                          <div class="tbl-user-image">
                            <img [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                              alt="valamji">
                          </div>
                          <div class="tbl-user-text-action">
                            <div class="tbl-user-text">
                              <p [title]="item.displayName || ''">{{ item.displayName }} </p>
                              <span>{{item.skuId}}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </ng-select>
              </div>
              <div *ngIf="associatedItems[i].id">
                <div class="tbl-user-checkbox-srno">
                  <div class="tbl-user-wrapper new-ng-width justify-content-space-between">
                    <div class="tbl-user-image" *ngIf="associatedItems[i].formattedName">
                      <img
                        [src]="associatedItems[i].formattedName ? (utilsService.imgPath + associatedItems[i].formattedName) : ''"
                        alt="valamji">
                    </div>
                    <div class="tbl-user-image" *ngIf="!associatedItems[i].formattedName">
                      {{associatedItems[i].displayName?.charAt(0).toUpperCase()}}
                    </div>
                    <div class="tbl-user-text-action">
                      <div class="tbl-user-text">
                        <p>{{ associatedItems[i].displayName }} </p>
                        <span>{{associatedItems[i].skuId}}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm"
              [ngClass]="{'form-error': contact.get('supplierSku').invalid && contact.get('supplierSku').touched}">
              <input [(ngModel)]="associatedItems[i].supplierSku" [maxlength]="utilsService.validationService.MAX_30"
                formControlName="supplierSku" type="text" class="form-control">
            </div>
          </td>
          <td>{{associatedItems[i].hsnCode ? associatedItems[i].hsnCode : '-'}}</td>
          <td>
            <div class="form-group form-group-sm">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group">
                  <input [(ngModel)]="associatedItems[i].itemLength" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemLength').invalid && contact.get('itemLength').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemLength" type="text" class="form-control">
                  <input [(ngModel)]="associatedItems[i].itemWidth" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWidth').invalid && contact.get('itemWidth').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWidth" type="text" class="form-control">
                  <input [(ngModel)]="associatedItems[i].itemHeight" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemHeight').invalid && contact.get('itemHeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemHeight" type="text" class="form-control">
                  <ng-select class="unit-width" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'"
                    [ngClass]="{'required': contact.get('itemDimUnitMaster').invalid && contact.get('itemDimUnitMaster').touched}"
                    [items]="associatedItems[i].itemDimArr" bindLabel="shortCode" bindValue="id" formControlName="itemDimUnitMaster"
                    [(ngModel)]="associatedItems[i].itemDimUnitMasterId">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm ">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group ">
                  <input [(ngModel)]="associatedItems[i].itemWeight"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWeight').invalid && contact.get('itemWeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWeight" type="text"
                    class="form-control">
                  <ng-select [disableControl]="true"
                    [ngClass]="{'required': contact.get('cartonWeightDim').invalid && contact.get('cartonWeightDim').touched}"
                    class="unit-width unit-width-ng-no-arrow" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'" [items]="associatedItems[i].cartownWdropdown" bindLabel="shortCode"
                    bindValue="id" formControlName="cartonWeightDim" [(ngModel)]="associatedItems[i].cartonWeightDim">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group">
                  <input [(ngModel)]="associatedItems[i].itemWithBoxLength" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWithBoxLength').invalid && contact.get('itemWithBoxLength').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWithBoxLength" type="text" class="form-control">
                  <input [(ngModel)]="associatedItems[i].itemWithBoxWidth" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWithBoxWidth').invalid && contact.get('itemWithBoxWidth').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWithBoxWidth" type="text" class="form-control">
                  <input [(ngModel)]="associatedItems[i].itemWithBoxHeight" [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWithBoxHeight').invalid && contact.get('itemWithBoxHeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWithBoxHeight" type="text" class="form-control">
                  <ng-select class="unit-width" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'"
                    [ngClass]="{'required': contact.get('itemWithBoxDimUnitMaster').invalid && contact.get('itemWithBoxDimUnitMaster').touched}"
                    [items]="associatedItems[i].itemBoxDimArr" bindLabel="shortCode" bindValue="id" formControlName="itemWithBoxDimUnitMaster"
                    [(ngModel)]="associatedItems[i].itemWithBoxDimUnitMasterId">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm ">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group ">
                  <input [(ngModel)]="associatedItems[i].itemWithBoxWeight"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('itemWithBoxWeight').invalid && contact.get('itemWithBoxWeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="itemWithBoxWeight" type="text"
                    class="form-control">
                  <ng-select [disableControl]="true"
                    [ngClass]="{'required': contact.get('cartonWeightDim').invalid && contact.get('cartonWeightDim').touched}"
                    class="unit-width unit-width-ng-no-arrow" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'" [items]="associatedItems[i].cartownWdropdown" bindLabel="shortCode"
                    bindValue="id" formControlName="cartonWeightDim" [(ngModel)]="associatedItems[i].cartonWeightDim">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group">
                  <input [(ngModel)]="associatedItems[i].cartonLength"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('cartonLength').invalid && contact.get('cartonLength').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="cartonLength" type="text"
                    class="form-control" (input)="calculateCBM(associatedItems[i])">
                  <input [(ngModel)]="associatedItems[i].cartonWidth"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('cartonWidth').invalid && contact.get('cartonWidth').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="cartonWidth" type="text"
                    class="form-control" (input)="calculateCBM(associatedItems[i])">
                  <input [(ngModel)]="associatedItems[i].cartonHeight"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('cartonHeight').invalid && contact.get('cartonHeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="cartonHeight" type="text"
                    class="form-control" (input)="calculateCBM(associatedItems[i])">
                  <ng-select class="unit-width" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'"
                    [ngClass]="{'required': contact.get('unitId').invalid && contact.get('unitId').touched}"
                    [items]="associatedItems[i].unitDropdown" bindLabel="shortCode" bindValue="id"
                    formControlName="unitId" [(ngModel)]="associatedItems[i].unitId"
                    (change)="calculateCBM(associatedItems[i])">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm ">
              <div class="form-control-wrapper">
                <div class="input-group input-group-sm input-group-select tbl-input-group ">
                  <input [(ngModel)]="associatedItems[i].cartonWeight"
                    [maxlength]="utilsService.validationService.MAX_10"
                    [ngClass]="{'required': contact.get('cartonWeight').invalid && contact.get('cartonWeight').touched}"
                    mask="separator.3" thousandSeparator="" formControlName="cartonWeight" type="text"
                    class="form-control">
                  <ng-select [disableControl]="true"
                    [ngClass]="{'required': contact.get('cartonWeightDim').invalid && contact.get('cartonWeightDim').touched}"
                    class="unit-width unit-width-ng-no-arrow" placeholder="Unit" [multiple]="false" [clearable]="false"
                    [appendTo]="'.input-group'" [items]="associatedItems[i].cartownWdropdown" bindLabel="shortCode"
                    bindValue="id" formControlName="cartonWeightDim" [(ngModel)]="associatedItems[i].cartonWeightDim">
                    <ng-template ng-option-tmp let-item="item">
                      <div [title]="item.shortCode">
                        {{ item.shortCode }}
                      </div>
                    </ng-template>
                  </ng-select>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm form-group-100">
              <input [(ngModel)]="associatedItems[i].cbm" [maxlength]="utilsService.validationService.MAX_10"
                mask="separator.5" thousandSeparator="" formControlName="cbm" type="text" class="form-control">
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm"
              [ngClass]="{'form-error': contact.get('cartonQuantity').invalid && contact.get('cartonQuantity').touched}">
              <input (input)="onChangeTagQty(associatedItems[i], i)" [(ngModel)]="associatedItems[i].cartonQuantity"
                [maxlength]="utilsService.validationService.MAX_10" mask="separator.0" thousandSeparator=""
                formControlName="cartonQuantity" type="text" class="form-control">
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm"
              [ngClass]="{'form-error': contact.get('pricePerItem').invalid && contact.get('pricePerItem').touched}">
              <input [(ngModel)]="associatedItems[i].pricePerItem" [maxlength]="utilsService.validationService.MAX_10"
                [mask]="getSupplierType() === 'CHINA' ? 'separator.4' : 'separator.2'" thousandSeparator=""
                formControlName="pricePerItem" type="text" class="form-control">
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm"
              [ngClass]="{'form-error': contact.get('pricePerCarton').invalid && contact.get('pricePerCarton').touched}">
              <input [(ngModel)]="associatedItems[i].pricePerCarton" [maxlength]="utilsService.validationService.MAX_10"
                mask="separator.2" thousandSeparator="" formControlName="pricePerCarton" type="text"
                class="form-control">
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm"
              [ngClass]="{'form-error': contact.get('tag').invalid && contact.get('tag').touched}">
              <input (input)="onChangeTagQty(associatedItems[i], i)" [(ngModel)]="associatedItems[i].tag"
                [maxlength]="utilsService.validationService.MAX_30" formControlName="tag" type="text"
                class="form-control">
            </div>
          </td>
          <td class="tbl-form-group">
            <div class="form-group theme-ngselect form-border-less">
              <ng-select class="" placeholder="Select Color" [clearable]="false" [closeOnSelect]="false"
                [ngClass]="{'required': contact.get('colorIds').invalid && contact.get('colorIds').touched}"
                [items]="associatedItems[i].colorDropdown" [multiple]="true" bindLabel="colorMasterName" bindValue="id"
                [clearSearchOnAdd]="true" (clear)="preventRemovalColor($event, associatedItems[i])"
                [(ngModel)]="associatedItems[i].colorIds" formControlName="colorIds" [appendTo]="'.theme-ngselect'">
              </ng-select>
            </div>
          </td>
          <td class="tbl-form-group">
            <div class="form-group theme-ngselect form-border-less">
              <ng-select class="" placeholder="Select" [clearable]="false" [closeOnSelect]="false"
                [ngClass]="{'required': contact.get('packingTypeIds').invalid && contact.get('packingTypeIds').touched}"
                [items]="associatedItems[i].packingTypes" [multiple]="true" bindLabel="label" bindValue="value"
                [clearSearchOnAdd]="true" [(ngModel)]="associatedItems[i].packingTypeIds" formControlName="packingTypeIds"
                [appendTo]="'.theme-ngselect'">
              </ng-select>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm form-group-200 eng-china-comment"
              [ngClass]="{'form-error': contact.get('englishComment').invalid && contact.get('englishComment').touched}">
              <textarea autosize [minRows]="2" [maxRows]="7" [(ngModel)]="associatedItems[i].englishComment"
                [maxlength]="utilsService.validationService.MAX_100" formControlName="englishComment" type="text"
                class="form-control"></textarea>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm form-group-200 eng-china-comment"
              [ngClass]="{'form-error': contact.get('chinaComment').invalid && contact.get('chinaComment').touched}">
              <textarea autosize [minRows]="2" [maxRows]="7" [(ngModel)]="associatedItems[i].chinaComment"
                [maxlength]="utilsService.validationService.MAX_100" formControlName="chinaComment" type="text"
                class="form-control"></textarea>
            </div>
          </td>
          <td>
            <div class="form-group form-group-sm new-ng-width"
              [ngClass]="{'form-error': contact.get('measurementCode').invalid && contact.get('measurementCode').touched}">
              <input [(ngModel)]="associatedItems[i].measurementCode"
                [maxlength]="utilsService.validationService.MAX_100" formControlName="measurementCode" type="text"
                class="form-control">
            </div>
          </td>
          <td class="tbl-switch">
            <div class="switch-box">
              <label class="switch" htmlFor="switch-{{i}}">
                <input (change)="onChangeActiveDeactive(associatedItems[i], i)" type="checkbox" id='switch-{{i}}'
                  [(ngModel)]="associatedItems[i].isActive" formControlName="isActive" />
                <div class="slider round"></div>
              </label>
            </div>
          </td>
          <td class="tbl-action">
            <div class="tbl-action-group">
              <button (click)="imgs.click()" class="btn btn-xs btn-light-primary btn-icon" ngbTooltip="Upload"
                placement="bottom" container="body" triggers="hover">
                <i class="bi bi-upload"></i>
              </button>
              <input #imgs type="file" (change)="onSelectImages($event, i);imgs.value=''" accept="image/*" hidden
                [multiple]="true">
              <button [disabled]="associatedItems[i].isDeleteFlag" (click)="openRemoveModal(i)"
                class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Remove Associate Item" placement="left"
                container="body" triggers="hover">
                <i class="th th-outline-trash"></i>
              </button>
              <app-attachment-download-dropdown [fileList]="associatedItems[i].images"
                (openRemoveImageItemModal)="openImgItem($event.index, $event.childIndex)" [local]="true"
                [selectedIndex]="i">
              </app-attachment-download-dropdown>
            </div>
          </td>
        </tr>
      </tbody>
      <tfoot>
        <tr class="tbl-add-new">
          <td colspan="100">
            <button (click)="addAssociateItems.emit()" class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                class="th-bold-add-circle"></i>
              Add New Row
            </button>
          </td>
        </tr>
      </tfoot>
    </table>
  </div>
</div>

<!-- Back to Draft Modal -->
<ng-template #createToPoWarning let-modal>
  <div class="modal-theme modal-confirmation modal-warning">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to save changes before creating PO?</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
          <button (click)="createPO(modal)" type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Confirm</button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
import { deserializeAs, serializeAs } from 'cerialize';
import { Dayjs } from 'dayjs';

export class Season {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('seasonName')
    @deserializeAs('seasonName')
    private _seasonName: string;

    @serializeAs('fromDate')
    @deserializeAs('fromDate')
    private _fromDate: string;

    @serializeAs('toDate')
    @deserializeAs('toDate')
    private _toDate: string;

    @serializeAs('advanceDate')
    @deserializeAs('advanceDate')
    private _advanceDate: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    // @serializeAs('date')
    @deserializeAs('date')
    private _date: any;

    // @serializeAs('tempAdvDate')
    @deserializeAs('tempAdvDate')
    private _tempAdvDate: any;

    @deserializeAs('tempStartDate')
    private _tempStartDate: any;

    @deserializeAs('tempEndDate')
    private _tempEndDate: any;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @deserializeAs('from')
    private _from: Date;

    @deserializeAs('to')
    private _to: Date;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDefault = false;
    }


    /**
     * Getter tempStartDate
     * @return {any}
     */
	public get tempStartDate(): any {
		return this._tempStartDate;
	}

    /**
     * Getter tempEndDate
     * @return {any}
     */
	public get tempEndDate(): any {
		return this._tempEndDate;
	}

    /**
     * Setter tempStartDate
     * @param {any} value
     */
	public set tempStartDate(value: any) {
		this._tempStartDate = value;
	}

    /**
     * Setter tempEndDate
     * @param {any} value
     */
	public set tempEndDate(value: any) {
		this._tempEndDate = value;
	}


    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter from
     * @return {Date}
     */
	public get from(): Date {
		return this._from;
	}

    /**
     * Getter to
     * @return {Date}
     */
	public get to(): Date {
		return this._to;
	}

    /**
     * Setter from
     * @param {Date} value
     */
	public set from(value: Date) {
		this._from = value;
	}

    /**
     * Setter to
     * @param {Date} value
     */
	public set to(value: Date) {
		this._to = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    

    /**
     * Getter date
     * @return {any}
     */
	public get date(): any {
		return this._date;
	}

    /**
     * Getter tempAdvDate
     * @return {any}
     */
	public get tempAdvDate(): any {
		return this._tempAdvDate;
	}

    /**
     * Setter date
     * @param {any} value
     */
	public set date(value: any) {
		this._date = value;
	}

    /**
     * Setter tempAdvDate
     * @param {any} value
     */
	public set tempAdvDate(value: any) {
		this._tempAdvDate = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter seasonName
     * @return {string}
     */
	public get seasonName(): string {
		return this._seasonName;
	}

    /**
     * Getter fromDate
     * @return {string}
     */
	public get fromDate(): string {
		return this._fromDate;
	}

    /**
     * Getter toDate
     * @return {string}
     */
	public get toDate(): string {
		return this._toDate;
	}

    /**
     * Getter advanceDate
     * @return {string}
     */
	public get advanceDate(): string {
		return this._advanceDate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter seasonName
     * @param {string} value
     */
	public set seasonName(value: string) {
		this._seasonName = value;
	}

    /**
     * Setter fromDate
     * @param {string} value
     */
	public set fromDate(value: string) {
		this._fromDate = value;
	}

    /**
     * Setter toDate
     * @param {string} value
     */
	public set toDate(value: string) {
		this._toDate = value;
	}

    /**
     * Setter advanceDate
     * @param {string} value
     */
	public set advanceDate(value: string) {
		this._advanceDate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


}
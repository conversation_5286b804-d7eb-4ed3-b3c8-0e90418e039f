import { Directive, ElementRef, Input, OnInit } from '@angular/core';
import { UtilsService } from '../services/utils.service';
import { EnumForRole } from '../enums/EnumForRole';

@Directive({
  selector: '[pageAccess]'
})
export class RoleDirective implements OnInit {

  @Input() pageAccess: {page: string, action: string, view?: boolean};
  permissionData: any[];
  enumForRole = EnumForRole;

  constructor(private el: ElementRef, private utilsService: UtilsService) { }

  ngOnInit() {
    this.permissionData = this.utilsService.decodeToken(this.utilsService.getToken())?.pageAccess;
    this.checkRole();
  }

  checkRole() {

    const page = this.permissionData?.find(p => p.name === this.pageAccess.page);
    const checkPage = page?.userPrivileges?.find(p => (p.privName === this.pageAccess.action) && (p.name === page.name));

    this.showAction(checkPage, page);
  }

  showAction(checkPage, page) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(checkPage)) {
      if (this.pageAccess.view) {
        this.utilsService.goToAccessDenied();
      }
      else {
        this.el.nativeElement.remove();
      }
    }
  }

}

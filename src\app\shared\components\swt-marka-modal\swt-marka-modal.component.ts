import { Component, computed, effect, inject, input, OnInit, output, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormControlsOf } from '@libs';
import { SWTItemSave, SWTMarkaData } from '@modal/StockWarehouseTransfer';
import { UtilsService } from '@service/utils.service';

interface InputData {
  index: number;
  markaData: SWTMarkaData[];
}

@Component({
  selector: 'app-swt-marka-modal',
  templateUrl: './swt-marka-modal.component.html',
  styleUrl: './swt-marka-modal.component.scss'
})
export class SwtMarkaModalComponent implements OnInit {

  utilsService = inject(UtilsService);

  openMarkaModal = output<void>();
  onSaveMarkaQty = output<void>();

  isLoose = input.required<boolean>();
  inputData = input.required<InputData>();
  items = input<FormGroup<FormControlsOf<SWTItemSave>>>();
  isPageView = input<boolean>(false);
  isMarkaSelectionAlwaysEnabled = input<boolean>(false);
  marka = input<any[]>([]);

  data = signal<InputData>({ index: null, markaData: [] });

  constructor() {
    // Updates data signal
    effect(() => {
      this.data.set(this.inputData());
    }, { allowSignalWrites: true });
  }

  totalCartonFieldSum = computed(() => (this.data()?.markaData || []).reduce((acc, d) =>
    acc + d.locations.reduce((sum, loc) => sum + (loc.cartonField || 0), 0), 0
  )
  );

  totalLooseFieldSum = computed(() => (this.data()?.markaData || []).reduce((acc, d) =>
    acc + d.locations.reduce((sum, loc) => sum + (loc.looseField || 0), 0), 0
  )
  );

  totalQtyAll = computed(() => (this.data()?.markaData || []).reduce((acc, d) =>
    acc + (d.totalCartonQty || 0) * (d.locations[0]?.piecesPerCarton || 0), 0
  )
  );

  ngOnInit(): void {
    console.log(this.marka());

  }

  onChangeCartonLoose = (event: any, markaIndex: number, locationIndex: number) => {
    const value = Number(event) || null;
    const location = this.data().markaData[markaIndex].locations[locationIndex];

    if (this.isLoose()) {
      location.looseField = value;
    } else {
      location.cartonField = value;
    }

    this.data.set({ ...this.data() });
  }

  checkIfInvalid = (value: number, markaIndex: number, locationIndex: number): boolean => {
    if (this.isLoose()) {
      const location = this.data().markaData[markaIndex].locations[locationIndex];
      return value > location.looseQty;
    } else {
      const location = this.data().markaData[markaIndex].locations[locationIndex];
      return value > location.cartonQty;
    }
  }

  disableIfInvalid = computed(() => {
    const key = this.isLoose() ? 'looseField' : 'cartonField';
    const markaData = this.data()?.markaData || [];

    for (let mIndex = 0; mIndex < markaData.length; mIndex++) {
      const marka = markaData[mIndex];
      for (let lIndex = 0; lIndex < marka.locations.length; lIndex++) {
        const loc = marka.locations[lIndex];
        const value = loc[key];

        if (this.utilsService.isValidValue(value) && !this.checkIfInvalid(value, mIndex, lIndex)) {
          return false;
        }
      }
    }
    return true;
  });

  onClear = () => {
    this.items().patchValue({
      marka: null,
      isMarkaSelected: false,
      reqQty: null,
      reqPcsPerCarton: null,
      assoItemQtyReqList: [],
      reqTotalPcs: null,
    })
    this.data().markaData.forEach(v => {
      v.locations.forEach(loc => {
        loc.cartonField = null;
        loc.looseField = null;
      })
    })
    this.data.set({ ...this.data() });
  }


  checkIfAnyMarka(): boolean {
    if (this.isMarkaSelectionAlwaysEnabled()) {
      return true;
    }
    return !this.utilsService.isEmptyObjectOrNullUndefined(this.marka())
  }
}
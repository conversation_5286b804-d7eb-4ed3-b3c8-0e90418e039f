export interface StockWarehouseTransferSave {
    expectedDate: string;
    reasonMasterId: number;
    note: string;
    pickupPersonId: number;
    tempoNo: string;
    contactCountryExtensionId: number;
    mobileNo: string;
    isManualRaised: boolean;
    warehouseToId: number;
    warehouseFromId: number;
    associateItemReqList: SWTItemSave[]
}

export interface SWTItemSave {
    itemType: string
    marka: string //comma seperated for multiple markas
    reqQty: number
    reqPcsPerCarton: number
    reqTotalPcs: number
    itemId: number
    warehouseId: number
    currentStock: number
    branchTransferAlert: number
    averagePrice: number
    mainWarehouseAlert: number
    importPurchaseQty: number
    importPurchaseDays: number
    importPurchaseDate: string
    importPurchaseStatus: string,
    isDataLoaded: boolean,
    isMarkaSelected: boolean,
    //
    assoItemQtyReqList: unknown
}

export interface AssoItemQtyReqList {
    qty: number
    marka: string
    locationId: number
    locationType: string
    piecesPerCarton: number
}

export interface SWTAttachments {
    id: number;
    file: File;
    originalName: string;
    formattedName: string;
}

export interface SWTReq {
    items: SWTItemDropdown[]
    reason: { label: string, value: number, isActive: boolean }[]
    user: { label: string, value: number, isActive: boolean }[]
    warehouseTo: { label: string, value: number, isActive: boolean }[]
    warehouseFrom: { label: string, value: number, isActive: boolean }[]
    country: { countryExtension: string, id: number, isActive: boolean }[]
    orderUnit: { label: string, value: string }[],
    dropLocation?: { label: string, value: number, isActive: boolean }[]
}

export interface SWTItemDropdown {
    id: number
    itemName: string
    displayName: string;
    skuId: string
    hsnCode: string;
    originalName: string
    formattedName: string;
}

// LISTING

export interface SWTListingMain {
    id: number
    expectedDate: string
    status: string
    reason: string
    note: string
    isManualRaised: boolean
    warehouseFromId: number
    warehouseFromName: string
    warehouseToId: number
    warehouseToName: string
    isExpand: boolean
    isExpandItem: boolean;
    loadedChild: boolean;
    items: SWTListingItem[]
    createdDate: string,
    transferId: string
    createdBy: string,
}

export interface SWTListingItem {
    id: number
    itemType: string
    marka: any;
    reqQty: number
    reqPcsPerCarton: number
    reqTotalPcs: number
    approvedQty: number
    approvedPcsPerCarton: number
    approvedTotalPcs: number
    itemId: number
    itemName: string
    skuId: string
    originalName: string
    formattedName: string
    currentStock: number
    branchTransferAlert: number
    averagePrice: number
    importPurchaseQty: number
    importPurchaseDays: number
    importPurchaseDate: string
    importPurchaseStatus: string
    mainWarehouseAlert: number
    receivedQty: number
    receivedPcsPerCarton: number
    receivedTotalPcs: number
    diffQty: number
    diffPcsPerCarton: number
    diffTotalPcs: number
    mismatchStatus: string
    mismatchNote: string
    markaData: SWTMarkaDataOriginal[]
    ticketData: any[];
    markaDataConverted: SWTMarkaData[]
}

export interface SWTMarkaDataOriginal {
    marka: string;
    looseQty?: number;
    cartonQty?: number;
    itemId: number;
    locationType: string;
    locationTypeId: number;
    locationName: string;
    warehouseId: number;
    warehouseName: string;
    age?: number;
    piecesPerCarton: number;
    reqQty?: number
    approveQty?: number
}

export interface SWTMarkaData {
    marka: string;
    age: number;
    totalLooseQty: number;
    totalCartonQty: number;
    piecesPerCartonFromLocation?: number;
    locations: {
        locationName: string;
        locationType: string;
        looseQty: number;
        cartonQty: number;
        age: number;
        piecesPerCarton: number;
        locationTypeId?: number;
        cartonField?: number;
        looseField?: number
        reqQty?: number
        totalPcs?: number
        approveQty?: number
    }[];
}

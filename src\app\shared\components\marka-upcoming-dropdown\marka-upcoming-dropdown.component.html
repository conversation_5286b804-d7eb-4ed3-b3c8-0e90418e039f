<div class="dropdown-branch-marka dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
  #dropdown="ngbDropdown" container="body">
  <button (click)="openMarkaPopup(i, false)" class="btn btn-link text-primary" type="button" ngbDropdownToggle>
    {{isOnlyUpcoming ? 'Upcoming' : 'View'}}
  </button>
  <div cdkDragBoundary="body" ngbDropdownMenu class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdown.isOpen" cdkDrag [cdkDragFreeDragPosition]="dragPosition">
    <div class="card-dropdown-with-tabs-tables card-theme">
      <div class="card-header">
        <div class='nav-tabs-outer nav-tabs-style2'>
          <nav>
            <div class="nav nav-tabs align-items-center d-flex w-100 justify-content-between" role="tablist">

              <!-- Scrollable branch tabs -->
              <div class="tabs-scroll d-flex">
                <ng-container>
                  @if(!isOnlyUpcoming) {
                    <button *ngFor="let tab of branchMaster; index as b"
                      class="nav-link d-flex flex-column align-items-center px-3" (click)="changeBranchTab(i, b, false);"
                      [class.active]="allMarkaBranchData.activeTab === b" type="button">
                      <div><i class="th th-outline-house-2"></i> {{ tab.branchName }}</div>
                      <small class="text-muted mt-1">{{ tab.totalAvailableQty }}</small>
                    </button>
                  }
                  <button *ngIf="isOnlyUpcoming" (click)="changeToUpcomingTab(i)" [class.active]="isUpcomingTab"
                    class="nav-link d-flex flex-column align-items-center px-3" type="button">
                    <div><i class="th th-outline-building"></i> Upcoming Qty</div>
                    <small *ngIf="isOnlyUpcoming" class="text-muted mt-1">{{ allMarkaBranchData.totalUpcomingQty || 0 }}</small>
                  </button>
                </ng-container>
              </div>

              <!-- Fixed right section -->
              <div class="fixed-tabs d-flex align-items-center ms-2">
                <button class="btn btn-primary btn-icon ms-2" cdkDragHandle>
                  <i class="bi bi-arrows-move m-0"></i>
                </button>
                <button class="btn btn-outline-primary btn-icon ms-2" (click)="dropdown.close()">
                  <i class="bi bi-x m-0"></i>
                </button>
              </div>

            </div>
          </nav>
        </div>
      </div>
      <div class="card-body">
        <div class='nav-tabs-outer nav-tabs-style2'>
          <div class="tab-content mt-2" *ngIf="!isUpcomingTab">
            <div class="p-3 border rounded bg-light mb-3">
              <div class="d-flex justify-content-between align-items-start flex-wrap">
                <!-- Left: Available Carton Counts -->
                <div class="d-flex flex-wrap gap-4">
                  <div>
                    <div class="text-muted small">
                      W. Pickup (CTN | PCS)</div>
                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalWPCarton || 0 }} | {{ allMarkaBranchData.markaDropDown?.totalWPLoose || 0 }}</div>
                  </div>
                  <div>
                    <div class="text-muted small">
                      Hold (CTN | PCS)</div>
                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalHoldCartonQty || 0 }} | {{ allMarkaBranchData.markaDropDown?.totalHoldLooseQty || 0 }}</div>
                  </div>
                  <div>
                    <div class="text-muted small">
                      Available Cartons</div>
                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.availableCartonsQty || 0 }}</div>
                  </div>
                  <div>
                    <div class="text-muted small">
                      Available Loose</div>
                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.availableLooseQty || 0 }}</div>
                  </div>
                  <div>
                    <div class="text-muted small">
                      Total Available Quantity</div>
                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalAvailableQty || 0 }}</div>
                  </div>
                </div>

                <!-- Right: Input -->
                <div class="d-flex flex-wrap align-items-center gap-3 mt-3">
                  <!-- Search Input -->
                  <div>
                    <input type="text" class="form-control"
                      id="searchText-{{ allMarkaBranchData.activeTab }}" placeholder="Search Marka"
                      [formControl]="searchText"
                      (input)="filtersMarkaModal('marka', null)" />
                  </div>

                  <!-- Warehouse Select -->
                  <div class="form-group theme-ngselect fg-marka">
                    <ng-select [items]="allMarkaBranchData.warehouse" bindLabel="warehouseName" bindValue="id"
                      [(ngModel)]="warehouseIdFilter" [ngModelOptions]="{ standalone: true }" placeholder="Warehouse"
                      [clearable]="false" [multiple]="true" [closeOnSelect]="true"
                      (change)="filtersMarkaModal('warehouseId', warehouseIdFilter)">
                      <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                        <div class="ng-value" *ngFor="let item of items | slice:0:1">
                          <span class="text-truncate ng-value-label max-w-100" [title]="item['warehouseName']">{{
                            item['warehouseName'] }}</span>
                          <span class="ng-value-icon right" (click)="clear(item)">×</span>
                        </div>
                        <div class="ng-value" *ngIf="items.length > 1">
                          <span class="ng-value-label">{{items.length - 1}} more...</span>
                        </div>
                      </ng-template>
                    </ng-select>
                  </div>
                </div>
              </div>
            </div>

            <div class="fade show active">
              <div class="table-responsive marka-inquiry-scroll">
                <table class="table-theme table table-bordered tbl-collapse">
                  <thead class="border-less">
                    <tr>
                      <th>Marka</th>
                      <th>Warehouse</th>
                      <th>Carton Qty</th>
                      <th>Loose</th>
                      <th *ngIf="!isSO">Total Carton Qty</th>
                      <th>Total Qty</th>
                      <th *ngIf="isSO"> Ordered Qty <br/> (CTN/Loose) </th>
                      <th>Image Link</th>
                    </tr>
                  </thead>
                  <tbody>

                    <ng-container *ngFor="let marka of filteredMarkaList; let mi = index">
                      <ng-container *ngFor="let warehouse of marka.warehouseStocks; let wi = index">
                        <tr [ngClass]="{'tbl-bg-danger': marka?.expiresInDays < 0}">
                          <!-- Marka Cell -->
                          <td *ngIf="wi === 0" [attr.rowspan]="marka.warehouseStocks.length">
                            <div class="fw-semibold">{{ marka.marka }}</div>
                            <div class="text-muted">Age: {{ marka.age || '-' }}</div>
                            <div class="text-muted">Quality: {{marka.tag || '-'}}</div>
                          </td>
                    
                          <!-- Warehouse Name -->
                          <td>{{ warehouse.warehouseName }}</td>
                    
                          <!-- Carton Qty -->
                          <td>
                            <div *ngIf="warehouse.pcsPerCarton">
                              {{ warehouse.totalCartons }} x {{ warehouse.pcsPerCarton }} = {{ warehouse.totalCartonQty }}
                            </div>
                            <div *ngIf="!warehouse.pcsPerCarton">-</div>
                          </td>
                    
                          <!-- Loose Qty -->
                          <td>{{ warehouse.totalLooseQty || '-' }}</td>
                    
                          <!-- Total Qty (No rowspan so alignment stays) -->
                          <td *ngIf="!isSO">{{ warehouse.totalCartonQty }}</td>
                          <td *ngIf="isSO">{{ warehouse.totalQty }}</td>
                    
                          <!-- Ordered Qty (Only on first warehouse row) -->
                          <td *ngIf="wi === 0 && isSO" [attr.rowspan]="marka.warehouseStocks.length">
                            <div class="form-group form-group-50" [ngClass]="{ 'form-error': isQtyInvalid(marka) }">
                              <input (ngModelChange)="getQtyTotal(mi)" type="text" class="form-control" placeholder="Enter"
                                [(ngModel)]="marka.qty" mask="separator.0" thousandSeparator=""
                                [maxLength]="utilsService.validationService.MAX_10">
                            </div>
                          </td>
                    
                          <!-- Image Link -->
                          <td *ngIf="wi === 0" [attr.rowspan]="marka.warehouseStocks.length" class="tbl-action">
                            <div class="tbl-action-group">
                              <ng-container *ngIf="marka.grnGroupLinkId; else noLink">
                                <span>{{ marka.countOfImage > 5 ? '5+' : marka.countOfImage }}</span>
                                <a *ngIf="marka.countOfImage > 0" (click)="utilsService.openStockDetailsInNewTab(marka.grnGroupLinkId)"
                                  class="btn-link text-primary">
                                  Link
                                </a>
                              </ng-container>
                              <ng-template #noLink>
                                <span>0</span>
                              </ng-template>
                              <button (click)="onCopy(marka.grnGroupLinkId)" [copyText]="getGrnGroupLink(marka.grnGroupLinkId)"
                                *ngIf="marka.grnGroupLinkId && marka.countOfImage > 0" class="btn btn-xs btn-transparent btn-icon" ngbTooltip="Copy Link"
                                placement="bottom" container="body" triggers="hover">
                                <i class="th th-outline-copy"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    
                      <!-- Marka Total Row -->
                      <tr class="fw-semibold bg-white border-top" *ngIf="(marka?.warehouseStocks || []).length > 0">
                        <td colspan="2">-</td>
                        <td>{{ marka.sumCartonPcs }}</td>
                        <td>-</td>
                        <td>{{ marka.sumTotalQty }}</td>
                        <td>{{ getQtyTotal(mi) }}</td>
                        <td>-</td>
                      </tr>
                    </ng-container>
                    
                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(filteredMarkaList)">
                      <td colspan="20" class="text-center">
                        <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="tab-content mt-2" *ngIf="isUpcomingTab">
            <div class="p-3 border rounded bg-light mb-3">
              <div class="d-flex justify-content-between align-items-start flex-wrap">
                <!-- Left: Available Carton Counts -->
                <div class="d-flex flex-wrap gap-4">
                  <div>
                    <div class="text-muted small">
                      Upcoming Cartons</div>
                    <div class="fw-bold">{{allMarkaBranchData.upcomingCartonQty}}</div>
                  </div>
                  <div>
                    <div class="text-muted small">
                      Total Upcoming Qty (Available)
                    </div>
                    <div class="fw-bold">{{allMarkaBranchData.totalUpcomingQty}}</div>
                  </div>
                </div>

                <!-- Right: Input -->
                <div>
                  <div>
                    <input type="text" class="form-control form-control-sm" style="width: 230px;"
                      id="searchTextUpcoming-{{ allMarkaBranchData.activeTab }}" placeholder="Search Marka"
                      [formControl]="searchText"
                      (input)="filterUpcomingQty('marka', null)" />
                  </div>
                </div>
              </div>
            </div>

            <div class="fade show active">
              <div class="table-responsive marka-inquiry-scroll">
                <table class="table-theme table-hover table table-bordered table-sticky">
                  <thead class="border-less">
                    <tr>
                      <th>Marka</th>
                      <!-- <th>Stage</th> -->
                      <th>Expected <br/> Delivery Date</th>
                      <th>Carton Qty</th>
                      <th *ngIf="isSO"> Ordered Qty <br/> (CTN/Loose) </th>
                      <th>Total Qty</th>
                      <!-- <th>Image Link</th> -->
                    </tr>
                  </thead>
                  <tbody>
                    @for(item of filteredUpcomingQtyList; let i = $index; track $index) {
                    <tr>
                      <td>
                        <div class="fw-semibold">
                          {{item.marka}}
                        </div>
                      </td>
                      <!-- <td>
                        <div class="fw-semibold">
                          {{item.stage['label']}}
                        </div>
                        <div class="text-muted">
                          {{item.stageDate ? (item.stageDate | date: 'dd/MM/YYYY h:mm a') : '-'}}
                        </div>
                        <div class="text-muted" *ngIf="item.containerName">
                          {{item.containerName}}
                        </div>
                      </td> -->
                      <td>
                        <span>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</span>
                      </td>
                      <td>
                        @if(item.cartonPic && item.cartonQty) {
                        <span>{{item.cartonQty}} x {{item.cartonPic}} = {{item.totalQty}}</span>
                        } @else {
                        <span>-</span>
                        }
                      </td>
                      <td>
                        <div class="form-group form-group-50" [ngClass]="{ 'form-error': isUpcomingInvalid(i) }">
                          <input type="text" class="form-control" placeholder="Enter" [(ngModel)]="item.upcomingQty" mask="separator.0"
                            thousandSeparator="" [maxLength]="utilsService.validationService.MAX_10">
                        </div>
                      </td>
                      <td>
                        <span>{{item.totalQty ? item.totalQty : 0}}</span>
                      </td>
                    </tr>
                    } @empty {
                    <tr
                      *ngIf="utilsService.isEmptyObjectOrNullUndefined(allMarkaBranchData?.markaDropDown?.markaStocks)">
                      <td colspan="20" class="text-center">
                        <span
                          class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                      </td>
                    </tr>
                    }
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card-footer mt-2" *ngIf="isSO">
        <div class="button-group">
          <button *ngIf="!isUpcomingTab" [disabled]="disabledIfInvalid()" (click)="onSaveMarkaQty();dropdown.close()" type="button" class="btn btn-sm btn-primary btn-icon-text">
            <i class="th th-outline-tick-circle"></i>
            Save
          </button>
          <button [disabled]="disabledUpcoming()" *ngIf="isUpcomingTab" (click)="onSaveUpcomingQty();dropdown.close()"
            type="button" class="btn btn-sm btn-primary btn-icon-text">
            <i class="th th-outline-tick-circle"></i>
            Save
          </button>
          <button (click)="dropdown.close()" type="button" class="btn btn-sm btn-outline-white btn-icon-text">
            <i class="th th-outline-close-circle"></i>
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
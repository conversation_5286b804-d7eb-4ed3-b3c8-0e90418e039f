import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, inject } from '@angular/core';
import { AuditTicketService } from './audit-ticket.service';

@Component({
  selector: 'app-audit-tickets-list',
  templateUrl: './audit-tickets-list.component.html',
  styleUrls: ['./audit-tickets-list.component.scss']
})
export class AuditTicketsListComponent implements OnInit, OnDestroy {

  auditService = inject(AuditTicketService)

  constructor() { }

  ngOnInit(): void {
    this.auditService.initPagination()
  }

  ngOnDestroy(): void {
    this.auditService.destroy$.next()
    this.auditService.destroy$.complete()
  }

}

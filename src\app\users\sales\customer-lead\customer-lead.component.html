<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.VIEW_CUSTOMER_LEAD, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Customer Leads</h4>
    </div>
    <div class="page-title-right">
      <button
        [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.ADD_CUSTOMER_LEAD}"
        (click)="openAddEditForm(null, true)" class="btn btn-sm btn-primary btn-icon-text">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button (click)="onRefresh()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left"
        container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <!-- ---------------------------- content-area ----------------------------- -->
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input [ngModel]="paginationRequest.get().searchText" (change)="onChangeFilter($event, 'search')"
              type="search" class="form-control" placeholder="Search by name">
          </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
          <div class="form-group-icon-end">
            <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
            <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
              [ngModel]="paginationRequest.get().dateRange" (ngModelChange)="onChangeFilter($event, 'date')"
              [showCustomRangeLabel]="true" [alwaysShowCalendars]="true" [ranges]="utilsService.ranges"
              [linkedCalendars]="false" [showClearButton]="false" placeholder="Created Date" [autoApply]="true"
              [showRangeLabelOnInput]="true" startKey="start" endKey="end">
          </div>
        </div>
        <button (click)="onClear()" class="btn btn-link btn-sm">Clear</button>
      </div>
      <div class="page-filters-right">
        <div class="form-group theme-ngselect form-group-sm form-group-export">
          <div class="dropdown export-dropdown">
            <button [disabled]="customerLeadListEmpty()" type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
              aria-expanded="false">
              Export
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="card card-theme card-table-sticky ">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered table-sticky">
            <thead class="border-less">
              <tr>
                @for(th of customerLeadTH; track $index; let j = $index) {
                <th [class]="th.class"
                  [ngClass]="{'sorting-asc': paginationRequest.get().sortColumn==th.keyName && paginationRequest.get().sortOrder === enumForSortOrder.A, 
                              'sorting-desc': paginationRequest.get().sortColumn==th.keyName && paginationRequest.get().sortOrder === enumForSortOrder.D }"
                  (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                  <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                    class="checkbox checkbox-primary checkbox-small">
                    <input [disabled]="customerLeadListEmpty()" type="checkbox" id="tbl-checkbox"
                      class="material-inputs filled-in" [ngModel]="flagForSelectAll()"
                      (ngModelChange)="selectAll($event)" />
                    <label for="tbl-checkbox"></label>
                  </div>
                  {{th.displayName}}
                </th>
                }
                <th class="text-center"
                  *ngIf="utilsService.checkPageAccess([utilsService.enumForPage.EDIT_CUSTOMER_LEAD, utilsService.enumForPage.DELETE_CUSTOMER_LEAD, utilsService.enumForPage.CONVERT_TO_CUSTOMER])">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              @for(item of customerLeadList.get(); track item.id; let i = $index) {
              <tr>
                @let fullName = item.firstName + ' ' + (item?.middleName ? item.middleName : '') + ' ' + (item?.lastName ? item.lastName : '');
                <td class="tbl-user tbl-bold">
                  <div class="tbl-user-checkbox-srno">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox2-{{i}}" [ngModel]="item.isSelected"
                        class="material-inputs filled-in" (ngModelChange)="selectUnselect(i, $event)" />
                      <label for="tbl-checkbox2-{{i}}"></label>
                    </div>
                    <div class="tbl-user-wrapper">
                      <div class="tbl-user-image">
                        {{ fullName.charAt(0).toUpperCase() }}
                      </div>
                      <div class="tbl-user-text-action">
                        <div class="tbl-user-text">
                          <p>{{fullName}}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
                <td>{{item.email ?? '-'}}</td>
                <td>{{item.countryExtension}} {{item.phone}}</td>
                <td>{{item.leadCreateDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                <td>{{item.inquiryCount ? item.inquiryCount : 0}} {{item.itemCount ? " [" +item.itemCount + "] " : ''}}</td>
                <td class="tbl-action"
                  *ngIf="utilsService.checkPageAccess([utilsService.enumForPage.EDIT_CUSTOMER_LEAD, utilsService.enumForPage.DELETE_CUSTOMER_LEAD, utilsService.enumForPage.CONVERT_TO_CUSTOMER])">
                  <div class="tbl-action-group">
                    <button [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.CONVERT_TO_CUSTOMER}" 
                      (click)="redirectToRegAdd(item.id)" class="btn btn-xs btn-light-success btn-icon" 
                      ngbTooltip="Convert to Customer" placement="left" container="body"
                      triggers="hover">
                      <i class="th th-outline-add-circle"></i>
                    </button>
                    <button [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.EDIT_CUSTOMER_LEAD}" 
                      (click)="openAddEditForm(item, false)" class="btn btn-xs btn-light-white btn-icon"
                      ngbTooltip="Edit" placement="bottom" container="body" triggers="hover">
                      <i class="th th-outline-edit"></i>
                    </button>
                    <button [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.DELETE_CUSTOMER_LEAD}" 
                      (click)="openDeleteCustomerLeadModal(item)" class="btn btn-xs btn-light-danger btn-icon"
                      ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                      <i class="th th-outline-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              }
              <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(customerLeadListEmpty())">
                <td colspan="20" class="text-center">
                  <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.get().pageNo" [pageSize]="paginationRequest.get().pageSize"
        [totalData]="paginationRequest.get().totalData">
      </app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="addCustomerLeadModal" tabindex="-1" aria-labelledby="addCustomerLeadModalLabel">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <app-customer-lead-modal [formGroup]="formGroup" [countryCodeDropdown]="countryCodeDropdown" [isAdd]="isAdd()"
      (onSave)="onSave()" [isFromInquiry]="false"/>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteCustomerLeadModal" tabindex="-1"
  aria-labelledby="deleteCustomerLeadModalLabel">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
          
            @let fullName = 
              (customerLeadObj.get()?.firstName ?? '') +
              (customerLeadObj.get()?.middleName ? ' ' + customerLeadObj.get()?.middleName : '') +
              (customerLeadObj.get()?.lastName ? ' ' + customerLeadObj.get()?.lastName : '');
          
            <p>You want to Delete <b>{{ fullName }}</b> Customer Lead.</p>
            <p><b>Note:</b> Existing inquiries for this customer lead will be deleted.</p>
          </div>
          
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteCustomerLead()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->
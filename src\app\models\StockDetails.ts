import { deserializeAs, serializeAs } from 'cerialize';

export class StockDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('warehouseName')
    @deserializeAs('warehouseName')
    private _warehouseName: string;

    @serializeAs('stockDetailsResponses')
    @deserializeAs('stockDetailsResponses')
    private _stockDetailsResponses: StockDetailsList[];

    @serializeAs('stockItems')
    @deserializeAs('stockItems')
    private _stockItems: StockDetailsList[];

    @deserializeAs('marka')
    private _marka: string;

    @deserializeAs('totalMarkaQty')
    private _totalMarkaQty: number;

    constructor() {
        this.stockDetailsResponses = [];
        this.isActive = false;
        this.isExpand = false;
        this.stockItems = []
    }


    /**
     * Getter totalMarkaQty
     * @return {number}
     */
	public get totalMarkaQty(): number {
		return this._totalMarkaQty;
	}

    /**
     * Setter totalMarkaQty
     * @param {number} value
     */
	public set totalMarkaQty(value: number) {
		this._totalMarkaQty = value;
	}


    /**
     * Getter marka
     * @return {string}
     */
	public get marka(): string {
		return this._marka;
	}

    /**
     * Setter marka
     * @param {string} value
     */
	public set marka(value: string) {
		this._marka = value;
	}


    /**
     * Getter stockItems
     * @return {StockDetailsList[]}
     */
	public get stockItems(): StockDetailsList[] {
		return this._stockItems;
	}

    /**
     * Setter stockItems
     * @param {StockDetailsList[]} value
     */
	public set stockItems(value: StockDetailsList[]) {
		this._stockItems = value;
	}


    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter warehouseName
     * @return {string}
     */
	public get warehouseName(): string {
		return this._warehouseName;
	}

    /**
     * Getter stockDetailsResponses
     * @return {StockDetailsList[]}
     */
	public get stockDetailsResponses(): StockDetailsList[] {
		return this._stockDetailsResponses;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter warehouseName
     * @param {string} value
     */
	public set warehouseName(value: string) {
		this._warehouseName = value;
	}

    /**
     * Setter stockDetailsResponses
     * @param {StockDetailsList[]} value
     */
	public set stockDetailsResponses(value: StockDetailsList[]) {
		this._stockDetailsResponses = value;
	}


}

export class StockDetailsList {

    @serializeAs('cartonQty')
    @deserializeAs('cartonQty')
    private _cartonQty: number;

    @serializeAs('length')
    @deserializeAs('length')
    private _length: number;

    @serializeAs('width')
    @deserializeAs('width')
    private _width: number;

    @serializeAs('height')
    @deserializeAs('height')
    private _height: number;

    @serializeAs('weight')
    @deserializeAs('weight')
    private _weight: number;

    @serializeAs('marka')
    @deserializeAs('marka')
    private _marka: string;

    @serializeAs('totalCartonQty')
    @deserializeAs('totalCartonQty')
    private _totalCartonQty: number;
    
    @serializeAs('looseQty')
    @deserializeAs('looseQty')
    private _looseQty: number;

    @serializeAs('uniteShortCode')
    @deserializeAs('uniteShortCode')
    private _uniteShortCode: string;

    @serializeAs('weightShortCode')
    @deserializeAs('weightShortCode')
    private _weightShortCode: string;

    @serializeAs('totalQty')
    @deserializeAs('totalQty')
    private _totalQty: number;

    @serializeAs('grnGroupLinkId')
    @deserializeAs('grnGroupLinkId')
    private _grnGroupLinkId: number;

    @serializeAs('deleteDocIds')
    @deserializeAs('deleteDocIds')
    private _deleteDocIds: number[];

    @serializeAs('countOfImage')
    @deserializeAs('countOfImage')
    private _countOfImage: number;

    @deserializeAs('location')
    private _location: string;

    @deserializeAs('tag')
    private _tag: string;

    constructor() {
        this.deleteDocIds = []
    }


    /**
     * Getter tag
     * @return {string}
     */
	public get tag(): string {
		return this._tag;
	}

    /**
     * Setter tag
     * @param {string} value
     */
	public set tag(value: string) {
		this._tag = value;
	}
    

    /**
     * Getter location
     * @return {string}
     */
	public get location(): string {
		return this._location;
	}


    /**
     * Getter looseQty
     * @return {number}
     */
	public get looseQty(): number {
		return this._looseQty;
	}

    /**
     * Setter looseQty
     * @param {number} value
     */
	public set looseQty(value: number) {
		this._looseQty = value;
	}


    /**
     * Setter location
     * @param {string} value
     */
	public set location(value: string) {
		this._location = value;
	}


    /**
     * Getter countOfImage
     * @return {number}
     */
	public get countOfImage(): number {
		return this._countOfImage;
	}

    /**
     * Setter countOfImage
     * @param {number} value
     */
	public set countOfImage(value: number) {
		this._countOfImage = value;
	}


    /**
     * Getter deleteDocIds
     * @return {number[]}
     */
	public get deleteDocIds(): number[] {
		return this._deleteDocIds;
	}

    /**
     * Setter deleteDocIds
     * @param {number[]} value
     */
	public set deleteDocIds(value: number[]) {
		this._deleteDocIds = value;
	}


    /**
     * Getter grnGroupLinkId
     * @return {number}
     */
	public get grnGroupLinkId(): number {
		return this._grnGroupLinkId;
	}

    /**
     * Setter grnGroupLinkId
     * @param {number} value
     */
	public set grnGroupLinkId(value: number) {
		this._grnGroupLinkId = value;
	}


    /**
     * Getter totalQty
     * @return {number}
     */
	public get totalQty(): number {
		return this._totalQty;
	}

    /**
     * Setter totalQty
     * @param {number} value
     */
	public set totalQty(value: number) {
		this._totalQty = value;
	}
    

    /**
     * Getter cartonQty
     * @return {number}
     */
	public get cartonQty(): number {
		return this._cartonQty;
	}

    /**
     * Setter cartonQty
     * @param {number} value
     */
	public set cartonQty(value: number) {
		this._cartonQty = value;
	}

    /**
     * Getter length
     * @return {number}
     */
	public get length(): number {
		return this._length;
	}

    /**
     * Setter length
     * @param {number} value
     */
	public set length(value: number) {
		this._length = value;
	}

    /**
     * Getter width
     * @return {number}
     */
	public get width(): number {
		return this._width;
	}

    /**
     * Setter width
     * @param {number} value
     */
	public set width(value: number) {
		this._width = value;
	}

    /**
     * Getter height
     * @return {number}
     */
	public get height(): number {
		return this._height;
	}

    /**
     * Setter height
     * @param {number} value
     */
	public set height(value: number) {
		this._height = value;
	}

    /**
     * Getter weight
     * @return {number}
     */
	public get weight(): number {
		return this._weight;
	}

    /**
     * Setter weight
     * @param {number} value
     */
	public set weight(value: number) {
		this._weight = value;
	}

    /**
     * Getter marka
     * @return {string}
     */
	public get marka(): string {
		return this._marka;
	}

    /**
     * Setter marka
     * @param {string} value
     */
	public set marka(value: string) {
		this._marka = value;
	}

    /**
     * Getter totalCartonQty
     * @return {number}
     */
	public get totalCartonQty(): number {
		return this._totalCartonQty;
	}

    /**
     * Setter totalCartonQty
     * @param {number} value
     */
	public set totalCartonQty(value: number) {
		this._totalCartonQty = value;
	}

    /**
     * Getter uniteShortCode
     * @return {string}
     */
	public get uniteShortCode(): string {
		return this._uniteShortCode;
	}

    /**
     * Setter uniteShortCode
     * @param {string} value
     */
	public set uniteShortCode(value: string) {
		this._uniteShortCode = value;
	}

    /**
     * Getter weightShortCode
     * @return {string}
     */
	public get weightShortCode(): string {
		return this._weightShortCode;
	}

    /**
     * Setter weightShortCode
     * @param {string} value
     */
	public set weightShortCode(value: string) {
		this._weightShortCode = value;
	}
    

}
<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input type="text" class="form-control" placeholder="Search by Container ID" (change)="onSearch($event)"
                    [(ngModel)]="paginationRequest.searchByPoContainer">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter w-25">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input pickerDirective class="form-control" readonly ngxDaterangepickerMd
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true"
                    [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                    [showClearButton]="false" placeholder="Released Date" [autoApply]="true"
                    [showRangeLabelOnInput]="true" startKey="start" endKey="end" [closeOnAutoApply]="true">
            </div>
        </div>
        <div class="form-group form-group-sm">
            <div class="form-group-icon-end">
                <i (click)="e.toggle()" class="th th-outline-calendar-1"></i>
                <input (click)="e.toggle()" (dateSelect)="onChangeDate('delivery')"
                    [(ngModel)]="paginationRequest.temp_expectedDeliveryDate" readonly type="text" class="form-control"
                    placeholder="Delivery Date" ngbDatepicker #e="ngbDatepicker">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeImporter()" placeholder="Importer" [multiple]="false" [clearable]="true"
                [items]="dropdown?.importer" bindLabel="label" bindValue="value"
                [(ngModel)]="paginationRequest.importerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme  table table-bordered tbl-collapse  table-sticky">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Container No
                        </th>
                        <th>Released Date</th>
                        <th>Expected <br /> Delivery Date</th>
                        <th>Cartons</th>
                        <th>Importer</th>
                        <!-- <th>Total Payment</th>
                        <th>Payment Status</th> -->
                        <th>Note</th>
                        <th>Tracking <br/> Link</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="item.isExpand = !item.isExpand">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.containerName}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.releasedDate ? (item.releasedDate | date: 'dd/MM/YYYY h:mm a') : '-'}}</td>
                            <td>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}
                            </td>
                            <td>{{item.totalLoadedCarton ? item.totalLoadedCarton : 0}}</td>
                            <td>{{item.importerName}}</td>
                            <!-- <td>-</td>
                            <td>-</td> -->
                            <td class="tbl-description ws-pre-line">{{item.note ? item.note : '-'}}</td>
                            <td>
                                <a (click)="item.trackingLink ? utilsService.openURL(item.trackingLink) : null"
                                    [ngClass]="{'text-link': item.trackingLink}">{{item.trackingLink ? 'Link' : '-'}}</a>
                            </td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button (click)="onPacking(item.id)"
                                        class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                                            class="bi bi-download "></i>
                                        Download Packing </button>
                                        <div class="dropdown dropdown-no-arrow" 
                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_CONTAINER_NO]) || 
                                                (item.showCartonMapping && utilsService.checkPageAccess([this.utilsService.enumForPage.CARTOM_MAPPING])) ||
                                                (item.isShowExpanse && utilsService.checkPageAccess([this.utilsService.enumForPage.ADD_CONTAINER_EXP])) ||
                                                item.markIsCompleted">
                                                <div ngbDropdown #dropdownAtt="ngbDropdown" autoClose="outside" container="body">
                                                <button class="btn btn-xs btn-light-white btn-icon" ngbDropdownToggle  ngbTooltip="More Option"
                                                        placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-more"></i>
                                                </button>
                                                <ul ngbDropdownMenu class="dropdown-menu" aria-labelledby="actionDropDown" (click)="dropdownAtt.close()">
                                                    <li
                                                        [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_CONTAINER_NO}">
                                                        <a (click)="openLoadedEditCon(item)" class="dropdown-item"><i
                                                                class="th th-outline-edit"></i>Edit Container No
                                                        </a>
                                                    </li>
                                                    <!-- <li *ngIf="item.showIsPrintQrCode">
                                                        <a (click)="qrDownload(item.id)" class="dropdown-item"><i
                                                                class="th th-outline-barcode"></i>Print QR Code</a>
                                                    </li> -->
                                                    <li *ngIf="item.showCartonMapping"
                                                        [pageAccess]="{page: utilsService.enumForPage.SETTINGS, action: utilsService.enumForPage.CARTOM_MAPPING}">
                                                        <a class="dropdown-item" (click)="redirectoToCartonMapping(item.containerId)"><i
                                                                class="th th-outline-box"></i>Carton Mapping
                                                        </a>
                                                    </li>
                                                    <li *ngIf="item.isShowExpanse" [pageAccess]="{page: utilsService.enumForPage.EXPENSES, action: utilsService.enumForPage.ADD_CONTAINER_EXP}">
                                                        <a (click)="redirectToExp(item.containerId)" class="dropdown-item"><i class="th th-outline-add-circle"></i>
                                                            Add Expense
                                                        </a>
                                                    </li>
                                                    <!-- <li><a class="dropdown-item"
                                                            [routerLink]="['/users/purchases/new-payments']"><i
                                                                class="th th-outline-dollar-circle"></i>Make
                                                            Payment</a>
                                                    </li> -->
                                                    <li *ngIf="item.markIsCompleted">
                                                        <a class="dropdown-item" (click)="onMoveToCompleted(item)">
                                                            <i class="th th-outline-add-circle"></i>
                                                            Mark As Completed
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                        <button (click)="item.isExpand = !item.isExpand" class="btn btn-xs text-color btn-icon btn-link ms-auto"
                                            [ngClass]="{'collapse-arrow': item.isExpand}" role="button" [attr.aria-expanded]="item.isExpand">
                                            <i class="th th-outline-arrow-right-3"></i>
                                        </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" #collapse="ngbCollapse" [ngbCollapse]="!item.isExpand" [id]="'table-collapse-2' + i">
                            <td colspan="30" class="p-0 tbl-collapse-child tbl-collapse-child-responsive">

                                <div class="table-responsive">
                                    <table class="table-theme  table table-bordered table-sticky">
                                        <thead class="border-less">

                                            <tr>
                                                <ng-container
                                                    *ngFor="let th of headerObj?.optionsArray | filterByShippingType: item?.shippingTypes?.value; index as k">
                                                    <th *ngIf="th.show" [class]="th.class" [innerHTML]="th.displayName">
                                                    </th>
                                                </ng-container>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                *ngFor="let child of item.poImportItemList; index as l; trackBy: trackByChild">
                                                <ng-container
                                                    *ngFor="let column of headerObj.columnArr | filterByShippingType: item?.shippingTypes?.value;">
                                                    <td class="tbl-user" *ngIf="column.show">
                                                        <ng-container [ngSwitch]="column.key">

                                                            <ng-container *ngSwitchCase="0">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <span>{{(l + 1) | padNum}}.</span>
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image" *ngIf="child?.item">
                                                                            <img *ngIf="child.item?.formattedName"
                                                                                loading="lazy"
                                                                                [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                alt="valamji">
                                                                            <ng-container
                                                                                *ngIf="!child.item?.formattedName">{{
                                                                                child.displayName?.charAt(0).toUpperCase()
                                                                                }}
                                                                            </ng-container>
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{child.item?.skuId}}</p>
                                                                                <span
                                                                                    class="tbl-description">{{child.item.displayName}}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown"
                                                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM])">
                                                                            <button
                                                                                class="btn btn-xs btn-light-white btn-icon"
                                                                                id="actionDropDown"
                                                                                data-bs-toggle="dropdown"
                                                                                aria-expanded="false"
                                                                                data-bs-popper-config='{"strategy":"fixed"}'
                                                                                ngbTooltip="More Option"
                                                                                placement="bottom" container="body"
                                                                                triggers="hover">
                                                                                <i class="th th-outline-more"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu"
                                                                                aria-labelledby="actionDropDown">
                                                                                <li
                                                                                    [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                    <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                        <i class="th th-outline-eye"></i>View Item Details
                                                                                    </a>    
                                                                                </li>
                                                                                <li>
                                                                                    <a class="dropdown-item"
                                                                                        (click)="onStatusOpen(child, item, null)">
                                                                                        <i
                                                                                            class="th th-outline-status"></i>
                                                                                        Change Status
                                                                                    </a>
                                                                                </li>
                                                                                <li *ngIf="child.showAssignedToCHA">
                                                                                    <a class="dropdown-item" (click)="onAssignToCHA(child, item)">
                                                                                        <i class="th th-outline-add-circle"></i>
                                                                                        Assign carton to CHA
                                                                                    </a>
                                                                                </li>
                                                                            </ul>

                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex flex-row align-items-center justify-content-end ms-auto mt-1">
                                                                    <button *ngIf="child.isDoneCartonMapping"
                                                                        [ngClass]="{'text-success': child.isDoneCartonMapping == 'Completed', 'text-warning': child.isDoneCartonMapping == 'In-Progress'}"
                                                                        class="btn btn-xs btn-icon btn-transparent"
                                                                        [ngbTooltip]="child.isDoneCartonMapping === 'Completed' ? 'Carton Mapping Completed' : 'Carton Mapping Pending'"
                                                                        placement="left" container="body" triggers="hover">
                                                                        <i class="th th-bold-box-tick"></i>
                                                                    </button>
                                                                    <button *ngIf="child.isQRGenerated" class="btn btn-xs btn-icon btn-transparent text-primary" ngbTooltip="QR Generated"
                                                                        placement="left" container="body" triggers="hover">
                                                                        <i class="bi bi-qr-code"></i>
                                                                    </button>
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="1">
                                                                <span class="d-flex flex-column align-items-start">
                                                                    <div>{{child.marka ? child.marka : '-'}}</div>
                                                                    <div>
                                                                        <ng-container>
                                                                            {{child.cartonLength ? child.cartonLength :
                                                                            '-'}} X
                                                                            {{child.cartonWidth ? child.cartonWidth :
                                                                            '-'}} X
                                                                            {{child.cartonHeight ? child.cartonHeight :
                                                                            '-'}}
                                                                            {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                        </ng-container>
                                                                    </div>
                                                                    <div>
                                                                        <p>{{child.pricePerCarton ? child.pricePerCarton
                                                                            : '-'}}</p>
                                                                    </div>
                                                                    <div>
                                                                        <p class="tbl-po-notes">{{child.chinaComment ?
                                                                            child.chinaComment : ''}}</p>
                                                                    </div>
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="2">
                                                                <span class="w-100 d-block"
                                                                    *ngFor="let v of child.colorName">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v
                                                                    : ''}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="3">
                                                                <div class="tbl-po-notes">
                                                                    {{child.note ? child.note : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="4">
                                                                <div>
                                                                    -
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="5">
                                                                <div>
                                                                    {{child.poDate ?
                                                                    (child.poDate |
                                                                    date: 'dd/MM/YYY') : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="6">
                                                                <div>
                                                                    {{child.expectedDeliveryDate ?
                                                                    (child.expectedDeliveryDate | date: 'dd/MM/YYYY') :
                                                                    '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="7">
                                                                <div>
                                                                    {{child.PoNo ? (child.PoNo) : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="8">
                                                                <div>
                                                                    {{child.poCarton ? (child.poCarton) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="9">
                                                                <div>
                                                                    {{child.rcCarton ? (child.rcCarton) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="10">
                                                                <span class="w-100 d-block"
                                                                    *ngFor="let v of (child.rcId)">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v
                                                                    : ''}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="11">
                                                                <div>
                                                                    {{child.pricePerCarton ? child.pricePerCarton : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="12">
                                                                <div>
                                                                    {{child.releasedQty ? (child.releasedQty) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="13">
                                                                <div>
                                                                    {{child.PendingReleasedQtyCarton ?
                                                                    (child.PendingReleasedQtyCarton) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="14">
                                                                <div>
                                                                    {{child.PendingReleasedQtyCartonPcs ?
                                                                    (child.PendingReleasedQtyCartonPcs) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="15">
                                                                <div>
                                                                    {{child.grnQty ? (child.grnQty) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="16">
                                                                <div>
                                                                    {{child.totalGrnQty ? (child.totalGrnQty) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="41">
                                                                <div>
                                                                    {{child.assignToCHAQty ? (child.assignToCHAQty) : 0}}
                                                                </div>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="42">
                                                                <div>
                                                                    {{child.PendingGrnQty ? (child.PendingGrnQty) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="17">
                                                                <div>
                                                                    {{child.pricePerItem ? child.pricePerItem : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="18">
                                                                <div>
                                                                    {{child.Total_Price ? (child.Total_Price |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="19">
                                                                <div>
                                                                    -
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="20">
                                                                <div>
                                                                    {{child.totalAmountWithExp ?
                                                                    (child.totalAmountWithExp | indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="21">
                                                                <div>
                                                                    {{child.totalAmountWithExpInINR ?
                                                                    (child.totalAmountWithExpInINR | indianCurrency) :
                                                                    '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="22">
                                                                <div>
                                                                    {{child.chinaFinalExpextedCode ?
                                                                    (child.chinaFinalExpextedCode) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="23">
                                                                <div>
                                                                    {{item.shippingTypes ? (item.shippingTypes.value) :
                                                                    '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="24">
                                                                <div>
                                                                    {{child.cartonLength ? child.cartonLength : '-'}} x
                                                                    {{child.cartonWidth ? child.cartonWidth : '-'}}
                                                                    x
                                                                    {{child.cartonHeight ? child.cartonHeight : '-'}}
                                                                    {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="25">
                                                                <div>
                                                                    {{child.dimAge}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="26">
                                                                <div>
                                                                    {{child.cbmCarton ? child.cbmCarton : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="27">
                                                                <div>
                                                                    {{child.totalCbm ? child.totalCbm : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="28">
                                                                <div>
                                                                    {{item.cbmPrice ? (item.cbmPrice | indianCurrency)
                                                                    : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="29">
                                                                <div>
                                                                    {{child.totalCBMExpense ? (child.totalCBMExpense |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="30">
                                                                <div>
                                                                    {{child.shippingExpense ? (child.shippingExpense |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="31">
                                                                <div>
                                                                    {{child.transportCharges ? (child.transportCharges |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="32">
                                                                <div>
                                                                    {{child.TotalTransportationCharges ?
                                                                    (child.TotalTransportationCharges | indianCurrency)
                                                                    : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="33">
                                                                <div>
                                                                    {{child.Transportationcharges_Mumbai_to_Surat ?
                                                                    (child.Transportationcharges_Mumbai_to_Surat |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="34">
                                                                <div>
                                                                    {{child.TotalInsurance ? (child.TotalInsurance |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="35">
                                                                <div>
                                                                    {{child.insurancePCS ? (child.insurancePCS |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="36">
                                                                <div>
                                                                    {{child.GSTAmount_PCS ? (child.GSTAmount_PCS |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="45">
                                                                <div>
                                                                    {{child.gstPer ? (child.gstPer ): 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="46">
                                                                <div>
                                                                    {{child.gst_amount ? (child.gst_amount |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="37">
                                                                <div>
                                                                    {{child.craneExpense ? (child.craneExpense |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="38">
                                                                <div>
                                                                    {{child.CraneExpense_PCS ? (child.CraneExpense_PCS |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="39">
                                                                <div>
                                                                    {{child.Total_Expense ? (child.Total_Expense |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="40">
                                                                <div>
                                                                    {{child.ChinaToSuratFinalPrice ?
                                                                    (child.ChinaToSuratFinalPrice | indianCurrency) :
                                                                    0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeight'">
                                                                    {{child.Weight_Carton ?
                                                                    child.Weight_Carton : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalWeight'">
                                                                    {{child.total_Weight ? child.total_Weight : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeightRu'">
                                                                    {{child.Weight_kg ?
                                                                    child.Weight_kg : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalLoadAmt'">
                                                                    {{child.total_load_amt_Ru ? child.total_load_amt_Ru
                                                                    : 0}}
                                                                </ng-container>
                                                                <ng-container
                                                                    *ngIf="column.key =='shippingCostperPieceINR'">
                                                                    {{child.shipingCost ? (child.shipingCost |
                                                                    indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container
                                                                    *ngIf="column.key =='totalShippingExpWeight'">
                                                                    {{child.totalShippingExpense ?
                                                                    (child.totalShippingExpense | indianCurrency) : 0}}
                                                                </ng-container>
                                                            </ng-container>

                                                            <ng-container>

                                                                <ng-container *ngIf="column.key =='percentage'">
                                                                    {{child.Percentage ? child.Percentage :
                                                                    0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalExpPCSper'">
                                                                    {{child.totalExpense ?
                                                                    (child.totalExpense | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container
                                                                    *ngIf="column.key =='totalFinalCostPCSper'">
                                                                    {{child.totalFinalCost ?
                                                                    (child.totalFinalCost | indianCurrency) : 0}}
                                                                </ng-container>

                                                            </ng-container>

                                                            <ng-container *ngIf="column.key =='expensePcs'">
                                                                {{child.Expense_PCS ?
                                                                (child.Expense_PCS | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalItemAmt'">
                                                                {{child.totalItemAmount ? (child.totalItemAmount |
                                                                indianCurrency) :
                                                                0}}
                                                            </ng-container>

                                                            <ng-container>
                                                                <ng-container *ngIf="column.key ==50">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key ==51">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key === 43">
                                                                {{child.purchaseRatio ?
                                                                child.purchaseRatio : 0}}
                                                            </ng-container>

                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>

                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData">
    </app-pagination>
</div>
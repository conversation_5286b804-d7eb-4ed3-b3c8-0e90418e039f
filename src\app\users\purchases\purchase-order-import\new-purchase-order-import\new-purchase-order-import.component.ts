import { DatePipe } from "@angular/common";
import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, ViewChild } from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { ImageViewModalComponent } from "@common/image-view-modal/image-view-modal.component";
import { EnumForPOImportOrderFor } from "@enums/EnumForPOImportOrderFor.enum";
import { EnumForPOImportStatus } from "@enums/EnumForPOImportStatus.enum";
import { EnumForRegUserType } from "@enums/EnumForRegUserType";
import { EnumForShippingType } from "@enums/EnumForShippingType.enum";
import { ItemImageObj } from "@modal/ItemImageObj";
import { POImport } from "@modal/POImport";
import { POImportComment } from "@modal/POImportComment";
import { POImportItem } from "@modal/POImportItem";
import { UtilsService } from "@service/utils.service";
import { Serialize, Deserialize } from "cerialize";
import dayjs from "dayjs";
import moment from "moment";
import { Subscription } from "rxjs";
import { PoImportDropdown } from "src/app/shared/constants/interface";
declare var window: any;

@Component({
  selector: 'app-new-purchase-order-import',
  templateUrl: './new-purchase-order-import.component.html',
  styleUrls: ['./new-purchase-order-import.component.scss']
})
export class NewPurchaseOrderImportComponent implements OnInit, OnDestroy {


  enumForOrderFor = EnumForPOImportOrderFor

  //Main
  poStatus: string;
  isSupplierAlreadyPresent: boolean = false;
  poImportForm: FormGroup;
  poImportObj = new POImport();
  dropdown: PoImportDropdown;
  poImportId: number;

  enumForStatus = EnumForPOImportStatus
  selectedTab: string = this.enumForStatus.DRAFT;
  enumForShippingType = EnumForShippingType

  //date
  maxDateNGB: any;
  minDateNGB: any;

  supplierChangeModal: any;
  oldSupplierId: any;

  //Items
  associatedItems: POImportItem[] = [];
  selectedItemIndex: number;
  associateItemPODeleteModal: any;
  itemObj = new POImportItem(); 

  //Subs
  conversationRateSub: Subscription;
  cbmPriceSub: Subscription;
  shippingTypesSub: Subscription;

  //comment
  commentObj = new POImportComment();
  deleteCommentObj = new POImportComment()
  commentList: POImportComment[] = [];
  selectedCommentIndex: number;
  isEditPhase: boolean = false;
  deleteCommentPoModal: any;

  //ItemLink
  linkedItemList: POImportItem[] = []
  LinkItemModal: any;
  selectedIds: any[] = [];
  flagForSelectAll: boolean = false;
  selectedItem = new POImportItem();

  //warning
  saveWarningPOModal: any;
  isWarningModal: boolean = false;
  isDraftFlag: boolean = false;
  isDateError: boolean = false;

  convertToPo:boolean = false;

  //From Registration
  fromReg: boolean = false;
  regItemIds: number[] = []

  /// Modal Image
  productmodalSliderPOModal: any;
  showSliderModal: boolean;
  itemImagesList: ItemImageObj[];
  currentItem = new ItemImageObj();
  selectedImageIndex: number;
  isCopied: boolean = false;
  timer: any = null;
  @ViewChild(ImageViewModalComponent) ImageViewModalComponent: ImageViewModalComponent;

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private route: ActivatedRoute) {

    if (Number(this.route.snapshot.paramMap.get('id'))) {
      this.poImportId = Number(this.route.snapshot.paramMap.get('id'));
      this.selectedTab = this.enumForStatus.DRAFT
    }
    if (Number(this.route.snapshot.paramMap.get('poCreatedId'))) {
      this.poImportId = Number(this.route.snapshot.paramMap.get('poCreatedId'));
      this.selectedTab = this.enumForStatus.PO_CREATED
    }

    if (Number(this.route.snapshot.paramMap.get('convertToPoId'))) {
      this.poImportId = Number(this.route.snapshot.paramMap.get('convertToPoId'));
      this.convertToPo = true;
      this.selectedTab = this.enumForStatus.DRAFT
    }

    this.maxDateNGB = { year: dayjs().year(), month: dayjs().month() + 1, day: dayjs().date() };
  }

  ngOnInit(): void {

    this.formGroup();

    if(!this.poImportId) {
      this.getFromRegistration();
      this.poStatus = Serialize(this.utilsService.poImportStatus.DRAFT)
      // this.getRequiredData(null);
    }

    // Redirected from Reg
    if (this.poImportId) {
      this.getDataById()

      this.poImportForm.get('supplierId').enable();

      // disable incase edit po
      if (this.selectedTab === this.enumForStatus.PO_CREATED) {
        this.poImportForm.get('supplierId').disable();
      }
      this.poImportForm.get('supplierId').updateValueAndValidity();
    }

    this.associateItemPODeleteModal = new window.bootstrap.Modal(
      document.getElementById('associateItemPODeleteModal')
    );

    this.deleteCommentPoModal = new window.bootstrap.Modal(
      document.getElementById('deleteCommentPoModal')
    );

    this.supplierChangeModal = new window.bootstrap.Modal(
      document.getElementById('supplierChangeModal')
    );

    document.getElementById('supplierChangeModal').addEventListener('hidden.bs.modal', () => {
      this.poImportObj.supplierId = Serialize(this.oldSupplierId)
    });

    this.LinkItemModal = new window.bootstrap.Modal(
      document.getElementById('LinkItemModal')
    );

    this.saveWarningPOModal = new window.bootstrap.Modal(
      document.getElementById('saveWarningPOModal')
    );

    /// image modal
    this.productmodalSliderPOModal = new window.bootstrap.Modal(
      document.getElementById('productmodalSliderPOModal')
    );

    document.getElementById('productmodalSliderPOModal').addEventListener('hidden.bs.modal', () => {
      this.showSliderModal = false;
    });

    // Image Slider init slick when opening modal
    document.getElementById('productmodalSliderPOModal').addEventListener('shown.bs.modal', () => {
      this.ImageViewModalComponent.reInitSlick()
    });

    this.cbmPriceSub = new Subscription();
    this.cbmPriceSub = this.poImportForm.get('cbmPrice').valueChanges.subscribe(a => {
      if (this.associatedItems.length > 0) {
        this.associatedItems.forEach((v, i) => {
          this.onCalPOCartonPCSCartonC(i);
        });
      }
    })
    
    this.conversationRateSub = new Subscription()
    this.conversationRateSub = this.poImportForm.get('conversationRate').valueChanges.subscribe(a => {
      if(this.associatedItems.length > 0) {
        this.associatedItems.forEach((v,i) => {
          this.onCalPOCartonPCSCartonC(i)
        })
      }
    })

    this.shippingTypesSub = new Subscription();
    this.shippingTypesSub = this.poImportForm.get('shippingTypes').valueChanges.subscribe(a => {
      if (this.associatedItems.length > 0) {
          for (let i = 0; i < this.associatedItems.length; i++) {
            const item = this.associatedItems[i];

            item.cbmPerCarton = null;
            item.totalCbm = null;
            item.totalCBMExpenseINR = null;
            item.shippingExpPerPCS = null;

            item.totalWeight = null;
            item.cartonWeightRu = null;
            item.totalLoadAmt = null;
            item.shippingCostperPieceINR = null;
            item.totalShippingExpWeight = null;

            item.percentage = null;
            item.expensePcs = null;
            item.totalExpPCSper = null;
            item.totalFinalCostPCSper = null;

            this.changeShippingTypeValidation(i);
          }
        this.associatedItems.forEach((_, i) => {
          this.onCalPOCartonPCSCartonC(i);
        });
      }
    })
  }

  changeShippingTypeValidation = (i: number) => {
    // remove validators other field
    switch (this.poImportForm.get('shippingTypes').value) {
      case this.enumForShippingType.WEIGHT:
        this.items.at(i).get('cartonWeightRu').addValidators(Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]));
        this.items.at(i).get('percentage').clearValidators()
        this.items.at(i).get('expensePcs').clearValidators()
        break;
      case this.enumForShippingType.PERCENTAGE:
        this.items.at(i).get('percentage').addValidators(Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]));
        this.items.at(i).get('cartonWeightRu').clearValidators()
        this.items.at(i).get('expensePcs').clearValidators()
        break;
      case this.enumForShippingType.PIECE:
        this.items.at(i).get('expensePcs').addValidators(Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]));
        this.items.at(i).get('cartonWeightRu').clearValidators()
        this.items.at(i).get('percentage').clearValidators()
        break;
      case this.enumForShippingType.CBM:
        ['percentage', 'cartonWeightRu', 'expensePcs'].forEach(v => {
          this.items.at(i).get(v).clearValidators()
          this.items.at(i).get(v).updateValueAndValidity();
        })
      break;
      default:
        break;
    }
    this.items.at(i).get('cartonWeightRu').updateValueAndValidity();
    this.items.at(i).get('percentage').updateValueAndValidity();
    this.items.at(i).get('expensePcs').updateValueAndValidity();
  }

  ngOnDestroy(): void {
    this.cbmPriceSub.unsubscribe();
    this.conversationRateSub.unsubscribe();
    this.shippingTypesSub.unsubscribe();

    //
    localStorage.removeItem('registration')
  }

  formGroup() {
    this.poImportForm = this.fb.group({
      customerId: [null],
      supplierId: [null, Validators.compose([Validators.required])],
      orderType: [null, Validators.compose([Validators.required])],
      mobileNo: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      mobileExtension: [null],
      deliveryWarehouseId: [null, Validators.compose([Validators.required])],
      bankGroupId: [null],
      conversationRateCurrencyId: [null],
      conversationRate: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      poImportDate: [null, Validators.compose([Validators.required])],
      poLimit: [null],
      advancePayByCustomerCurrencyId: [null],
      advancePayByCustomer: [null],
      paymentTermsId: [null, Validators.compose([Validators.required])],
      shippingTypes: [null],
      cbmPriceCurrencyId: [null],
      cbmPrice: [null],
      tags: [null],
      notes: [null],
      status: [null],
      items: this.fb.array([])
    })
  }

  getRequiredData(suppId: number) {

    let API = null
    this.dropdown = null;

    API = `${this.utilsService.serverVariableService.REQ_DATA_PO_IMPORT}?isFlag=${this.selectedTab === this.enumForStatus.DRAFT}`
    if (suppId) {
      API = `${this.utilsService.serverVariableService.REQ_DATA_PO_IMPORT}?supplierId=${suppId}&isFlag=${this.selectedTab === this.enumForStatus.DRAFT}`
    }

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.currencyAdvCus = Serialize(this.dropdown.currency);
        this.dropdown.currencyCBM = Serialize(this.dropdown.currency);
        this.dropdown.supplierDropdown = Serialize(this.dropdown.registration.filter(a => a.type !== EnumForRegUserType.CUSTOMER && a.supplierType?.value === 'CHINA'))
        this.dropdown.customerDropdown = Serialize(this.dropdown.registration.filter(a => a.type !== EnumForRegUserType.SUPPLIER))
        Object.keys(this.dropdown).filter(v => v !== 'orderType' && v !== 'shippingType' && v !== 'status' && Array.isArray(this.dropdown[v])).forEach(key => {
          if (this.dropdown[key]) {
            this.dropdown[key] = this.utilsService.transformDropdownItems(this.dropdown[key]);
          }
        });
      
        //initial
        if (!this.poImportObj.cbmPrice) {
          this.poImportObj.cbmPrice = response.cbmPrice;
        }
        if (!this.poImportObj.orderType) {
          this.poImportObj.orderType = this.enumForOrderFor.GENERAL;
        }
        if (!this.poImportObj.poImportDate) {
          this.poImportObj.temp_date = {
            year: moment().year(),
            month: moment().month() + 1,
            day: moment().date()
          }
        }
        if(!this.poImportObj.conversationRate) {
          this.poImportObj.conversationRate = response.convRate;      
        }
        
        if (!this.poImportObj.conversationRateCurrencyId) {
          this.poImportObj.conversationRateCurrencyId = this.dropdown.currency.find(a => a.label.toLowerCase() === ('RS').toLowerCase())?.value
        }

        // CBM Default Select
        if (!this.poImportObj.shippingTypes) {
          this.poImportForm.get('shippingTypes').setValue(this.enumForShippingType.CBM);
          this.onChangeShipmentType();
        }

        ///existing supplier
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response.poImportResponse)) {
          this.setObjData(response.poImportResponse, false)
          this.isSupplierAlreadyPresent = true;
        }

        if (this.poImportId) {
          // Used for edit po created settings dropdown data
          this.associatedItems.forEach((v, i) => {
            //dropdown
            const dropdown = (this.dropdown?.item.find(a => a.associatedIds === v.associatedIds)?.associatedItemColors)
            v.associatedItemColors = Serialize(dropdown)
            
            v.checkList = this.utilsService.filterIsActiveLV(this.dropdown.checkList, v.qcCheckListId ? v.qcCheckListId : null);
            v.unitMaster = (this.dropdown?.item || []).find(a => a.associatedIds === v.associatedIds)?.unitMaster || null
            v.itemBoxDimArr = this.utilsService.filterIsActive(this.dropdown?.unitMaster || [], v.itemWithBoxDimUnitMaster ?? null).filter(d => d?.unitMasterCategory?.value == 'LENGTH');
            v.itemDimArr = this.utilsService.filterIsActive(this.dropdown?.unitMaster || [], v.itemDimUnitMaster ?? null).filter(d => d?.unitMasterCategory?.value == 'LENGTH');
            let packingDropdown = (this.dropdown?.item.find(a => a.associatedIds === v.associatedIds)?.packingTypes);
            packingDropdown = (packingDropdown || []).map(item => item.packingMasterId)
            v.packingTypes = this.utilsService.filterIsActiveMultipleAndContains(this.dropdown?.packingMaster, packingDropdown ?? null);
            this.onCalPOCartonPCSCartonC(i)
          })
        }
        //Dropdown disabled value checking
        this.dropdown.warehouse = this.utilsService.filterIsActive(this.dropdown?.warehouse, this.poImportObj.deliveryWarehouseId ? this.poImportObj.deliveryWarehouseId : null);
        this.dropdown.bankGroup = this.utilsService.filterIsActive(this.dropdown?.bankGroup, this.poImportObj.bankGroupId ? this.poImportObj.bankGroupId : null);
        this.dropdown.paymentTerms = this.utilsService.filterIsActiveLV(this.dropdown?.paymentTerms, this.poImportObj.paymentTermsId ? this.poImportObj.paymentTermsId : null);
        this.dropdown.customerDropdown = this.utilsService.filterIsActive(this.dropdown?.customerDropdown, this.poImportObj.customerId ? this.poImportObj.customerId : null);
        this.dropdown.supplierDropdown = this.utilsService.filterIsActive(this.dropdown?.supplierDropdown, this.poImportObj.supplierId ? this.poImportObj.supplierId : null);
        this.dropdown.Country = this.utilsService.filterIsActiveLV(this.dropdown?.Country, this.poImportObj.mobileExtensionId ? this.poImportObj.mobileExtensionId : null);
        this.dropdown.currency = this.utilsService.filterIsActiveLV(this.dropdown?.currency, this.poImportObj.conversationRateCurrencyId ? this.poImportObj.conversationRateCurrencyId : null);
        this.dropdown.currencyAdvCus = this.utilsService.filterIsActiveLV(this.dropdown?.currencyAdvCus, this.poImportObj.advancePayByCustomerCurrencyId ? this.poImportObj.advancePayByCustomerCurrencyId : null);
        this.dropdown.currencyCBM = this.utilsService.filterIsActiveLV(this.dropdown?.currencyCBM, this.poImportObj.cbmPriceCurrencyId ? this.poImportObj.cbmPriceCurrencyId : null);

        //From Registration
        if (this.fromReg) {
          this.setRegRedirectData()
        }
      }
    })
  }

  getDataById() {

    let API = null;
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        API = this.utilsService.serverVariableService.PO_IMPORT_GET_DATA
        break;
      case this.enumForStatus.PO_CREATED:
        API = this.utilsService.serverVariableService.PO_IMPORT_GET_DATA
        break;

      default:
        break;
    }

    this.utilsService.getMethodAPI(false, `${API}?id=${this.poImportId}&isFlag=${this.selectedTab === this.enumForStatus.DRAFT}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.setObjData(response, true);
        this.getRequiredData(this.poImportObj.supplierId ? this.poImportObj.supplierId : null);
      }
    })
  }

  onSelectAttachments(event): void {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if (this.poImportObj.poImportDocList.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.poImportObj.poImportDocList.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.FILE_INVALID_EXTENSION);
        }
      });
    }
  }

  removeAttachment(i: number, file) {
    this.poImportObj?.poImportDocList?.splice(i, 1)
    if (file.id) {
      this.poImportObj.deletedDocsID.push(file.id)
    }
  }

  openLink(link, newUpload: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  onSelectSupplier() {

    //warning if change supplier, item will be removed
    if (this.items.length > 0) {
      this.openSupplierChangeWarningModal();
    }
    else {
      this.removeOldValueIfSupplierAlreadyPresent();
      this.poImportObj.mobileNo = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId).phone;
      this.poImportObj.mobileExtensionId = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId)?.countryId;
      this.getRequiredData(this.poImportObj.supplierId)
    }
  }

  addValidationAfterSupplierSelect() {
    const validations = {
      supplierId: [Validators.required],
      orderType: [Validators.required],
      mobileNo: [Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)],
      mobileExtension: [],
      deliveryWarehouseId: [Validators.required],
      bankGroupId: [],
      conversationRateCurrencyId: [],
      conversationRate: [Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)],
      poImportDate: [Validators.required],
      paymentTermsId: [Validators.required],
    };
    Object.keys(validations).forEach(field => {
      this.poImportForm.get(field).setValidators(validations[field]);
      this.poImportForm.get(field).updateValueAndValidity();
    });
  }

  removeOldValueIfSupplierAlreadyPresent() {
    this.poImportObj.poImportDocList = []
    this.poImportObj.id = null;
    this.commentList = []
    this.poImportObj.subTotalRmb = 0;
    this.poImportObj.subTotalRupees = 0;
    this.poImportObj.gstAmountRmb = 0;
    this.poImportObj.gstAmountRupees = 0;
    this.poImportObj.grandTotalRmb = 0;
    this.poImportObj.grandTotalRupees = 0;
    const firstStepFields = ['mobileNo', 'deliveryWarehouseId', 'bankGroupId', 'poLimit', 'advancePayByCustomerCurrencyId',
      'advancePayByCustomer', 'paymentTermsId', 'shippingTypes', 'cbmPriceCurrencyId', 'cbmPrice', 'tags', 'notes', 'items'];
    firstStepFields.map(field => this.poImportForm.get(field).clearValidators());
    firstStepFields.map(field => this.poImportForm.get(field).updateValueAndValidity());
    firstStepFields.map(field => this.poImportForm.get(field).reset());

    this.addValidationAfterSupplierSelect();
  }

  // Supplier dropdown storing old value
  captureOldValue() {
    this.oldSupplierId = Serialize(this.poImportObj.supplierId)
  }

  openSupplierChangeWarningModal() {
    this.supplierChangeModal.show();
  }

  // Supplier changed functionality, (confirm from modal)
  supplierChanged() {
    this.items.clear();
    this.associatedItems = [];
    this.supplierChangeModal.hide();

    this.removeOldValueIfSupplierAlreadyPresent();

    this.oldSupplierId = Serialize(this.poImportObj.supplierId)

    this.poImportObj.mobileNo = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId).phone;
    this.poImportObj.mobileExtensionId = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId)?.countryId;
    this.getRequiredData(this.poImportObj.supplierId)
  }

  // Order type change functionality
  onChangeOrderFor() {
    this.poImportObj.advancePayByCustomerCurrencyId = null;
    this.poImportObj.customerId = null;
    this.poImportObj.advancePayByCustomer = null;
    this.poImportForm.get('advancePayByCustomerCurrencyId').reset();
    this.poImportForm.get('advancePayByCustomerCurrencyId').clearValidators();
    this.poImportForm.get('customerId').reset();
    this.poImportForm.get('customerId').clearValidators();
    this.poImportForm.get('advancePayByCustomer').reset();
    this.poImportForm.get('advancePayByCustomer').clearValidators();
    if (this.poImportObj.orderType === this.enumForOrderFor.Customer) {
      // this.poImportForm.get('advancePayByCustomerCurrencyId').setValidators([Validators.compose([Validators.required])]);
      this.poImportForm.get('customerId').setValidators([Validators.compose([Validators.compose([Validators.required])])]);
      this.poImportObj.advancePayByCustomerCurrencyId = this.dropdown.currencyAdvCus.find(a => a.label.toLowerCase() === ('RS').toLowerCase()).value
    }
    this.poImportForm.get('customerId').updateValueAndValidity();
    this.poImportForm.get('advancePayByCustomerCurrencyId').updateValueAndValidity();
    this.poImportForm.get('advancePayByCustomer').updateValueAndValidity();
  }

  // Shipment type change functionality, reset associated items carton weight, percentage, expense pcs fields
  onChangeShipmentType() {
    this.associatedItems = this.associatedItems.map(v => {
      v.cartonWeightRu = null
      v.percentage = null
      v.expensePcs = null
      return v;
    })
  }

  onSavePOImport(isDraft: boolean, fromWarning?: boolean) {

    const formData = new FormData();

    // Form validation and scroll to first invalid field in associated items
    if (this.poImportForm.invalid) {
      this.poImportForm.markAllAsTouched();
      const invalidItem = this.items.controls.find((control) => control.invalid);
      if (invalidItem) {
        const index = this.items.controls.indexOf(invalidItem);
        const invalidControl = invalidItem as FormGroup;
        const invalidFieldName = Object.keys(invalidControl.controls).find(key =>
          invalidControl.get(key)?.invalid
        );

        if (invalidFieldName) {
          let targetElement = document.getElementById(`${invalidFieldName}-${index}`);
          if (!targetElement) {
            targetElement = document.querySelector(`[formarrayname="items"] > div:nth-child(${index + 1})`);
          }

          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'auto',
              block: 'nearest',
              inline: 'center'
            });
          }
        }
      }
      return;
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.associatedItems.length === 0)) {
      this.utilsService.toasterService.error('Minimum One Item is required.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const checkItem = (this.associatedItems.filter(a => !a.isDraft) || [])
    if (this.convertToPo && checkItem.length === 0) {
      this.utilsService.toasterService.error('At least one item that is not reverted to draft must be selected to move to PO created.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (!this.utilsService.isEverythingUnique(this.associatedItems, 'associatedIds')) {
      this.utilsService.toasterService.error('Items should be unique.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const fileRes = {}

    if(this.poImportObj?.poImportDocList) {
      const obj = this.poImportObj.poImportDocList.filter(a => !a.id).map((v, i) => {
        if (v.file) {
          if (v.isMarkDefault) {
            fileRes[i] = v.file.name;
            formData.set('poDocMapping', JSON.stringify(fileRes));
          }
          formData.append('poImportDoc', v.file);
        }
      })
    }

    if (this.poImportObj.temp_tags) {
      this.poImportObj.tags = Serialize((this.poImportObj.temp_tags.toString()))
    }

    if (this.associatedItems) {
      this.poImportObj.poItemReq = Serialize((this.associatedItems))
    }

    const date = new Date(this.poImportObj.temp_date.year, this.poImportObj.temp_date.month - 1, this.poImportObj.temp_date.day)
    this.poImportObj.poImportDate = new DatePipe('en-US').transform(date, 'yyyy-MM-dd')
    
    if (this.commentList) {
      const obj = this.commentList.map(a => ({
        userId: a.userId,
        id: a.id,
        comments: a.comments
      }));
      this.poImportObj.poCommentsReq = Serialize(obj);
    }

    ////
    const unlinkedItems = this.associatedItems.filter(a => !a.isLink);
    if (unlinkedItems.length > 0) {
      const hasDecimal = unlinkedItems.some(a => !Number.isInteger(a.poCarton));

      if (hasDecimal) {
        this.utilsService.toasterService.error('PO Carton should be non decimal for unlinked items.', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }
    }

    const uniqueGroups = new Set<string>();
    for (const item of this.associatedItems) {
      if (item.isLink) {
        const key = item.linkIds.sort((a, b) => a - b).join(',');
        uniqueGroups.add(key);
      }
    }
    const groupedArray = Array.from(uniqueGroups).map(group => group.split(',').map(Number));
    const arrayObject = groupedArray.map((item) => { 
      return { associatedItemIds: item};
    });

    let errorItemLink = false;
    arrayObject.forEach((a) => {
      let sum = 0;
      let itemNames: string[] = [];

      a.associatedItemIds.forEach(itemId => {
        const associatedItem = this.associatedItems.find(item => item.associatedIds === itemId);
        if (associatedItem) {
          sum += associatedItem.poCarton;
          itemNames.push(associatedItem.displayName);
        }
      });

      if (sum !== 1 && this.associatedItems.some(a => a.isLink)) {
        let errMsg = `PO Carton Sum is not 1 for items: ${itemNames.join(', ')}`
        this.utilsService.toasterService.error(errMsg, '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        errorItemLink = true;
      }
    });

    if (errorItemLink) {
      return;
    }
    
    if(!this.utilsService.isEmptyObjectOrNullUndefined(arrayObject)) {
      this.poImportObj.linkPoImportItems = JSON.stringify(!this.utilsService.isEmptyObjectOrNullUndefined(arrayObject) ? arrayObject : null)
    } else {
      this.poImportObj.linkPoImportItems = null;
    }
    // checking is any items in list is converting to draft
    this.poImportObj.isDraft = this.associatedItems.some(a => a.isDraft === true);

    let finalItems = Serialize(this.associatedItems) as POImportItem[]
    finalItems = finalItems.filter(a => !a.isDraft)
    ///

    /////////
    if (isDraft) {
      this.poImportObj.status = this.utilsService.poImportStatus.DRAFT;

      // this.poImportForm.controls['conversationRate'].removeValidators(Validators.required);;
      // this.poImportForm.controls['conversationRate'].updateValueAndValidity();
    }
    else {
      this.poImportObj.status = this.utilsService.poImportStatus.PO_CREATED;

      // this.poImportForm.controls['conversationRate'].setValidators(Validators.required);
      // this.poImportForm.controls['conversationRate'].updateValueAndValidity();

      // if (this.poImportForm.controls['conversationRate'].invalid) {
      //   this.poImportForm.controls['conversationRate'].markAllAsTouched();
      //   return;
      // }
    };
    ///////////


    ///// 
    // Warning Modal

    if(!fromWarning && !isDraft) {

      this.isDateError = false;

      if (!this.utilsService.isEmptyObjectOrNullUndefined(this.poImportObj.poImportDate)) {
        const poImportDate = moment(this.poImportObj.poImportDate, 'YYYY/MM/DD').startOf('day');
        const today = moment().startOf('day');
      
        if (poImportDate.isBefore(today)) {
          this.isDraftFlag = isDraft;
          this.isDateError = true;
          this.openWarningPOModal();
          return;
        }
      }

      if(this.poImportForm.get('poLimit').value) {
        if(Number(this.poImportObj.grandTotalRmb) > Number(this.poImportForm.get('poLimit').value)) {
          this.isDraftFlag = isDraft;
          this.isDateError = false;
          this.openWarningPOModal();
          return;
        }
      }
    }

    // Warning
    /////

    const param = Serialize(this.poImportObj) as POImport;
    param.poItemReq = Serialize(finalItems)
    if (this.poImportObj.poImportDocList) {
      param.docId = this.poImportObj.poImportDocList.map(a => a?.id).filter(id => id != null);
    }

    formData.set('poImportInfo', JSON.stringify(param));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_IMPORT_SAVE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.utilsService.redirectTo('/users/purchases/po-import/')
        if(fromWarning) {
          this.saveWarningPOModal.hide();
        }
      }
    })

  }

  // Items

  get items() {
    return (this.poImportForm.get('items') as FormArray);
  }

  addItem() {
    const group = this.fb.group({
      itemId: [null, Validators.compose([Validators.required])],
      marka: [null],
      colorId: [null, Validators.compose([Validators.required])],
      note: [null],
      englishComment: [null],
      chinaComment: [null],
      poCarton: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      pricePerCarton: [null],
      pricePerItem: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      expDeliveryCost: [null],
      itemDimLength: [null],
      itemDimWidth: [null],
      itemDimHeight: [null],
      dimAge: [null],
      transportCharges: [null],
      craneExpense: [null],
      cartonWeight: [null],
      cartonWeightRu: [null],
      percentage: [null],
      expensePcs: [null],
      qcCheckListId: [null],
      purchaseRation: [null],
      extraExpense: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      itemLength: [null],
      itemWidth: [null],
      itemHeight: [null],
      itemDimUnitMaster: [null],
      itemWeight: [null],
      itemWithBoxLength: [null],
      itemWithBoxWidth: [null],
      itemWithBoxHeight: [null],
      itemWithBoxWeight: [null],
      itemWithBoxDimUnitMaster: [null],
      packingTypeIds: [null],
      tag: [null],
      cbm: [{ value: null, disabled: true }],
      measurementCode: [null],
      isQCRequired: [false],
      isPhotosRequired: [false],
    });
    this.items.push(group);
    const obj = new POImportItem()
    obj.itemDropdown = this.utilsService.filterIsActive(this.dropdown.item, null);
    obj.checkList = this.utilsService.filterIsActiveLV(this.dropdown.checkList, null);
    obj.itemDimArr = this.utilsService.filterIsActive(this.dropdown.unitMaster, null);
    obj.itemBoxDimArr = this.utilsService.filterIsActive(this.dropdown.unitMaster, null);
    // obj.packingTypes = this.utilsService.filterIsActiveLV(this.dropdown?.packingMaster, null);
    // obj.colorDropdown = this.utilsService.filterIsActive(this.dropdown.color, null);

    if (obj.itemDimArr) {
      obj.itemDimArr = obj.itemDimArr.filter(d => d?.unitMasterCategory?.value == 'LENGTH')
      obj.itemDimUnitMasterId = (obj.itemDimArr || []).find(unit => (unit.shortCode)?.toLowerCase() === ('CM')?.toLowerCase())?.id;
    }
    if (obj.itemBoxDimArr) {
      obj.itemBoxDimArr = obj.itemBoxDimArr.filter(d => d?.unitMasterCategory?.value == 'LENGTH')
      obj.itemWithBoxDimUnitMasterId = (obj.itemBoxDimArr || []).find(unit => (unit.shortCode)?.toLowerCase() === ('CM')?.toLowerCase())?.id;
    }
    this.associatedItems.push(obj)
    this.changeShippingTypeValidation(this.items.length - 1);
  }

  openRemoveAIModal(index: number, item: POImportItem) {

    if (this.checkIfItemsReceived(this.associatedItems[index])) {
      return;
    }

    this.itemObj = (item)
    this.selectedItemIndex = index;
    this.associateItemPODeleteModal.show();
  }

  removeAI() {
    if (this.associatedItems.at(this.selectedItemIndex)?.id) {
      this.poImportObj.deletePoItem.push(this.associatedItems.at(this.selectedItemIndex).id)
    }
    this.items.removeAt(this.selectedItemIndex);
    this.associatedItems.splice(this.selectedItemIndex, 1);
    this.associateItemPODeleteModal.hide();

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.associatedItems)) {
      this.poImportObj.subTotalRmb = 0
      this.poImportObj.gstAmountRupees = 0
      this.poImportObj.grandTotalRmb = 0
      this.poImportObj.gstAmountRmb = 0
      this.poImportObj.subTotalRupees = 0
      this.poImportObj.grandTotalRupees = 0
    }
    this.associatedItems.forEach((_, i) => {
      this.onCalPOCartonPCSCartonC(i)
    })
  }

  // Called when item change in associated items or redirected from registration
  setValueToAssociateItem(item: POImportItem, index: number, objNew?: POImportItem) {

    let obj = null;

    if (objNew) {
      obj = objNew;
    } else {
      obj = this.associatedItems[index].itemDropdown.find(v => v.associatedIds === item.associatedIds);
    }

    this.associatedItems[index].levelBreachQtys = (obj?.levelBreachQtys?.breachQtys) ? obj?.levelBreachQtys : null;
    this.associatedItems[index].displayName = obj ? obj?.displayName : null;
    this.associatedItems[index].itemId = obj ? obj?.itemId : null;
    this.associatedItems[index].colorId = [];

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.associatedItems[index].levelBreachQtys) && obj) {
      this.utilsService.toasterService.error(`Breach Quantity not selected for item ${item?.displayName}`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      this.items.at(index).reset();
      return;
    }

    this.associatedItems[index].cartonLength = obj ? obj?.cartonLength : null;
    this.associatedItems[index].cartonWidth = obj ? obj?.cartonWidth : null;
    this.associatedItems[index].cartonHeight = obj ? obj?.cartonHeight : null;

    if (this.associatedItems[index].cartonLength &&
      this.associatedItems[index].cartonWidth &&
      this.associatedItems[index].cartonHeight) {

      ['itemDimHeight', 'itemDimWidth', 'itemDimLength'].forEach(field => {
        const control = this.items.at(index).get(field);
        control.disable();
        control.updateValueAndValidity();
      });

    } else {
      ['itemDimHeight', 'itemDimWidth', 'itemDimLength'].forEach(field => {
        const control = this.items.at(index).get(field);
        control.enable();
        control.updateValueAndValidity();
      });
    }
  
    this.associatedItems[index].associatedIds = obj ? obj?.associatedIds : null;
    this.associatedItems[index].itemGroupName = obj ? obj?.itemGroupName : null;
    this.associatedItems[index].cartonQuantity = obj ? obj?.cartonQuantity : null;
    this.associatedItems[index].associatedItemColors = obj ? obj?.associatedItemColors : null;

    if(this.associatedItems[index]?.associatedItemColors.length === 1) {
      this.associatedItems[index].colorId.push(this.associatedItems[index]?.associatedItemColors[0].poColorId)
    }

    this.associatedItems[index].fromDate = (obj?.seasonMaster) ? obj?.seasonMaster?.fromDate : null;
    this.associatedItems[index].toDate = (obj?.seasonMaster) ? obj?.seasonMaster?.toDate : null;
    this.associatedItems[index].advanceDate = (obj?.seasonMaster) ? obj?.seasonMaster?.advanceDate : null;
    this.associatedItems[index].note = obj ? obj?.note : null;
    this.associatedItems[index].englishComment = obj ? obj?.englishComment : null;
    this.associatedItems[index].expectedDeliveryDate = obj ? obj?.expectedDeliveryDate : null;
    this.associatedItems[index].chinaComment = obj ? obj?.chinaComment : null;
    // this.associatedItems[index].pricePerItem = obj && this.poImportObj.conversationRate ? Number.parseFloat((obj?.pricePerItem / this.poImportObj.conversationRate).toFixed(4)) : null;
    this.associatedItems[index].pricePerItem = obj ? obj?.pricePerItem : null;
    this.associatedItems[index].pricePerCarton = obj ? obj?.cartonQuantity : null;
    this.associatedItems[index].qcCheckListId = obj ? obj?.qcCheckListId : null;
    this.associatedItems[index].itemPrice = obj ? obj?.itemPrice : null;
    this.associatedItems[index].unitMaster = obj ? obj?.unitMaster : null;

    this.associatedItems[index].formattedName = obj ? obj?.itemFileFormatedName : null;
    this.associatedItems[index].skuId = obj ? obj?.itemSKUId : null;
    this.associatedItems[index].lastRecord = obj ? obj?.lastRecord : null;
    this.associatedItems[index].dimUpdatedDate = obj ? obj?.dimUpdatedDate : null;
    this.associatedItems[index].dimUniteName = obj ? obj?.unitMaster?.shortCode : null;
    this.associatedItems[index].cartonDimUnitId = obj ? obj?.unitMaster?.id : null;
    this.associatedItems[index].dimUniteId = obj ? obj?.cartonWeightDim?.id : null;
    this.associatedItems[index].dimAge = obj ? obj?.dimAge : null;
    this.associatedItems[index].qcCheckListId = obj ? obj?.qcCheckListId : null;
    this.associatedItems[index].cartonWeight = (obj?.cartonWeightDim?.conversionToMeter && Number.isFinite(obj.cartonWeight / obj.cartonWeightDim.conversionToMeter)) ? (obj.cartonWeight / obj.cartonWeightDim.conversionToMeter) : null;
    this.associatedItems[index].cartonWeightUnit = obj ? obj?.cartonWeightDim?.shortCode : null;
    this.associatedItems[index].rate = obj ? obj?.rate : null;
    this.associatedItems[index].purchaseRation = obj ? obj?.purchaseRation : null;
    this.associatedItems[index].averagePriceWithGST = obj ? obj?.averagePriceWithGST : null;
    this.associatedItems[index].averagePriceWithoutGST = obj ? obj?.averagePriceWithoutGST : null;
    this.associatedItems[index].customerCount = obj ? obj?.customerCount : null;
    this.associatedItems[index].inquiryCount = obj ? obj?.inquiryCount : null;
    this.associatedItems[index].inquiryShopWithPrice = obj ? obj?.inquiryShopWithPrice : [];
    
    this.onChangeTagQty(this.associatedItems[index], index)

    this.associatedItems[index].itemLength = obj ? obj?.itemLength : null;
    this.associatedItems[index].itemWidth = obj ? obj?.itemWidth : null;
    this.associatedItems[index].itemHeight = obj ? obj?.itemHeight : null;
    this.associatedItems[index].itemWeight = obj ? obj?.itemWeight : null;
    this.associatedItems[index].itemWithBoxLength = obj ? obj?.itemWithBoxLength : null;
    this.associatedItems[index].itemWithBoxWidth = obj ? obj?.itemWithBoxWidth : null;
    this.associatedItems[index].itemWithBoxHeight = obj ? obj?.itemWithBoxHeight : null;
    this.associatedItems[index].itemWithBoxWeight = obj ? obj?.itemWithBoxWeight : null;
    this.associatedItems[index].tag = obj ? obj?.tag : null;
    this.associatedItems[index].itemWithBoxDimUnitMasterId = obj ? obj?.itemWithBoxDimUnitMasterId : null;
    this.associatedItems[index].itemDimUnitMasterId = obj ? obj?.itemDimUnitMasterId : null;
    this.associatedItems[index].measurementCode = obj ? obj?.measurementCode : null;
    this.associatedItems[index].packingTypeIds = obj ? (obj?.packingTypes || []).map(item => item.packingMasterId) : null;
    if (!objNew) {
      this.associatedItems[index].packingTypes = this.utilsService.filterIsActiveMultipleAndContains(this.dropdown?.packingMaster, this.associatedItems[index].packingTypeIds ?? null);
    }

    //Calc again
    this.onCalPOCartonPCSCartonC(index)
  }

  customSearchFn(term: string, item: POImportItem) {
    const lowerCaseTerm = term.toLocaleLowerCase();
    return item?.itemSKUId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.itemGroupName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.tag?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      (`${item?.itemSKUId} - ${item?.displayName}`).toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
  }

  onCalPOCartonPCSCartonC(index: number) {
    const A = Number(this.associatedItems[index]?.poCarton) || 0
    const B = Number(this.associatedItems[index]?.pricePerCarton) || 0
    this.associatedItems[index].totalPcsQty = Number.parseFloat((A * B)?.toFixed(2));
    this.onCalE(index)
    this.onCalG(index)
    this.onCalH(index)
    this.onCalI(index)
    this.onCalP(index)
    this.onCalQ(index)
    this.onCalR(index)
    this.onCalS(index)
    this.onCalT(index)
    this.onCalV(index)
    this.onCalW(index)
    this.onCalK5(index)
    this.onCalL3(index)
    this.onCalK3(index)
    this.onCalW(index)
    this.onCalN1(index)
    this.onCalM1(index)
    this.onCalJ1(index)
    this.onCalK1(index)
    this.onCalK2(index)
    this.onCalM2(index)
    this.onCalN2(index)
    this.onCalO2(index)
    this.onCalX(index)
    this.onCalJ4(index)

    this.totalCalculations();
  }

  onCalE(index: number) {
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    const D = Number(this.associatedItems[index]?.pricePerItem) || 0
    this.associatedItems[index].totalAmount = Number.parseFloat((C * D)?.toFixed(4));
    this.onCalG(index)
    this.onCalH(index)
    this.totalCalculations();
  }

  onCalG(index: number) {
    const E = Number(this.associatedItems[index]?.totalAmount) || 0
    const F = Number(this.associatedItems[index]?.expDeliveryCost) || 0
    this.associatedItems[index].totalAmountWithExp = Number.parseFloat((E + F)?.toFixed(4));
    this.onCalH(index)
    this.totalCalculations();
  }

  onCalH(index: number) {
    const G = Number(this.associatedItems[index]?.totalAmountWithExp) || 0
    const Conv = Number((this.poImportForm.get('conversationRate').value)) || 0
    this.associatedItems[index].totalAmountWithExpInINR = Number.parseFloat((G * (Conv ? Conv : 0))?.toFixed(2));
    this.onCalI(index)
    this.onCalR(index)
    this.onCalT(index)
    this.onCalGSTAmt(index);
  }

  onCalI(index: number) {
    const H = Number(this.associatedItems[index]?.totalAmountWithExpInINR) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].chinaFinalExpextedCode = (H > 0 && C > 0) ? Number.parseFloat((H / C).toFixed(2)) : 0;
    this.onCalL3(index)
    this.onCalK3(index)
    this.onCalK5(index)
    this.onCalO2(index)
    this.onCalK2(index)
    this.onCalN2(index);
    this.onCalJ4(index)
  }

  onCalP(index: number) {
    const O = Number(this.associatedItems[index]?.transportCharges) || 0
    const A = Number(this.associatedItems[index]?.poCarton) || 0
    this.associatedItems[index].totalTransportationChargesM2S = Number.parseFloat((O * A)?.toFixed(2));
    this.onCalQ(index);
  }

  onCalQ(index: number) {
    const P = Number(this.associatedItems[index]?.totalTransportationChargesM2S) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].transportationChargesM2SperPCS = C === 0 ? 0 : Number.parseFloat((P / C).toFixed(2));
    this.onCalW(index)
  }

  onCalR(index: number) {
    const H = Number(this.associatedItems[index]?.totalAmountWithExpInINR)
    this.associatedItems[index].totalInsurance = Number.parseFloat((H * 0.01)?.toFixed(2));
    this.onCalS(index)
  }

  onCalS(index: number) {
    const R = Number(this.associatedItems[index]?.totalInsurance) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].insurancePerPcs = C === 0 ? 0 : Number.parseFloat((R / C).toFixed(2));
    this.onCalW(index)
  }

  onCalT(index: number) {
    const H = Number(this.associatedItems[index]?.totalAmountWithExpInINR) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].gstAmtPerPcs = Number.parseFloat(((H * 30 / 100 * 0.18) / C).toFixed(2));
    this.onCalW(index)
    this.onCalX(index)
  }

  onCalV(index: number) {
    const U = Number(this.associatedItems[index]?.craneExpense) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].craneExpPcs = C === 0 ? 0 : Number.parseFloat((U / C).toFixed(2));
    this.onCalW(index)
    this.onCalX(index)
  }

  onCalW(index: number) {
    const Q = Number(this.associatedItems[index]?.transportationChargesM2SperPCS) || 0
    const S = Number(this.associatedItems[index]?.insurancePerPcs) || 0
    const T = Number(this.associatedItems[index]?.gstAmtPerPcs) || 0
    const V = Number(this.associatedItems[index]?.craneExpPcs) || 0
    this.associatedItems[index].totalExp = Number.parseFloat((Q + S + T + V)?.toFixed(2));
    this.onCalL3(index)
    this.onCalX(index)
    this.onCalJ4(index)
  }

  onCalK3(index: number) {
    const I = Number(this.associatedItems[index].chinaFinalExpextedCode) || 0
    const J3 = Number(this.associatedItems[index].percentage) || 0

    this.associatedItems[index].totalExpPCSper = Number.parseFloat((I * (J3/100))?.toFixed(2));
    this.onCalL3(index)
    this.onCalX(index)
  }

  onCalL3(index: number) {
    const I = Number(this.associatedItems[index].chinaFinalExpextedCode) || 0
    const W = Number(this.associatedItems[index].totalExp) || 0
    const K3 = Number(this.associatedItems[index].totalExpPCSper) || 0

    this.associatedItems[index].totalFinalCostPCSper = Number.parseFloat((I + W + K3)?.toFixed(2));
  }

  onCalK5(index: number) {
    const I = Number(this.associatedItems[index].chinaFinalExpextedCode) || 0
    const W = Number(this.associatedItems[index].totalExp) || 0
    const J5 = Number(this.associatedItems[index].expensePcs) || 0

    this.associatedItems[index].totalItemAmt = Number.parseFloat((I + W + J5)?.toFixed(2));
    this.onCalX(index)
  }

  onCalJ1(index: number) {
    const conversionToMeter = Number(this.associatedItems[index]?.unitMaster?.conversionToMeter) || 0;
    const length = conversionToMeter !== 0 ? (Number(this.associatedItems[index].cartonLength)) : 0;
    const width = conversionToMeter !== 0 ? (Number(this.associatedItems[index].cartonWidth)) : 0;
    const height = conversionToMeter !== 0 ? (Number(this.associatedItems[index].cartonHeight)) : 0;

    const conversionValue = conversionToMeter ** 3;
    
    this.associatedItems[index].cbmPerCarton = Number.parseFloat(((length * width * height) / conversionValue)?.toFixed(5));
  }

  onCalK1(index: number) {
    const A = Number(this.associatedItems[index]?.poCarton) || 0
    const J1 = Number(this.associatedItems[index].cbmPerCarton) || 0

    this.associatedItems[index].totalCbm = Number.parseFloat((A * J1)?.toFixed(5));
    this.onCalM1(index)
  }

  onCalM1(index: number) {
    const K1 = Number(this.associatedItems[index]?.totalCbm) || 0
    const L1 = Number(this.poImportForm.get('cbmPrice').value) || 0

    this.associatedItems[index].totalCBMExpenseINR = Number.parseFloat((K1 * L1)?.toFixed(2));
    this.onCalN1(index)
  }

  onCalN1(index: number) {
    const M1 = Number(this.associatedItems[index]?.totalCBMExpenseINR) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0

    this.associatedItems[index].shippingExpPerPCS = Number.parseFloat((M1 / C)?.toFixed(2));
    this.onCalX(index)
  }

  onCalX(index: number) {
    const I = Number(this.associatedItems[index]?.chinaFinalExpextedCode) || 0
    const W = Number(this.associatedItems[index]?.totalExp) || 0
    
    const N1 = Number(this.associatedItems[index]?.shippingExpPerPCS) || 0
    const N2 = Number(this.associatedItems[index]?.shippingCostperPieceINR) || 0
    const J5 = Number(this.associatedItems[index]?.expensePcs) || 0
    const K3 = Number(this.associatedItems[index]?.totalExpPCSper) || 0

    let cal = null;

    switch (this.poImportForm.get('shippingTypes').value) {
      case this.enumForShippingType.CBM:
        cal = I + N1 + W
        break;
      case this.enumForShippingType.WEIGHT:
        cal = I + N2 + W
        break;
      case this.enumForShippingType.PERCENTAGE:
        cal = I + K3 + W
        break;
      case this.enumForShippingType.DONE:
        cal = I + W
        break;
      case this.enumForShippingType.PIECE:
        cal = I + J5 + W
        break;

      default:
        cal = I + W
        break;
    }

    this.associatedItems[index].chinaToSuratPadtar = Number.parseFloat((cal)?.toFixed(2));
  }

  onCalK2 = (index: number) => {
    const J2 = Number(this.associatedItems[index]?.cartonWeight) || 0
    const A = Number(this.associatedItems[index]?.poCarton) || 0
    this.associatedItems[index].totalWeight = Number.parseFloat((J2 * A)?.toFixed(2));
    this.onCalM2(index)
    this.onCalX(index)
  }

  onCalM2 = (index: number) => {
    const K2 = Number(this.associatedItems[index]?.totalWeight) || 0
    const L2 = Number(this.associatedItems[index]?.cartonWeightRu) || 0
    this.associatedItems[index].totalLoadAmt = Number.parseFloat((K2 * L2)?.toFixed(2));
    this.onCalN2(index)
    this.onCalX(index)
  }

  onCalN2 = (index: number) => {
    const M2 = Number(this.associatedItems[index]?.totalLoadAmt) || 0
    const C = Number(this.associatedItems[index]?.totalPcsQty) || 0
    this.associatedItems[index].shippingCostperPieceINR = Number.parseFloat((M2 / C)?.toFixed(2));
    this.onCalO2(index);
  }

  onCalO2 = (index: number) => {
    const I = Number(this.associatedItems[index].chinaFinalExpextedCode) || 0
    const W = Number(this.associatedItems[index].totalExp) || 0
    const N2 = Number(this.associatedItems[index].shippingCostperPieceINR) || 0
    this.associatedItems[index].totalShippingExpWeight = Number.parseFloat((I + W + N2)?.toFixed(2));
  }

  onCalGSTAmt = (index: number) => {
    const conv = Number((this.poImportForm.get('conversationRate').value)) || 0
    const E = Number(this.associatedItems[index]?.totalAmount * conv) || 0
    this.associatedItems[index].gst_amounts = Number.parseFloat(((E * (this.associatedItems[index].rate / 100))).toFixed(2));
  }

  onCalJ4 = (index: number) => {
    const I = Number(this.associatedItems[index].chinaFinalExpextedCode) || 0
    const W = Number(this.associatedItems[index].totalExp) || 0
    this.associatedItems[index].j4Amt = Number.parseFloat((I + W)?.toFixed(2));
  }

  //COMMENT

  onSaveComment() {

    if(this.commentList.length === 0) {
      this.selectedCommentIndex = null;
    }

    if (!this.commentObj.comments?.trim()) {
      return;
    }
    
    if (this.selectedCommentIndex !== null && this.selectedCommentIndex !== undefined) {
      this.commentList[this.selectedCommentIndex].comments = this.commentObj.comments;
      this.commentList[this.selectedCommentIndex].isEdit = false;
      this.selectedCommentIndex = null;
    } else {
      const obj = new POImportComment();
      obj.isAdd = true;
      obj.comments = this.commentObj.comments;
      obj.userId = this.utilsService.userId;
      obj.username = this.utilsService.username;
      obj.profile = this.utilsService.userProfilePicture;
      this.commentList.unshift(obj);
    }

    this.commentObj.comments = '';
    this.isEditPhase = false;
  }
  
  onEditComment(obj: POImportComment, index: number) {
    obj.isEdit = true;
    this.isEditPhase = true;
    this.commentList.map(comment => {comment.isEdit = false; return comment})
    this.selectedCommentIndex = index;
    this.commentObj.comments = obj.comments;
  }

  openDeleteModalComment(item: POImportComment, index: number) {
    this.selectedCommentIndex = index;
    this.deleteCommentObj = Serialize(item)
    this.deleteCommentPoModal.show()
  }

  onDeleteComment() {
    this.commentList.splice(this.selectedCommentIndex, 1);
    this.deleteCommentObj = new POImportComment();
    this.commentObj = new POImportComment();
    this.deleteCommentPoModal.hide()
    this.selectedCommentIndex = null;
  }

  // COMMON GET DATA
  setObjData(response, isGetData: boolean) {
    if (response) {

      let key = null;
      switch (this.selectedTab) {
        case this.enumForStatus.DRAFT:
          key = 'poImport'
          break;
        case this.enumForStatus.PO_CREATED:
          key = 'poImportCreate'
          break;

        default:
          break;
      }

      this.poImportObj = Deserialize(response[key], POImport);

      this.poImportObj.orderType = response[key]?.orderType?.value

      if (response[key]?.shippingTypes) {
        this.poImportForm.get('shippingTypes').setValue(response[key].shippingTypes.value);
      }

      if (response[key]?.advancePayByCustomCurrencyId) {
        this.poImportObj.advancePayByCustomerCurrencyId = response[key]?.advancePayByCustomCurrencyId
      }

      const date = moment(this.poImportObj.poImportDate).toDate();
      this.poImportObj.temp_date = {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate()
      }

      this.poImportObj.status = response[key]?.status?.value
      if (response[key]?.status?.value) {
        this.poStatus = Serialize(response[key].status.value)

        if (this.poStatus === this.enumForStatus.PO_CREATED) {
          this.poImportObj.poCreateId = Serialize(this.poImportObj.id)
        }
      }

      if (this.poImportObj?.tags) {
        this.poImportObj.temp_tags = this.poImportObj.tags.split(",");
      }

      if (response[key]?.poImportDocList) {
        this.poImportObj.poImportDocList = response[key]?.poImportDocList || []
      }

      if (response[key]?.poComments) {
        this.commentList = response[key]?.poComments || [];
        for(const a of this.commentList) {
          a.profile = a.profileUrl
          a.username = a.name
        }
      }

      if (response[key]?.poImportItemList) {
        this.associatedItems = Deserialize(response[key]?.poImportItemList, POImportItem);
        const fa = (this.poImportForm.get('items') as FormArray);
        fa.clear();
        const obj = this.associatedItems.map((v, i) => {
          v.packingTypeIds = Serialize(v.packingTypes)
          fa.push(this.fb.group({
            itemId: [v.associatedIds, Validators.compose([Validators.required])],
            marka: [v.marka],
            colorId: [null, Validators.compose([Validators.required])],
            note: [v.note],
            englishComment: [v.englishComment],
            chinaComment: [v.chinaComment],
            poCarton: [v.poCarton, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            pricePerCarton: [v.pricePerCarton],
            pricePerItem: [v.pricePerItem, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            expDeliveryCost: [v.expDeliveryCost],
            itemDimLength: [v.cartonLength],
            itemDimWidth: [v.cartonWidth],
            itemDimHeight: [v.cartonHeight],
            dimAge: [v.dimAge],
            transportCharges: [v.transportCharges],
            craneExpense: [v.craneExpense],
            cartonWeight: [null],
            cartonWeightRu: [null],
            percentage: [null],
            expensePcs: [v.expensePcs],
            qcCheckListId: [null],
            purchaseRation: [v.purchaseRatio],
            extraExpense: [v.extraExpense, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            itemLength: [v.itemLength],
            itemWidth: [v.itemWidth],
            itemHeight: [v.itemHeight],
            itemDimUnitMaster: [v.itemDimUnitMaster],
            itemWeight: [v.itemWeight],
            itemWithBoxLength: [v.itemWithBoxLength],
            itemWithBoxWidth: [v.itemWithBoxWidth],
            itemWithBoxHeight: [v.itemWithBoxHeight],
            itemWithBoxWeight: [v.itemWithBoxWeight],
            itemWithBoxDimUnitMaster: [v.itemWithBoxDimUnitMaster],
            packingTypeIds: [v.packingTypeIds],
            tag: [v.tag],
            measurementCode: [v.measurementCode],
            isPhotosRequired: [v.isPhotosRequired],
            isQCRequired: [v.isQCRequired],
          }));
          v.itemGroupName = v.item?.itemGroupName
          v.fromDate = v.item?.seasonMaster?.fromDate
          v.toDate = v.item?.seasonMaster?.toDate
          v.advanceDate = v.item?.seasonMaster?.advanceDate
          v.itemPrice = v.item?.itemPrice
          // v.associatedIds = v.associatedIds
          // v.expectedDeliveryDate = v.expectedDeliveryDate
          v.levelBreachQtys = v.item?.levelBreachQtys
          v.cartonWeight = v.Weight_Carton
          v.cartonWeightRu = v.Weight_kg
          v.percentage = v.Percentage
          v.expensePcs = v.Expense_PCS
          v.dimUniteName = v.cartonDimensionUnit?.shortCode
          v.cartonDimUnitId = v.cartonDimensionUnit?.id
          v.unitMaster = (this.dropdown?.item || []).find(a => a.associatedIds === v.associatedIds)?.unitMaster || null
          v.itemBoxDimArr = this.utilsService.filterIsActive(this.dropdown?.unitMaster || [], v.itemWithBoxDimUnitMaster ?? null).filter(d => d?.unitMasterCategory?.value == 'LENGTH');
          v.itemDimArr = this.utilsService.filterIsActive(this.dropdown?.unitMaster || [], v.itemDimUnitMaster ?? null).filter(d => d?.unitMasterCategory?.value == 'LENGTH');
          
          setTimeout(() => {
            this.onCalPOCartonPCSCartonC(i);
            this.totalCalculations()
          }, 100);

          if (this.associatedItems[i].cartonLength &&
            this.associatedItems[i].cartonWidth &&
            this.associatedItems[i].cartonHeight) {

            ['itemDimHeight', 'itemDimWidth', 'itemDimLength'].forEach(field => {
              const control = this.items.at(i).get(field);
              control.disable();
              control.updateValueAndValidity();
            });

          } else {
            ['itemDimHeight', 'itemDimWidth', 'itemDimLength'].forEach(field => {
              const control = this.items.at(i).get(field);
              control.enable();
              control.updateValueAndValidity();
            });
          }
        })
        if(!isGetData) {
          this.associatedItems.forEach((v, i) => {
            const dropdown = (this.dropdown?.item.find(a => a.associatedIds == v.associatedIds)?.associatedItemColors)
            v.associatedItemColors = Serialize(dropdown)
            v.checkList = this.utilsService.filterIsActiveLV(this.dropdown.checkList, v.qcCheckListId ? v.qcCheckListId : null);
          })
        }
      }

      if(response[key].linkPoImportItems) {
        const linkedItems = JSON.parse(response[key].linkPoImportItems)

        for (const num of linkedItems) {
          for (const associatedId of num.associatedItemIds) {
            for (const item of this.associatedItems) {
              if (item.associatedIds === associatedId) {
                item.linkIds = num.associatedItemIds;
                item.isLink = true;
              }
            }
          }
        }
      }
    }
  }

  //LINK ITEM

  openLinkItemModal(item: POImportItem) {

    if (this.checkIfItemsReceived(item)) {
      return;
    }

    this.selectedIds = [];
    this.linkedItemList = [];
    this.flagForSelectAll = false;
    for (const item of this.linkedItemList) {
      item.isSelected = false;
    }
    if (this.associatedItems.length > 0) {
      this.associatedItems.forEach((v, i) => {
        v.isSelected = false
        this.onCalPOCartonPCSCartonC(i)
      })
    }
    this.selectedItem = Serialize(item)
    this.linkedItemList = ((this.associatedItems || []).filter(a => a.associatedIds !== item.associatedIds && !a.isLink).filter(a => this.utilsService.isDecimal(a.poCarton) && a.poCarton < 1))
    setTimeout(() => {
      for (const a of this.linkedItemList) {
        a.displayName = a.displayName
        a.formattedName = a.formattedName
        a.skuId = a.skuId
        a.colorName = a.associatedItemColors.filter(d => a.colorId.includes(d.poColorId)).map(d => d.colorName)?.join(", ")?.split(",").map(d => d.trim());
      }
    }, 50);
    this.LinkItemModal.show();
  }

  onLinkItems() {

    let poCarton = this.selectedItem.poCarton;
    let totalCarton = 0;

    for (const item of this.linkedItemList) {
      if (item.isSelected) {
        totalCarton += item.poCarton;
      }
    }
    poCarton += totalCarton;

    if (poCarton > 1) {
      this.utilsService.toasterService.error('PO Carton exceeding 1', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (this.linkedItemList.every(a => !a.isSelected)) {
      this.utilsService.toasterService.error('Select Atleast 1 Item to link', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    setTimeout(() => {

      let IDs = this.linkedItemList.filter(a => a.isSelected).map(a => a.associatedIds).flat();
      IDs.push(this.selectedItem.associatedIds);

      const obj = this.associatedItems.map(a => {
        if (IDs.includes(a.associatedIds)) {
          a.isLink = true;
          a.linkIds = [...IDs];
        }
        return a;
      });

      // this.associatedItems = obj.sort((a, b) => {
      //   if (a.linkIds && b.linkIds) {
      //     return b.linkIds.length - a.linkIds.length;
      //   }
      //   return 0;
      // });
    }, 0);
    
    this.LinkItemModal.hide();
  }

  onRemoveLink(index: number) {
    const associatedIdToRemove = this.associatedItems[index].associatedIds;

    this.associatedItems[index].linkIds = [];
    this.associatedItems[index].isLink = false;

    for (const item of this.associatedItems) {
      if (item.associatedIds !== associatedIdToRemove) {
        item.linkIds = item.linkIds.filter(id => id !== associatedIdToRemove);

        if (item.linkIds.length === 1) {
          item.linkIds = [];
          item.isLink = false;
        } else if (item.linkIds.length === 0) {
          item.isLink = false;
        }
      }
    }
  }

  //on Move to Draft
  onMoveToDraft(index: number) {
    
    if (this.checkIfItemsReceived(this.associatedItems[index])) {
      return;
    }

    this.associatedItems[index].isDraft = true;
    if (this.associatedItems.at(index)?.id) {
      this.poImportObj.draftPoItem.push(this.associatedItems.at(index).id)
    }
  }

  onRemoveFromDraft(index: number) {
    this.associatedItems[index].isDraft = false;
    const findIndex = this.poImportObj.draftPoItem.findIndex(a => a === this.associatedItems[index].id)
    if (findIndex !== -1) {
      this.poImportObj.draftPoItem.splice(findIndex, 1);
    }
  }

  selectAll() {
    if (this.flagForSelectAll === true) {
      this.selectedIds = [];
    }
    const obj = this.linkedItemList.filter((val, index) => {
      if (this.flagForSelectAll === true) {
        val.isSelected = true;
        this.selectedIds.push(val.id);
      } else {
        val.isSelected = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.flagForSelectAll === false) {
      this.selectedIds = [];
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.linkedItemList.filter((val, index) => {
      if (val.isSelected === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  //
  isLinkIdPresent(linkIds: number[], associatedIds: number): boolean {
    return (linkIds || []).includes(associatedIds);
  }

  //
  checkIfItemsReceived(item: POImportItem) {
    if(item.receivedQty) {
      this.utilsService.toasterService.error(`Some quantity of this item ${item.item?.displayName} is already received`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return true;
    }
    return false;
  }

  // PO import total calculations top right box
  totalCalculations() {

    this.poImportObj.subTotalRmb = Number(
      this.associatedItems.reduce((total, item) => {
        return total + (Number(item.totalAmount) || 0);
      }, 0).toFixed(4)
    );

    // GST AMT RMB
    this.poImportObj.gstAmountRupees = Number(
      this.associatedItems.reduce((total, item) => {
        return total + (Number(item.gst_amounts) || 0);
      }, 0).toFixed(2)
    );
    // Grand Total RMB
    this.poImportObj.grandTotalRmb = Number((this.poImportObj.gstAmountRmb + this.poImportObj.subTotalRmb).toFixed(4));

    // Converted values using conversationRate

    const conv = (this.poImportForm.get('conversationRate').value || 0)

    this.poImportObj.gstAmountRmb = (Number(conv) || 0) > 0 ? Number((Number(this.poImportObj.gstAmountRupees) / Number(conv)).toFixed(4)) : 0;
    this.poImportObj.subTotalRupees = Number((this.poImportObj.subTotalRmb * conv).toFixed(2));
    this.poImportObj.grandTotalRupees = Number((this.poImportObj.grandTotalRmb * conv).toFixed(2));
  }

  /// Warning Modal

  openWarningPOModal() {
    this.saveWarningPOModal.show();
    this.isWarningModal = true;
  }


  // Clear

  onClearPaymentTerms = () => {
    this.poImportObj.paymentTermsId = null;
  }

  onClearItem = (index: number) => {
    this.associatedItems[index] = new POImportItem()
    this.associatedItems[index].itemDropdown = this.utilsService.filterIsActive(this.dropdown.item, null);
    this.associatedItems[index].checkList = this.utilsService.filterIsActiveLV(this.dropdown.checkList, null);
  }
  
  // Create PO From Registration

  getFromRegistration = () => {

    const data = JSON.parse(localStorage.getItem('registration'));
    if (this.utilsService.isEmptyObjectOrNullUndefined(data)) {
      this.getRequiredData(null);
      return;
    }

    this.fromReg = true;
    this.selectedTab = this.enumForStatus.DRAFT
    this.poImportObj.supplierId = data.regId;
    this.regItemIds = (data.items || [])
    setTimeout(() => {
      this.getRequiredData(this.poImportObj.supplierId);
    }, 50);
  }

  // Set registration data, redirected from registration or reg details
  setRegRedirectData = () => {
    this.poImportObj.mobileNo = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId).phone;
    this.poImportObj.mobileExtensionId = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId)?.countryId;

    for (const a of this.regItemIds) {
      if (a) { this.addItem(); }
    }

    const a: number[] = [];
    this.associatedItems.forEach((item: POImportItem, i: number) => { if (item.id) { a.push(i); }});

    let index = a.length > 0 ? Math.max(...a) + 1 : 0;

    for(const id of this.regItemIds) {
      if (!this.associatedItems[index]) {
        this.associatedItems[index] = null;
      }

      this.associatedItems[index].associatedIds = id;
      const obj = this.associatedItems[index]?.itemDropdown.find(v => v.associatedIds === this.associatedItems[index].associatedIds);

      if (obj) {
        this.associatedItems[index].itemId = obj.itemId;
        this.setValueToAssociateItem(this.associatedItems[index], index, obj);
      }
      index++;
    }
    localStorage.removeItem('registration')
  }

  /// Tag function for max tag allowed

  addTagLimit = (term: string) => {
    if ((this.poImportObj.temp_tags || []).length < 10) {
      return term;
    }
    this.utilsService.toasterService.error("Maximum 10 tags allowed", '', {
      positionClass: 'toast-top-right',
      closeButton: true,
      timeOut: 10000
    });
    return null;
  }

  /// Image View Modal

  openImagePreviewModal(item: POImportItem) {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.isCopied = false;
    this.itemImagesList = [];
    this.showSliderModal = true;
    this.itemObj = Serialize(item)
    setTimeout(() => {
      this.getImageByItem(item.associatedIds)
    }, 50);
  }

  getImageByItem = (id: number) => {
    const supplierName = this.dropdown.supplierDropdown.find(a => a.id === this.poImportObj.supplierId)?.displayName;

    this.utilsService.getMethodAPI(false, `${this.utilsService.serverVariableService.PO_GET_ITEM_IMAGES}?associatedItemId=${id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemImagesList = response;
        if (this.itemImagesList) {
          this.currentItem = this.itemImagesList[0];
          this.selectedImageIndex = 1;
          this.productmodalSliderPOModal.show();
        }
      }
      else {
        this.utilsService.toasterService.error('No Images found in associated item for the supplier, ' + supplierName, '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
      }
    })
  }

  // Slider change event
  onSlideChange(event): void {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.isCopied = false;
    const activeIndex = event.slick.currentSlide;
    this.currentItem = this.itemImagesList[activeIndex];
    this.selectedImageIndex = activeIndex + 1;
  }

  // Copy link/image url
  onCopy() {
    this.isCopied = true;
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.isCopied = false;
    }, 5000);
  }

  // Check if comments are not saved
  checkIfCommentsNotSaved = () => {
    const flag = !this.utilsService.isEmptyObjectOrNullUndefined(this.commentObj.comments);
    return flag;
  }

  // Add ng select class to increase width of item dropdown on opening ng-select
  handleFocus(): void {
    const myCustomClass = 'new-po-select';
    const openPanels = document.querySelectorAll('.ng-dropdown-panel');
    openPanels.forEach(panel => panel.remove());
    setTimeout(() => {
      const newPanel = document.querySelector('.ng-dropdown-panel');
      if (newPanel) {
        newPanel.classList.add(myCustomClass);
      }
    }, 0);
  }

  onChangeTagQty(item: POImportItem, index: number) {
    const skuId = item.item?.skuId ? item.item?.skuId : (item.itemDropdown || []).find(item => item.associatedIds === this.associatedItems[index].associatedIds)?.itemSKUId || '';
    const tag = this.associatedItems[index]?.tag || '';
    const cartonQuantity = this.associatedItems[index]?.pricePerCarton || '';
    
    let measurementCode = skuId ? `${skuId}` : '';
    if (cartonQuantity) measurementCode ? measurementCode += `_${cartonQuantity}` : measurementCode += `${cartonQuantity}`;
    if (tag) measurementCode += `_${tag}`;
    this.items.at(index).get('measurementCode').setValue(measurementCode);
  }
}

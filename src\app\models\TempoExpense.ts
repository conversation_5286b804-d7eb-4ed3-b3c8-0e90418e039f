import { deserializeAs, serializeAs } from 'cerialize';

export class TempoExpense {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @deserializeAs('expenseID')
    private _expenseID: number;

    @serializeAs('expenseDate')
    @deserializeAs('expenseDate')
    private _expenseDate: string;

    @serializeAs('tempoID')
    @deserializeAs('tempoID')
    private _tempoID: number;

    @serializeAs('notes')
    @deserializeAs('notes')
    private _notes: string;

    @deserializeAs('temp_date')
    private _temp_date: any;

    @serializeAs('expenseTypeItems')
    @deserializeAs('expenseTypeItems')
    private _expenseTypeItems: TempoExpenseList[];

    @deserializeAs('docs')
    private _docs: any[];

    @deserializeAs('containersName')
    private _containersName: string;

    @deserializeAs('expTotal')
    private _expTotal: number;

    @serializeAs('deletedDocumentID')
    @deserializeAs('deletedDocumentID')
    private _deletedDocumentID: number[];

    @serializeAs('deletedTypeID')
    @deserializeAs('deletedTypeID')
    private _deletedTypeID: number[];

    @deserializeAs('vehicleNo')
    private _vehicleNo: string;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @deserializeAs('documents')
    private _documents: any[];

    @deserializeAs('totalExpenseAmount')
    private _totalExpenseAmount: number;

    @deserializeAs('isCompleted')
    private _isCompleted: boolean;

    @deserializeAs('importType')
    private _importType: string;

    @serializeAs('isApprove')
    @deserializeAs('isApprove')
    private _isApprove: boolean;

    @deserializeAs('expenseStatus')
    private _expenseStatus: any;

    @serializeAs('status')
    @deserializeAs('status')
    private _status: any;

    @deserializeAs('isRollBack')
    private _isRollBack: boolean;

    constructor() {
        this.docs = [];
        this.expenseTypeItems = [];
        this.deletedDocumentID = [];
        this.deletedTypeID = [];
        this.isSelected = false;
        this.documents = [];
        this.isCompleted = false;
        this.isApprove = false;
        this.isRollBack = false;
    }

    /**
     * Getter isRollBack
     * @return {boolean}
     */
	public get isRollBack(): boolean {
		return this._isRollBack;
	}

    /**
     * Setter isRollBack
     * @param {boolean} value
     */
	public set isRollBack(value: boolean) {
		this._isRollBack = value;
	}

    /**
     * Getter status
     * @return {any}
     */
	public get status(): any {
		return this._status;
	}

    /**
     * Setter status
     * @param {any} value
     */
	public set status(value: any) {
		this._status = value;
	}


    /**
     * Getter expenseStatus
     * @return {any}
     */
	public get expenseStatus(): any {
		return this._expenseStatus;
	}

    /**
     * Setter expenseStatus
     * @param {any} value
     */
	public set expenseStatus(value: any) {
		this._expenseStatus = value;
	}


    /**
     * Getter isApprove
     * @return {boolean}
     */
	public get isApprove(): boolean {
		return this._isApprove;
	}

    /**
     * Setter isApprove
     * @param {boolean} value
     */
	public set isApprove(value: boolean) {
		this._isApprove = value;
	}

    /**
     * Getter importType
     * @return {string}
     */
	public get importType(): string {
		return this._importType;
	}

    /**
     * Setter importType
     * @param {string} value
     */
	public set importType(value: string) {
		this._importType = value;
	}

    /**
     * Getter isCompleted
     * @return {boolean}
     */
	public get isCompleted(): boolean {
		return this._isCompleted;
	}

    /**
     * Setter isCompleted
     * @param {boolean} value
     */
	public set isCompleted(value: boolean) {
		this._isCompleted = value;
	}


    /**
     * Getter totalExpenseAmount
     * @return {number}
     */
	public get totalExpenseAmount(): number {
		return this._totalExpenseAmount;
	}

    /**
     * Setter totalExpenseAmount
     * @param {number} value
     */
	public set totalExpenseAmount(value: number) {
		this._totalExpenseAmount = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter expenseID
     * @return {number}
     */
	public get expenseID(): number {
		return this._expenseID;
	}

    /**
     * Getter expenseDate
     * @return {string}
     */
	public get expenseDate(): string {
		return this._expenseDate;
	}

    /**
     * Getter tempoID
     * @return {number}
     */
	public get tempoID(): number {
		return this._tempoID;
	}

    /**
     * Getter notes
     * @return {string}
     */
	public get notes(): string {
		return this._notes;
	}

    /**
     * Getter temp_date
     * @return {any}
     */
	public get temp_date(): any {
		return this._temp_date;
	}

    /**
     * Getter expenseTypeItems
     * @return {TempoExpenseList[]}
     */
	public get expenseTypeItems(): TempoExpenseList[] {
		return this._expenseTypeItems;
	}

    /**
     * Getter docs
     * @return {any[]}
     */
	public get docs(): any[] {
		return this._docs;
	}

    /**
     * Getter containersName
     * @return {string}
     */
	public get containersName(): string {
		return this._containersName;
	}

    /**
     * Getter expTotal
     * @return {number}
     */
	public get expTotal(): number {
		return this._expTotal;
	}

    /**
     * Getter deletedDocumentID
     * @return {number[]}
     */
	public get deletedDocumentID(): number[] {
		return this._deletedDocumentID;
	}

    /**
     * Getter deletedTypeID
     * @return {number[]}
     */
	public get deletedTypeID(): number[] {
		return this._deletedTypeID;
	}

    /**
     * Getter vehicleNo
     * @return {string}
     */
	public get vehicleNo(): string {
		return this._vehicleNo;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter documents
     * @return {any[]}
     */
	public get documents(): any[] {
		return this._documents;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter expenseID
     * @param {number} value
     */
	public set expenseID(value: number) {
		this._expenseID = value;
	}

    /**
     * Setter expenseDate
     * @param {string} value
     */
	public set expenseDate(value: string) {
		this._expenseDate = value;
	}

    /**
     * Setter tempoID
     * @param {number} value
     */
	public set tempoID(value: number) {
		this._tempoID = value;
	}

    /**
     * Setter notes
     * @param {string} value
     */
	public set notes(value: string) {
		this._notes = value;
	}

    /**
     * Setter temp_date
     * @param {any} value
     */
	public set temp_date(value: any) {
		this._temp_date = value;
	}

    /**
     * Setter expenseTypeItems
     * @param {TempoExpenseList[]} value
     */
	public set expenseTypeItems(value: TempoExpenseList[]) {
		this._expenseTypeItems = value;
	}

    /**
     * Setter docs
     * @param {any[]} value
     */
	public set docs(value: any[]) {
		this._docs = value;
	}

    /**
     * Setter containersName
     * @param {string} value
     */
	public set containersName(value: string) {
		this._containersName = value;
	}

    /**
     * Setter expTotal
     * @param {number} value
     */
	public set expTotal(value: number) {
		this._expTotal = value;
	}

    /**
     * Setter deletedDocumentID
     * @param {number[]} value
     */
	public set deletedDocumentID(value: number[]) {
		this._deletedDocumentID = value;
	}

    /**
     * Setter deletedTypeID
     * @param {number[]} value
     */
	public set deletedTypeID(value: number[]) {
		this._deletedTypeID = value;
	}

    /**
     * Setter vehicleNo
     * @param {string} value
     */
	public set vehicleNo(value: string) {
		this._vehicleNo = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter documents
     * @param {any[]} value
     */
	public set documents(value: any[]) {
		this._documents = value;
	}



}

export class TempoExpenseList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('amount')
    @deserializeAs('amount')
    private _amount: number;

    @serializeAs('expenseTypeId')
    @deserializeAs('expenseTypeId')
    private _expenseTypeId: number;

    @deserializeAs('expenseDropdown')
    private _expenseDropdown: any[];

    @deserializeAs('expenseTypeName')
    private _expenseTypeName: string;

    constructor() {
        this.expenseDropdown = [];
    }
    

    /**
     * Getter expenseTypeName
     * @return {string}
     */
	public get expenseTypeName(): string {
		return this._expenseTypeName;
	}

    /**
     * Setter expenseTypeName
     * @param {string} value
     */
	public set expenseTypeName(value: string) {
		this._expenseTypeName = value;
	}


    /**
     * Getter expenseDropdown
     * @return {any[]}
     */
	public get expenseDropdown(): any[] {
		return this._expenseDropdown;
	}

    /**
     * Setter expenseDropdown
     * @param {any[]} value
     */
	public set expenseDropdown(value: any[]) {
		this._expenseDropdown = value;
	}


    /**
     * Getter amount
     * @return {number}
     */
	public get amount(): number {
		return this._amount;
	}

    /**
     * Setter amount
     * @param {number} value
     */
	public set amount(value: number) {
		this._amount = value;
	}

    /**
     * Getter expenseTypeId
     * @return {number}
     */
	public get expenseTypeId(): number {
		return this._expenseTypeId;
	}

    /**
     * Setter expenseTypeId
     * @param {number} value
     */
	public set expenseTypeId(value: number) {
		this._expenseTypeId = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

}
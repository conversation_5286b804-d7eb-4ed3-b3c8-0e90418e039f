export interface SbtBranchStock {
    id: number;
    image: string;
    itemName: string;
    totalQty: number;
    skuId: string;
    breachQty: number;
    allWarehouseStocks: SbtWarehouse[];
    allBranches: AllBranchList[];
    branchWiseStocks: {
        branchId: number;
        branchName: string;
        totalQty: number;
        breachQty: number;
    }[]
    warehouseWiseStocks: {
        warehouseId: number;
        warehouseName: string;
        totalQty: number;
    }[]
    availableQtyModalData: AvailableQtyModalData[]
    isDataLoaded: boolean;
    itemPrice: number
}

export interface AllBranchList {
    branchName: string
    id: number
    isMainBranch: boolean
}

export interface SbtWarehouse {
    id: number
    isMainWarehouse: boolean
    warehouseName: string
}

export interface AvailableQtyModalData {
    cartonQty: number;
    looseQty: number;
    totalCartonQty: number
    totalQty: number;
    itemId: number;
    locationName: string;
    locationType: string;
    locationTypeId: number;
    marka: string;
    piecesPerCarton: number;
    warehouseId: number;
    warehouseName: string;
}
<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Stock Shifting</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-primary btn-icon-text" [routerLink]="['/users/inventory/stock-shifting/new-stock-shifting']">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button (click)="onRefresh()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left" container="body"
        triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (change)="onChangeFilter($event, 'search')" type="text" class="form-control" placeholder="Search by SKU ID or Item Name"
              [ngModel]="paginationRequest.get().searchText">
          </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
          <div class="form-group-icon-end">
            @if(paginationRequest.get().dateRange) {
              <i class="th-bold-close-circle cursor-pointer" (click)="onClearDateOnly()"></i>
            }   @else {
              <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
            }
            <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
              [ngModel]="paginationRequest.get().dateRange" (ngModelChange)="onChangeFilter($event, 'date')"
              [showCustomRangeLabel]="true" [alwaysShowCalendars]="true" [ranges]="utilsService.ranges"
              [linkedCalendars]="false" [showClearButton]="false" placeholder="Shifted Date" [autoApply]="true"
              [showRangeLabelOnInput]="true" startKey="start" endKey="end">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-item-all">
          <ng-select [ngModel]="paginationRequest.get().warehouseId" (ngModelChange)="onChangeFilter($event, 'warehouseId')"
            class="" placeholder="Warehouse" [multiple]="false" [clearable]="true" [items]="warehouseDropdown" bindLabel="label"
            bindValue="value">
            <ng-template ng-option-tmp let-item="item">
              <span class="fs-13" [title]="item.label">
                {{ item.label }}
              </span>
            </ng-template>
          </ng-select>
        </div>
        <button (click)="onClear()" class="btn btn-link btn-sm">Clear</button>
      </div>
      <div class="page-filters-right">

      </div>
    </div>
    <div class="card card-theme card-table-sticky3">
      <div class="card-body p-0">
        <div class="table-responsive position-static">

          <table class="table-theme  table table-bordered tbl-collapse">
            <thead class="border-less">
              <tr>
                <th>Ticket Id</th>
                <th>Warehouse</th>
                <th>Shifted Date</th>
                <th>Last Updated By</th>
                <th>Requested By</th>
                <th>Shifting Status</th>
                <th>Reason</th>
                <th>Note</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              @for(item of stockShiftingList.get(); let i = $index; track item.id) {
                <tr (click)="onExpand(i, item)" [ngClass]="{'tbl-bg-secondary-two': item.isExpand}">
                  <td>
                    <b>#{{item.transferId}}</b>
                  </td>
                  <td>{{item.warehouseName}}</td>
                  <td>{{item.shiftingDate | date: 'dd/MM/yyyy'}}</td>
                  <td class="tbl-level">
                    <span>{{item.lastModifiedBy}}</span>
                    <span>{{item.lastModifiedDate | date: 'dd/MM/yyyy h:mm a'}}</span>
                  </td>
                  <td>{{item.createdBy}}</td>
                  <td>
                    <div [ngClass]="{ 'badge badge-primary-light': item.status === enumForShiftingStatus.IN_PROGRESS, 
                                      'badge badge-success-light': item.status === enumForShiftingStatus.ACCEPTED,
                                      'badge badge-secondary-light': item.status === enumForShiftingStatus.PENDING }">
                      {{item.status}}
                    </div>
                  </td>
                  <td class="tbl-description">
                    <div [title]="item.reasonName || ''">
                      {{item.reasonName}}
                    </div>
                  </td>
                  <td class="tbl-description">
                    <div [title]="item.note || ''">
                      {{item.note}}
                    </div>
                  </td>
                  <td class="tbl-action" (click)="$event.stopPropagation()">
                    <div class="tbl-action-group">
  
                      <button (click)="openDeleteStockShiftingModal(item)" class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Delete"
                        placement="left" container="body" triggers="hover">
                        <i class="th th-outline-trash"></i>
                      </button>
  
                      <button class="btn btn-xs btn-light-white btn-icon" [ngClass]="{'collapse-arrow': item.isExpand}"
                        (click)="onExpand(i, item)"><i class="th th-outline-arrow-right-3"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                @if(item.isExpand) {
                  <tr class="collapse" #collapse="ngbCollapse" [ngbCollapse]="!item.isExpand">
                    <td colspan="30">
                      <div class="table-responsive position-static">
    
                        <table class="table-theme  table table-bordered ">
                          <thead class="border-less">
                            <tr>
                              <th class="">Item Details</th>
                              <th>Marka</th>
                              <th>Carton Qty</th>
                              <th>Loose Qty</th>
                              <th>Current Location</th>
                              <th>Transfer Location</th>
                              <!-- <th>Audit Ticket</th> -->
                              <!-- <th>Shifting Status</th> -->
                              <th>Action</th>
    
                            </tr>
                          </thead>
                          <tbody>
                            @for (child of item.items; track $index; let j = $index) {
                              <tr>
                                <td class="tbl-user">
                                  <div class="tbl-user-checkbox-srno">
                                    <span>{{(j + 1) | padNum}}.</span>
                                    <div class="tbl-user-wrapper">
                                      <div class="tbl-user-image">
                                        <img [src]="utilsService.imgPath + child.formattedName" alt="valamji">
                                      </div>
                                      <div class="tbl-user-text-action">
                                        <div class="tbl-user-text">
                                          <p>{{child.itemName}}</p>
                                          <span>#{{child.skuId}}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  @if(child.marka === 'VIEW') {
                                    <div class="dropdown dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
                                      #dropdownInput="ngbDropdown">
                                      <button ngbDropdownToggle (click)="getStockShiftingAssociatedItemQty(item.id, child.assoItemId, i, j)"
                                        class="btn btn-link text-primary">
                                        <span class="text-primary text-link">View Marka</span>
                                      </button>
                                      <div class="dropdown-menu" ngbDropdownMenu>
                                        <div class="card-dropdown-with-tabs-tables">
                                          <div class="card-header">
                                            <div class='nav-tabs-outer nav-tabs-style2'>
                                              <nav>
                                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                                  <button class="nav-link active" id="nav-dropdowntab10-tab" data-bs-toggle="tab"
                                                    data-bs-target="#nav-dropdowntab10" type="button" role="tab" aria-controls="nav-dropdowntab10"
                                                    aria-selected="true">
                                                    <i class="th th-outline-house-2"></i>Transfer Qty</button>
                                  
                                                  <div class="dropdown-with-tabs-tables-close ms-auto">
                                                    <button (click)="dropdownInput.close()" class="btn btn-transparent btn-icon">
                                                      <i class="th th-close m-0"></i>
                                                    </button>
                                                  </div>
                                                </div>
                                              </nav>
                                            </div>
                                          </div>
                                          <div class="card-body">
                                            <div class='nav-tabs-outer nav-tabs-style2'>
                                              <div class="tab-content" id="nav-tabContent">
                                                <div class="tab-pane fade show active" id="nav-dropdowntab10" role="tabpanel"
                                                  aria-labelledby="nav-dropdowntab10-tab">
                                                  <div class="table-responsive ">
                                                    <table class="table-theme  table table-bordered table-sticky">
                                                      <thead class="border-less">
                                                        <tr>
                                                          <th class="">Marka</th>
                                                          <th>Carton</th>
                                                          <th>PCS/Carton</th>
                                                          <th>Total Carton <br /> Qty</th>
                                                          <th>Loose Qty</th>
                                                          <th>Total Qty</th>
                                                        </tr>
                                                      </thead>
                                                      <tbody>
                                                        @for(sub of child.data; track $index; let k = $index) {
                                                        <tr>
                                                          <td><b>{{sub.marka}}</b></td>
                                                          <td>{{sub.qtyCarton}}</td>
                                                          <td>{{sub.piecesPerCarton}}</td>
                                                          <td>{{sub.qtyCartonItem}}</td>
                                                          <td>{{sub.qtyLoose}}</td>
                                                          <td>{{sub.totalQty}}</td>
                                                        </tr>
                                                        } @empty {
                                                        <tr>
                                                          <td colspan="20" class="text-center">
                                                            <span
                                                              class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                          </td>
                                                        </tr>
                                                        }
                                                      </tbody>
                                                      <tfoot>
                                                        <tr class="fw-semibold bg-white border-top">
                                                          <td>-</td>
                                                          <td>{{child?.data?.[0]?.total?.qtyCarton}}</td>
                                                          <td>-</td>
                                                          <td>{{child?.data?.[0]?.total?.qtyCartonItem}}</td>
                                                          <td>{{child?.data?.[0]?.total?.qtyLoose}}</td>
                                                          <td>{{child?.data?.[0]?.total?.totalQty}}</td>
                                                        </tr>
                                                      </tfoot>
                                                    </table>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  } @else {
                                    <span>-</span>
                                  }
                                </td>
                                <td>{{child.cartonQty || '-'}}</td>
                                <td>{{child.looseQty || '-'}}</td>
                                <td>{{child.fromLocation || '-'}}</td>
                                <td>{{child.toLocation || '-'}}</td>
                                <!-- <td></td> -->
                                <!-- <td></td> -->
                                <td class="tbl-action">
                                  <div class="tbl-action-group">
                                    <div class="dropdown dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
                                      #dropdownT="ngbDropdown" container="body">
                                      <button ngbDropdownToggle (click)="getTicketsStatusByTicketTypeAndAssociatedItem(item.id, child.assoItemId, i, j)"
                                        class="btn btn-light-primary btn-icon btn-sm" type="button">
                                        <i class="th th-outline-ticket-star ms-2 fs-13"></i>
                                      </button>
                                      <div class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdownT.isOpen" ngbDropdownMenu>
                                        <div class="card-dropdown-menu">
                                          <div class="card-body">
                                            <div class="table-responsive ">
                                              <table class="table-theme table table-bordered">
                                                <thead class="border-less">
                                                  <tr>
                                                    <th>Ticket ID</th>
                                                    <th>Status</th>
                                                    <th>Assign to</th>
                                                    <th>Assign On</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  @for(ticket of child.ticketData; track $index; let k = $index) {
                                                  <tr>
                                                    <td class="tbl-bold">#{{ticket.ticketId}}</td>
                                                    <td
                                                      [ngClass]="{  'text-primary': ticket.status === enumForTicketStatus.IN_PROGRESS, 
                                                                    'text-success': ticket.status === enumForTicketStatus.COMPLETED,
                                                                    'text-secondary': ticket.status === enumForTicketStatus.PENDING }">
                                                      {{ticket.status}}
                                                    </td>
                                                    <td>{{ticket.assignTo}}</td>
                                                    <td>{{ticket.assignDateTime | date: 'dd/MM/yyyy h:mm a'}}</td>
                                                  </tr>
                                                  } @empty {
                                                  <tr>
                                                    <td colspan="20" class="text-center">
                                                      <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                    </td>
                                                  </tr>
                                                  }
                                                </tbody>
                                              </table>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            }
                          </tbody>
                        </table>
                      </div>
                    </td>
                  </tr>
                }
              } @empty {
                <tr>
                  <td colspan="20" class="text-center">
                    <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                  </td>
                </tr>
              }
            </tbody>
          </table>

        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.get().pageNo" [pageSize]="paginationRequest.get().pageSize"
        [totalData]="paginationRequest.get().totalData">
      </app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteStockShiftingModal" tabindex="-1"
  aria-labelledby="deleteStockShiftingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete Stock Shifting record <br/> <b>#{{stockShiftingObj.get().transferId}}</b></p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteStockShifting()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->
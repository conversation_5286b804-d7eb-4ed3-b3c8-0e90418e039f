<div class="dropdown dropdown-attachments" *ngIf="!utilsService.isEmptyObjectOrNullUndefined(fileList)">
  <div ngbDropdown #dropdownAtt="ngbDropdown" autoClose="outside" container="body">    
    <button ngbTooltip="Attachments" placement="left" container="body" triggers="hover" id="tbl-attachments-dropdown"
      class="btn btn-xs btn-light-white btn-icon-text w-auto no-caret" ngbDropdownToggle>
      <i class="th th-outline-attach-circle"></i> <span class="">{{fileList.length}}</span>
    </button>
    <div ngbDropdownMenu dropdownClass="dropdown-menu-attachment" class="dropdown-menu-attachment" aria-labelledby="tbl-attachments-dropdown" *ngIf="dropdownAtt.isOpen">
      <div class="attachments-card-wrapper attachments-sticky">
        <div class="attachments-card-header">
          <div class="card-details-left">
            <h6 class="title"> Attachments </h6>
          </div>
          <div class="card-details-right">
            <button (click)="closeDropdown()" class="btn btn-sm btn-transparent btn-icon text-danger">
              <i class="th th-close m-0"></i>
            </button>
          </div>
        </div>
        <div class="attachments-card-body" *ngIf="!local">
          <div class="attachments-items " *ngFor="let item of fileList; index as i">
            <div class="attachments-user-image small-image">
              <img *ngIf="utilsService.isImage(item.formattedName)"
                [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null" alt="valamji">
              <img *ngIf="utilsService.isDocument(item.formattedName)" src="assets/images/dummy-pdf.png" alt="valamji">
              <img *ngIf="utilsService.isExcel(item.formattedName)" src="assets/images/files/file-excel.svg"
                alt="valamji">
            </div>
            <div class="attachments-user-text-action">
              <div class="attachments-user-text">
                <p>{{item.originalName}}</p>
                <!-- <span>File Size: 1.6 MB</span> -->
              </div>
              <div class="user-action">
                <button (click)="downloadAttachment(item)" class="btn btn-xs btn-transparent btn-icon"
                  ngbTooltip="Download" placement="bottom" container="body" triggers="hover">
                  <i class="bi bi-download"></i>
                </button>
                <button *ngIf="isDelete" (click)="openDocModal(i)" class="btn btn-xs btn-transparent btn-icon text-danger"
                  ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                  <i class="th th-outline-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="attachments-card-body" *ngIf="local">
          <div class="attachments-items " *ngFor="let item of fileList; index as i">
            <div class="attachments-user-image small-image">
              <img *ngIf="utilsService.isImage(item.formattedName ? item.formattedName : item.fileName)"
                [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : item.originalname"
                alt="valamji">
              <img *ngIf="utilsService.isDocument(item.formattedName ? item.formattedName : item.fileName)"
                src="assets/images/dummy-pdf.png" alt="valamji">
              <img *ngIf="utilsService.isExcel(item.formattedName ? item.formattedName : item.fileName)"
                src="assets/images/files/file-excel.svg" alt="valamji">
            </div>
            <div class="attachments-user-text-action">
              <div class="attachments-user-text">
                <p>{{item.originalName ? item.originalName : item.fileName}}</p>
              </div>
              <div class="user-action">
                <button *ngIf="item.file" (click)="openLinkLocal(null, item.originalname, null)" ngbTooltip="View"
                  placement="bottom" container="body" triggers="hover" class="btn btn-xs btn-transparent btn-icon">
                  <i class="bi bi-download"></i>
                </button>
                <button *ngIf="!item.file" (click)="openLinkLocal(item.formattedName, null, item.originalName)" ngbTooltip="Download"
                  placement="bottom" container="body" triggers="hover" class="btn btn-xs btn-transparent btn-icon">
                  <i class="bi bi-download"></i>
                </button>
                <button (click)="openImgItem(i)" class="btn btn-xs btn-transparent btn-icon text-danger"
                  ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                  <i class="th th-outline-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
import { Injectable, TemplateRef, computed, inject, signal } from '@angular/core';
import { createArraySignal, createObjectSignal } from '@libs';
import { AuditTicketPagination } from '@modal/request/AuditTicketPagination';
import { UtilsService } from '@service/utils.service';
import { Subject, tap, map, takeUntil, of, catchError } from 'rxjs';
import { AuditTicketListing, TicketListing } from '@modal/AuditTicket';
import { NgbModalService } from '@service/ngb-modal.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import dayjs from 'dayjs';
import { FormControl, Validators } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import saveAs from 'file-saver';
import { Router } from '@angular/router';
import { AuditTicketsModule } from '../audit-tickets.module';
import { EnumForTicketStatus } from '@enums/EnumForTicketStatus';
import { NumberInput } from '@angular/cdk/coercion';
import { EnumForTicketType, EnumForTicketTypeLabel } from '@enums/EnumForQCProperty';

const EnumForTabs = {
  NEW: 'NEW',
  RESOLVED: 'RESOLVED',
  DELETED: 'DELETED'
} as const

@Injectable({
  providedIn: AuditTicketsModule
})

export class AuditTicketService {

  readonly utilsService = inject(UtilsService)
  private readonly modalService = inject(NgbModalService)

  userControl = new FormControl<string | null>(null, Validators.required);

  enumForTabs = EnumForTabs;
  enumForTicketStatus = EnumForTicketStatus;
  enumForTicketType = EnumForTicketType;
  enumForTicketTypeLabel = EnumForTicketTypeLabel;
  selectedTab = signal<string>(this.enumForTabs.NEW);

  paginationRequest = createObjectSignal({} as AuditTicketPagination);
  auditTicketList = createArraySignal([] as AuditTicketListing[]);
  ticketObj = createObjectSignal({} as TicketListing);

  flagForExpandAll = computed(() => this.auditTicketList.get().every(item => item.isExpand));
  isAuditTicketListEmpty = computed(() => this.auditTicketList.get()?.length === 0);

  reAssignUserDropdown = [];

  destroy$ = new Subject<void>();
  private dateCallCount = 0;

  constructor(private router: Router) { }

  initPagination() {
    this.paginationRequest.update(a => ({ ...a, markAsResolved: false }));
    this.onChangeTab(this.enumForTabs.NEW);
  }

  getAuditTicketList = () => {
    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])

    this.utilsService.post(this.utilsService.serverVariableService.FETCH_TICKETS, param, null, true).pipe(
      tap((res) => {
        this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'], pagination: res.pagination }));
      }),
      map((res) => res?.['content']),
      tap((data: AuditTicketListing[]) => {
        this.auditTicketList.set(data)

        this.auditTicketList.update(list => list.map(group => ({
          ...group,
          tickets: group.tickets.map(ticket => {
            const parts = (ticket.assignTo || '').split(',').map(p => p.trim()).filter(Boolean);
            
            if (parts.length > 1) {
              const assignDay = parts.pop() || null;
              return { ...ticket, assignTo: parts.join(', '), assignDay };
            }

            return { ...ticket, assignDay: null };
          })
        })));
        this.auditTicketList.update(list => list.map(item => ({ ...item, isExpand: true })));
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // API calling by converting object to signal (called on page load)
  dropdown = toSignal(this.utilsService.get(this.utilsService.serverVariableService.AUDIT_TICKET_DROPDOWN, null, true).pipe(
    takeUntil(this.destroy$),
    catchError(() => {
      return of(null);
    })), { initialValue: null }
  );

  onExpand = (index: number) => {
    this.auditTicketList.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
  }

  onExpandAll = () => {
    this.auditTicketList.update(a => a.map(item => ({ ...item, isExpand: this.flagForExpandAll() ? false : true })))
  }

  onChangeTab(tab: string) {
    this.auditTicketList.set([])
    this.selectedTab.set(tab);

    if (tab === this.enumForTabs.RESOLVED) {
      this.paginationRequest.update(a => ({ ...a, markAsResolved: true }));
    } else {
      this.paginationRequest.update(a => ({ ...a, markAsResolved: false }));
    }

    this.onClear()
  }

  // Ticket Changes Modals
  openTicketModal(item: TicketListing, content: TemplateRef<any>, isAssignModal = false) {
    this.ticketObj.set(item);
    if (!isAssignModal) {
      this.modalService.open(content);
    } else {
      this.userControl.reset();
        const API = this.utilsService.serverVariableService.GET_AVAILABLE_USERS_FOR_REASSIGN;
        const param = { ticketId: item.id }
        this.utilsService.post(API, param, { toast: false }, true).pipe(
          tap((res) => {
            this.modalService.open(content, { windowClass: 'modal-lg' });
            this.reAssignUserDropdown = this.utilsService.transformDropdownItems(res || []);
            if (item.assignToId && item.ticketType === this.enumForTicketType.REAL_PHOTO) {
                const matchingOption = this.reAssignUserDropdown.find(option => option.id === item.assignToId);
                if (matchingOption) {
                  this.userControl.setValue(matchingOption.id);
                }
            }
          }),
          takeUntil(this.destroy$)
        ).subscribe();
    }
  }

  openResolveTicketModal = (item: TicketListing, content: TemplateRef<any>) => {
    this.ticketObj.set(item);
    this.modalService.open(content);
  }

  onTicketChanges(modal: NgbModalRef, type: 'approve' | 'delete') {
    const API = type === 'approve' ? `${this.utilsService.serverVariableService.AUDIT_TICKET_APPROVE}?ticketId=${this.ticketObj.get()?.id}`
      : `${this.utilsService.serverVariableService.AUDIT_TICKET_DELETE}?id=${this.ticketObj.get()?.id}`;
    const request = type === 'delete' ? this.utilsService.delete(API, { toast: true }, true) : this.utilsService.post(API, null, { toast: true }, true);

    request.pipe(
      tap(() => {
        this.modalService.close(modal);
        this.getAuditTicketList();
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onReAssignTicket = (modal: NgbModalRef) => {

    const API = this.utilsService.serverVariableService.RE_ASSIGN_AUDIT_TICKET;

    const param = {
      ticketId: this.ticketObj.get()?.id,
      userId: this.userControl.value
    }

    this.utilsService.post(API, param, { toast: true }, true).pipe(
      tap(() => {
        this.modalService.close(modal);
        this.getAuditTicketList();
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onChangeFilter = (event: any, type: 'date' | 'search' | 'assignTo' | 'subjectStatus' | 'ticketType' | 'ticketSubject', isDateClear?: boolean, isAllClear = false) => {
    switch (type) {
      case 'date':
        this.dateCallCount++;

        // Skip first 2 calls
        if (this.dateCallCount <= 2 && (!event || (event?.start === null && event?.end === null)) && !isAllClear) {
          return;
        }
        if (event?.start === null && event?.end === null && !isDateClear) {
          return;
        }
        const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
        const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
        this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
        break;
      case 'search':
        this.paginationRequest.update(a => ({ ...a, searchById: event.target.value }));
        break;
      case 'assignTo':
        this.paginationRequest.update(a => ({ ...a, assignTo: event }));
        break;
      case 'subjectStatus':
        this.paginationRequest.update(a => ({ ...a, subjectStatus: event }));
        break;
      case 'ticketType':
        this.paginationRequest.update(a => ({ ...a, ticketType: event }));
        break;
      case 'ticketSubject':
        this.paginationRequest.update(a => ({ ...a, ticketSubject: event }));
        break;
    }
    this.destroy$.complete()
    if (!isAllClear) {
      this.getAuditTicketList();
    }
  }

  onClearDateOnly = () => {
    this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null }));
    this.onChangeFilter(null, 'date', true);
  }

  // Filters Clear
  onClear = () => {
    this.paginationRequest.update(a => ({
      ...a, fromDate: null, toDate: null, dateRange: null, searchById: null, assignTo: null, subjectStatus: null, ticketType: null, ticketSubject: null
    }));
    this.getAuditTicketList();
  }

  //Refresh
  onRefresh = () => {
    this.getAuditTicketList();
  }

  // Export Excel
  exportReport = () => {
    const { fromDate, toDate, assignTo, subjectStatus, ticketType, ticketSubject, searchById, markAsResolved } = this.paginationRequest.get()
    const param = { fromDate, toDate, assignTo, subjectStatus, ticketType, ticketSubject, searchById, markAsResolved }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.AUDIT_TICKET_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), `Audit Ticket Sheet`);
    });
  }

  redirectToDetails(ticketId: NumberInput, branchId: number): void {
    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])
    this.router.navigate(['/users/audit-tickets/audit-tickets-details', ticketId], {
      queryParams: { branchId },
      state: { searchParam : param }
    });
  }

}

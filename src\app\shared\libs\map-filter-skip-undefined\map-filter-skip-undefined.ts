import { type Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export function mapSkipUndefined<T, R>(fnTrasformSkipUndefined: (value: T) => R) {
	return function (source: Observable<T>) {
		return source.pipe(map(fnTrasformSkipUndefined), filterSubUndefined());
	};
}

export const filterSubUndefined = <T>() => filter((value: T): value is Exclude<T, undefined | null | ''> => value !== undefined && value !== null && value !== '');

export const isNotNilOrEmpty = <T>(value: T): value is Exclude<T, undefined | null | ''> => value !== undefined && value !== null && value !== '';

export const hasDuplicates = (arr: number[]): boolean => {
	return new Set(arr).size !== arr.length;
}
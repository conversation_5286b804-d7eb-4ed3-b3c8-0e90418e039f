import { deserializeAs, serializeAs } from "cerialize";

export class ItemSupplier {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isItemSave')
    @deserializeAs('isItemSave')
    private _isItemSave: boolean;

    @serializeAs('cbm')
    @deserializeAs('cbm')
    private _cbm: number;

    @deserializeAs('totalQty')
    private _totalQty: number;

    @deserializeAs('isDeleteFlag')
    private _isDeleteFlag: boolean;

    @deserializeAs('supplierName')
    private _supplierName: string;

    @deserializeAs('itemIsActive')
    private _itemIsActive: boolean;

    @serializeAs('supplierSku')
    @deserializeAs('supplierSku')
    private _supplierSku: string;

    @serializeAs('cartonLength')
    @deserializeAs('cartonLength')
    private _cartonLength: number;

    @serializeAs('cartonWidth')
    @deserializeAs('cartonWidth')
    private _cartonWidth: number;

    @serializeAs('cartonHeight')
    @deserializeAs('cartonHeight')
    private _cartonHeight: number;

    @serializeAs('cartonWeight')
    @deserializeAs('cartonWeight')
    private _cartonWeight: number;

    @serializeAs('cartonQuantity')
    @deserializeAs('cartonQuantity')
    private _cartonQuantity: number;

    @serializeAs('pricePerItem')
    @deserializeAs('pricePerItem')
    private _pricePerItem: number;

    @serializeAs('pricePerCarton')
    @deserializeAs('pricePerCarton')
    private _pricePerCarton: number;

    @serializeAs('tag')
    @deserializeAs('tag')
    private _tag: string;

    @serializeAs('englishComment')
    @deserializeAs('englishComment')
    private _englishComment: string;

    @serializeAs('chinaComment')
    @deserializeAs('chinaComment')
    private _chinaComment: string;

    @serializeAs('measurementCode')
    @deserializeAs('measurementCode')
    private _measurementCode: string;

    @serializeAs('supplierId')
    @deserializeAs('supplierId')
    private _supplierId: number;

    @serializeAs('hsnCodeId')
    @deserializeAs('hsnCodeId')
    private _hsnCodeId: number;

    @serializeAs('unitId')
    @deserializeAs('unitId')
    private _unitId: number;

    @serializeAs('cartonWeightDim')
    @deserializeAs('cartonWeightDim')
    private _cartonWeightDim: number;

    @serializeAs('fileIndexes')
    @deserializeAs('fileIndexes')
    private _fileIndexes: number[];

    @serializeAs('colorIds')
    @deserializeAs('colorIds')
    private _colorIds: number[];

    @serializeAs('packingTypeIds')
    @deserializeAs('packingTypeIds')
    private _packingTypeIds: number[];

    @serializeAs('itemLength')
    @deserializeAs('itemLength')
    private _itemLength: number;

    @serializeAs('itemWidth')
    @deserializeAs('itemWidth')
    private _itemWidth: number;

    @serializeAs('itemHeight')
    @deserializeAs('itemHeight')
    private _itemHeight: number;

    @serializeAs('itemDimUnitMaster')
    @deserializeAs('itemDimUnitMaster')
    private _itemDimUnitMaster: number;

    @serializeAs('itemWeight')
    @deserializeAs('itemWeight')
    private _itemWeight: number;

    @serializeAs('itemWithBoxLength')
    @deserializeAs('itemWithBoxLength')
    private _itemWithBoxLength: number;

    @serializeAs('itemWithBoxWidth')
    @deserializeAs('itemWithBoxWidth')
    private _itemWithBoxWidth: number;

    @serializeAs('itemWithBoxHeight')
    @deserializeAs('itemWithBoxHeight')
    private _itemWithBoxHeight: number;

    @serializeAs('itemWithBoxDimUnitMaster')
    @deserializeAs('itemWithBoxDimUnitMaster')
    private _itemWithBoxDimUnitMaster: number;

    @serializeAs('itemWithBoxWeight')
    @deserializeAs('itemWithBoxWeight')
    private _itemWithBoxWeight: number;

    // @serializeAs('poColorId')
    @deserializeAs('poColorId')
    private _poColorId: number[];

    @deserializeAs('images')
    private _images: any[];

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    @deserializeAs('unitDropdown')
    private _unitDropdown: any[];

    @deserializeAs('cartownWdropdown')
    private _cartownWdropdown: any[];

    @deserializeAs('colorDropdown')
    private _colorDropdown: any[];

    @deserializeAs('hsnCode')
    private _hsnCode: string;

    @deserializeAs('imageIndex')
    private _imageIndex: number;

    @deserializeAs('itemColors')
    private _itemColors: any;

    @deserializeAs('itemDocList')
    private _itemDocList: any[];

    @deserializeAs('itemSKUId')
    private _itemSKUId: any;

    @deserializeAs('itemName')
    private _itemName: any;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @deserializeAs('marketType')
    private _marketType: string;

    @deserializeAs('cartonPrice')
    private _cartonPrice: string;

    @deserializeAs('itemPrice')
    private _itemPrice: string;

    @deserializeAs('cartonWeightDimId')
    private _cartonWeightDimId: number;

    @deserializeAs('itemFileFormatedName')
    private _itemFileFormatedName: string;

    @deserializeAs('breachAlert')
    private _breachAlert: any;

    @deserializeAs('displayName')
    private _displayName: any;

    @deserializeAs('skuId')
    private _skuId: any;

    @deserializeAs('formattedName')
    private _formattedName: any;

    @deserializeAs('color')
    private _color: any;

    @deserializeAs('averagePriceWithGST')
    private _averagePriceWithGST: number;

    @deserializeAs('averagePriceWithoutGST')
    private _averagePriceWithoutGST: number;

    @deserializeAs('packingTypes')
    private _packingTypes: any[];

    @deserializeAs('itemDimArr')
    private _itemDimArr: any[];

    @deserializeAs('itemBoxDimArr')
    private _itemBoxDimArr: any[];

    @deserializeAs('supplierDropdown')
    private _supplierDropdown: any[];

    @deserializeAs('suppColor')
    private _suppColor: any[];

    @serializeAs('itemWithBoxDimUnitMasterId')
    @deserializeAs('itemWithBoxDimUnitMasterId')
    private _itemWithBoxDimUnitMasterId: number;

    @serializeAs('itemDimUnitMasterId')
    @deserializeAs('itemDimUnitMasterId')
    private _itemDimUnitMasterId: number;

    @deserializeAs('regId')
    private _regId: number;

    constructor() {
        this.isDeleteFlag = false;
        this.isSelected = false;
        this.itemIsActive = false;
        this.fileIndexes = [];
        this.colorIds = [];
        this.poColorId = []
        this.images = [];
        this.colorDropdown = [];
        this.itemDropdown = [];
        this.unitDropdown = [];
        this.itemDocList = [];
        this.cartownWdropdown = [];
        this.packingTypeIds = [];
        this.packingTypes = [];
        this.itemDimArr = [];
        this.itemBoxDimArr = [];
        this.supplierDropdown = [];
        this.isItemSave = true;
        this.suppColor = [];
    }


    /**
     * Getter suppColor
     * @return {any[]}
     */
	public get suppColor(): any[] {
		return this._suppColor;
	}

    /**
     * Setter suppColor
     * @param {any[]} value
     */
	public set suppColor(value: any[]) {
		this._suppColor = value;
	}


    /**
     * Getter supplierName
     * @return {string}
     */
	public get supplierName(): string {
		return this._supplierName;
	}

    /**
     * Setter supplierName
     * @param {string} value
     */
	public set supplierName(value: string) {
		this._supplierName = value;
	}


    /**
     * Getter regId
     * @return {number}
     */
	public get regId(): number {
		return this._regId;
	}

    /**
     * Setter regId
     * @param {number} value
     */
	public set regId(value: number) {
		this._regId = value;
	}


    /**
     * Getter itemWithBoxDimUnitMasterId
     * @return {number}
     */
	public get itemWithBoxDimUnitMasterId(): number {
		return this._itemWithBoxDimUnitMasterId;
	}

    /**
     * Getter itemDimUnitMasterId
     * @return {number}
     */
	public get itemDimUnitMasterId(): number {
		return this._itemDimUnitMasterId;
	}

    /**
     * Setter itemWithBoxDimUnitMasterId
     * @param {number} value
     */
	public set itemWithBoxDimUnitMasterId(value: number) {
		this._itemWithBoxDimUnitMasterId = value;
	}

    /**
     * Setter itemDimUnitMasterId
     * @param {number} value
     */
	public set itemDimUnitMasterId(value: number) {
		this._itemDimUnitMasterId = value;
	}


    /**
     * Getter isItemSave
     * @return {boolean}
     */
	public get isItemSave(): boolean {
		return this._isItemSave;
	}

    /**
     * Setter isItemSave
     * @param {boolean} value
     */
	public set isItemSave(value: boolean) {
		this._isItemSave = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter cbm
     * @return {number}
     */
	public get cbm(): number {
		return this._cbm;
	}

    /**
     * Getter totalQty
     * @return {number}
     */
	public get totalQty(): number {
		return this._totalQty;
	}

    /**
     * Getter isDeleteFlag
     * @return {boolean}
     */
	public get isDeleteFlag(): boolean {
		return this._isDeleteFlag;
	}

    /**
     * Getter itemIsActive
     * @return {boolean}
     */
	public get itemIsActive(): boolean {
		return this._itemIsActive;
	}

    /**
     * Getter supplierSku
     * @return {string}
     */
	public get supplierSku(): string {
		return this._supplierSku;
	}

    /**
     * Getter cartonLength
     * @return {number}
     */
	public get cartonLength(): number {
		return this._cartonLength;
	}

    /**
     * Getter cartonWidth
     * @return {number}
     */
	public get cartonWidth(): number {
		return this._cartonWidth;
	}

    /**
     * Getter cartonHeight
     * @return {number}
     */
	public get cartonHeight(): number {
		return this._cartonHeight;
	}

    /**
     * Getter cartonWeight
     * @return {number}
     */
	public get cartonWeight(): number {
		return this._cartonWeight;
	}

    /**
     * Getter cartonQuantity
     * @return {number}
     */
	public get cartonQuantity(): number {
		return this._cartonQuantity;
	}

    /**
     * Getter pricePerItem
     * @return {number}
     */
	public get pricePerItem(): number {
		return this._pricePerItem;
	}

    /**
     * Getter pricePerCarton
     * @return {number}
     */
	public get pricePerCarton(): number {
		return this._pricePerCarton;
	}

    /**
     * Getter tag
     * @return {string}
     */
	public get tag(): string {
		return this._tag;
	}

    /**
     * Getter englishComment
     * @return {string}
     */
	public get englishComment(): string {
		return this._englishComment;
	}

    /**
     * Getter chinaComment
     * @return {string}
     */
	public get chinaComment(): string {
		return this._chinaComment;
	}

    /**
     * Getter measurementCode
     * @return {string}
     */
	public get measurementCode(): string {
		return this._measurementCode;
	}

    /**
     * Getter supplierId
     * @return {number}
     */
	public get supplierId(): number {
		return this._supplierId;
	}

    /**
     * Getter hsnCodeId
     * @return {number}
     */
	public get hsnCodeId(): number {
		return this._hsnCodeId;
	}

    /**
     * Getter unitId
     * @return {number}
     */
	public get unitId(): number {
		return this._unitId;
	}

    /**
     * Getter cartonWeightDim
     * @return {number}
     */
	public get cartonWeightDim(): number {
		return this._cartonWeightDim;
	}

    /**
     * Getter fileIndexes
     * @return {number[]}
     */
	public get fileIndexes(): number[] {
		return this._fileIndexes;
	}

    /**
     * Getter colorIds
     * @return {number[]}
     */
	public get colorIds(): number[] {
		return this._colorIds;
	}

    /**
     * Getter packingTypeIds
     * @return {number[]}
     */
	public get packingTypeIds(): number[] {
		return this._packingTypeIds;
	}

    /**
     * Getter itemLength
     * @return {number}
     */
	public get itemLength(): number {
		return this._itemLength;
	}

    /**
     * Getter itemWidth
     * @return {number}
     */
	public get itemWidth(): number {
		return this._itemWidth;
	}

    /**
     * Getter itemHeight
     * @return {number}
     */
	public get itemHeight(): number {
		return this._itemHeight;
	}

    /**
     * Getter itemDimUnitMaster
     * @return {number}
     */
	public get itemDimUnitMaster(): number {
		return this._itemDimUnitMaster;
	}

    /**
     * Getter itemWeight
     * @return {number}
     */
	public get itemWeight(): number {
		return this._itemWeight;
	}

    /**
     * Getter itemWithBoxLength
     * @return {number}
     */
	public get itemWithBoxLength(): number {
		return this._itemWithBoxLength;
	}

    /**
     * Getter itemWithBoxWidth
     * @return {number}
     */
	public get itemWithBoxWidth(): number {
		return this._itemWithBoxWidth;
	}

    /**
     * Getter itemWithBoxHeight
     * @return {number}
     */
	public get itemWithBoxHeight(): number {
		return this._itemWithBoxHeight;
	}

    /**
     * Getter itemWithBoxDimUnitMaster
     * @return {number}
     */
	public get itemWithBoxDimUnitMaster(): number {
		return this._itemWithBoxDimUnitMaster;
	}

    /**
     * Getter itemWithBoxWeight
     * @return {number}
     */
	public get itemWithBoxWeight(): number {
		return this._itemWithBoxWeight;
	}

    /**
     * Getter poColorId
     * @return {number[]}
     */
	public get poColorId(): number[] {
		return this._poColorId;
	}

    /**
     * Getter images
     * @return {any[]}
     */
	public get images(): any[] {
		return this._images;
	}

    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Getter unitDropdown
     * @return {any[]}
     */
	public get unitDropdown(): any[] {
		return this._unitDropdown;
	}

    /**
     * Getter cartownWdropdown
     * @return {any[]}
     */
	public get cartownWdropdown(): any[] {
		return this._cartownWdropdown;
	}

    /**
     * Getter colorDropdown
     * @return {any[]}
     */
	public get colorDropdown(): any[] {
		return this._colorDropdown;
	}

    /**
     * Getter hsnCode
     * @return {string}
     */
	public get hsnCode(): string {
		return this._hsnCode;
	}

    /**
     * Getter imageIndex
     * @return {number}
     */
	public get imageIndex(): number {
		return this._imageIndex;
	}

    /**
     * Getter itemColors
     * @return {any}
     */
	public get itemColors(): any {
		return this._itemColors;
	}

    /**
     * Getter itemDocList
     * @return {any[]}
     */
	public get itemDocList(): any[] {
		return this._itemDocList;
	}

    /**
     * Getter itemSKUId
     * @return {any}
     */
	public get itemSKUId(): any {
		return this._itemSKUId;
	}

    /**
     * Getter itemName
     * @return {any}
     */
	public get itemName(): any {
		return this._itemName;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter marketType
     * @return {string}
     */
	public get marketType(): string {
		return this._marketType;
	}

    /**
     * Getter cartonPrice
     * @return {string}
     */
	public get cartonPrice(): string {
		return this._cartonPrice;
	}

    /**
     * Getter itemPrice
     * @return {string}
     */
	public get itemPrice(): string {
		return this._itemPrice;
	}

    /**
     * Getter cartonWeightDimId
     * @return {number}
     */
	public get cartonWeightDimId(): number {
		return this._cartonWeightDimId;
	}

    /**
     * Getter itemFileFormatedName
     * @return {string}
     */
	public get itemFileFormatedName(): string {
		return this._itemFileFormatedName;
	}

    /**
     * Getter breachAlert
     * @return {any}
     */
	public get breachAlert(): any {
		return this._breachAlert;
	}

    /**
     * Getter displayName
     * @return {any}
     */
	public get displayName(): any {
		return this._displayName;
	}

    /**
     * Getter skuId
     * @return {any}
     */
	public get skuId(): any {
		return this._skuId;
	}

    /**
     * Getter formattedName
     * @return {any}
     */
	public get formattedName(): any {
		return this._formattedName;
	}

    /**
     * Getter color
     * @return {any}
     */
	public get color(): any {
		return this._color;
	}

    /**
     * Getter averagePriceWithGST
     * @return {number}
     */
	public get averagePriceWithGST(): number {
		return this._averagePriceWithGST;
	}

    /**
     * Getter averagePriceWithoutGST
     * @return {number}
     */
	public get averagePriceWithoutGST(): number {
		return this._averagePriceWithoutGST;
	}

    /**
     * Getter packingTypes
     * @return {any[]}
     */
	public get packingTypes(): any[] {
		return this._packingTypes;
	}

    /**
     * Getter itemDimArr
     * @return {any[]}
     */
	public get itemDimArr(): any[] {
		return this._itemDimArr;
	}

    /**
     * Getter itemBoxDimArr
     * @return {any[]}
     */
	public get itemBoxDimArr(): any[] {
		return this._itemBoxDimArr;
	}

    /**
     * Getter supplierDropdown
     * @return {any[]}
     */
	public get supplierDropdown(): any[] {
		return this._supplierDropdown;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter cbm
     * @param {number} value
     */
	public set cbm(value: number) {
		this._cbm = value;
	}

    /**
     * Setter totalQty
     * @param {number} value
     */
	public set totalQty(value: number) {
		this._totalQty = value;
	}

    /**
     * Setter isDeleteFlag
     * @param {boolean} value
     */
	public set isDeleteFlag(value: boolean) {
		this._isDeleteFlag = value;
	}

    /**
     * Setter itemIsActive
     * @param {boolean} value
     */
	public set itemIsActive(value: boolean) {
		this._itemIsActive = value;
	}

    /**
     * Setter supplierSku
     * @param {string} value
     */
	public set supplierSku(value: string) {
		this._supplierSku = value;
	}

    /**
     * Setter cartonLength
     * @param {number} value
     */
	public set cartonLength(value: number) {
		this._cartonLength = value;
	}

    /**
     * Setter cartonWidth
     * @param {number} value
     */
	public set cartonWidth(value: number) {
		this._cartonWidth = value;
	}

    /**
     * Setter cartonHeight
     * @param {number} value
     */
	public set cartonHeight(value: number) {
		this._cartonHeight = value;
	}

    /**
     * Setter cartonWeight
     * @param {number} value
     */
	public set cartonWeight(value: number) {
		this._cartonWeight = value;
	}

    /**
     * Setter cartonQuantity
     * @param {number} value
     */
	public set cartonQuantity(value: number) {
		this._cartonQuantity = value;
	}

    /**
     * Setter pricePerItem
     * @param {number} value
     */
	public set pricePerItem(value: number) {
		this._pricePerItem = value;
	}

    /**
     * Setter pricePerCarton
     * @param {number} value
     */
	public set pricePerCarton(value: number) {
		this._pricePerCarton = value;
	}

    /**
     * Setter tag
     * @param {string} value
     */
	public set tag(value: string) {
		this._tag = value;
	}

    /**
     * Setter englishComment
     * @param {string} value
     */
	public set englishComment(value: string) {
		this._englishComment = value;
	}

    /**
     * Setter chinaComment
     * @param {string} value
     */
	public set chinaComment(value: string) {
		this._chinaComment = value;
	}

    /**
     * Setter measurementCode
     * @param {string} value
     */
	public set measurementCode(value: string) {
		this._measurementCode = value;
	}

    /**
     * Setter supplierId
     * @param {number} value
     */
	public set supplierId(value: number) {
		this._supplierId = value;
	}

    /**
     * Setter hsnCodeId
     * @param {number} value
     */
	public set hsnCodeId(value: number) {
		this._hsnCodeId = value;
	}

    /**
     * Setter unitId
     * @param {number} value
     */
	public set unitId(value: number) {
		this._unitId = value;
	}

    /**
     * Setter cartonWeightDim
     * @param {number} value
     */
	public set cartonWeightDim(value: number) {
		this._cartonWeightDim = value;
	}

    /**
     * Setter fileIndexes
     * @param {number[]} value
     */
	public set fileIndexes(value: number[]) {
		this._fileIndexes = value;
	}

    /**
     * Setter colorIds
     * @param {number[]} value
     */
	public set colorIds(value: number[]) {
		this._colorIds = value;
	}

    /**
     * Setter packingTypeIds
     * @param {number[]} value
     */
	public set packingTypeIds(value: number[]) {
		this._packingTypeIds = value;
	}

    /**
     * Setter itemLength
     * @param {number} value
     */
	public set itemLength(value: number) {
		this._itemLength = value;
	}

    /**
     * Setter itemWidth
     * @param {number} value
     */
	public set itemWidth(value: number) {
		this._itemWidth = value;
	}

    /**
     * Setter itemHeight
     * @param {number} value
     */
	public set itemHeight(value: number) {
		this._itemHeight = value;
	}

    /**
     * Setter itemDimUnitMaster
     * @param {number} value
     */
	public set itemDimUnitMaster(value: number) {
		this._itemDimUnitMaster = value;
	}

    /**
     * Setter itemWeight
     * @param {number} value
     */
	public set itemWeight(value: number) {
		this._itemWeight = value;
	}

    /**
     * Setter itemWithBoxLength
     * @param {number} value
     */
	public set itemWithBoxLength(value: number) {
		this._itemWithBoxLength = value;
	}

    /**
     * Setter itemWithBoxWidth
     * @param {number} value
     */
	public set itemWithBoxWidth(value: number) {
		this._itemWithBoxWidth = value;
	}

    /**
     * Setter itemWithBoxHeight
     * @param {number} value
     */
	public set itemWithBoxHeight(value: number) {
		this._itemWithBoxHeight = value;
	}

    /**
     * Setter itemWithBoxDimUnitMaster
     * @param {number} value
     */
	public set itemWithBoxDimUnitMaster(value: number) {
		this._itemWithBoxDimUnitMaster = value;
	}

    /**
     * Setter itemWithBoxWeight
     * @param {number} value
     */
	public set itemWithBoxWeight(value: number) {
		this._itemWithBoxWeight = value;
	}

    /**
     * Setter poColorId
     * @param {number[]} value
     */
	public set poColorId(value: number[]) {
		this._poColorId = value;
	}

    /**
     * Setter images
     * @param {any[]} value
     */
	public set images(value: any[]) {
		this._images = value;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}

    /**
     * Setter unitDropdown
     * @param {any[]} value
     */
	public set unitDropdown(value: any[]) {
		this._unitDropdown = value;
	}

    /**
     * Setter cartownWdropdown
     * @param {any[]} value
     */
	public set cartownWdropdown(value: any[]) {
		this._cartownWdropdown = value;
	}

    /**
     * Setter colorDropdown
     * @param {any[]} value
     */
	public set colorDropdown(value: any[]) {
		this._colorDropdown = value;
	}

    /**
     * Setter hsnCode
     * @param {string} value
     */
	public set hsnCode(value: string) {
		this._hsnCode = value;
	}

    /**
     * Setter imageIndex
     * @param {number} value
     */
	public set imageIndex(value: number) {
		this._imageIndex = value;
	}

    /**
     * Setter itemColors
     * @param {any} value
     */
	public set itemColors(value: any) {
		this._itemColors = value;
	}

    /**
     * Setter itemDocList
     * @param {any[]} value
     */
	public set itemDocList(value: any[]) {
		this._itemDocList = value;
	}

    /**
     * Setter itemSKUId
     * @param {any} value
     */
	public set itemSKUId(value: any) {
		this._itemSKUId = value;
	}

    /**
     * Setter itemName
     * @param {any} value
     */
	public set itemName(value: any) {
		this._itemName = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter marketType
     * @param {string} value
     */
	public set marketType(value: string) {
		this._marketType = value;
	}

    /**
     * Setter cartonPrice
     * @param {string} value
     */
	public set cartonPrice(value: string) {
		this._cartonPrice = value;
	}

    /**
     * Setter itemPrice
     * @param {string} value
     */
	public set itemPrice(value: string) {
		this._itemPrice = value;
	}

    /**
     * Setter cartonWeightDimId
     * @param {number} value
     */
	public set cartonWeightDimId(value: number) {
		this._cartonWeightDimId = value;
	}

    /**
     * Setter itemFileFormatedName
     * @param {string} value
     */
	public set itemFileFormatedName(value: string) {
		this._itemFileFormatedName = value;
	}

    /**
     * Setter breachAlert
     * @param {any} value
     */
	public set breachAlert(value: any) {
		this._breachAlert = value;
	}

    /**
     * Setter displayName
     * @param {any} value
     */
	public set displayName(value: any) {
		this._displayName = value;
	}

    /**
     * Setter skuId
     * @param {any} value
     */
	public set skuId(value: any) {
		this._skuId = value;
	}

    /**
     * Setter formattedName
     * @param {any} value
     */
	public set formattedName(value: any) {
		this._formattedName = value;
	}

    /**
     * Setter color
     * @param {any} value
     */
	public set color(value: any) {
		this._color = value;
	}

    /**
     * Setter averagePriceWithGST
     * @param {number} value
     */
	public set averagePriceWithGST(value: number) {
		this._averagePriceWithGST = value;
	}

    /**
     * Setter averagePriceWithoutGST
     * @param {number} value
     */
	public set averagePriceWithoutGST(value: number) {
		this._averagePriceWithoutGST = value;
	}

    /**
     * Setter packingTypes
     * @param {any[]} value
     */
	public set packingTypes(value: any[]) {
		this._packingTypes = value;
	}

    /**
     * Setter itemDimArr
     * @param {any[]} value
     */
	public set itemDimArr(value: any[]) {
		this._itemDimArr = value;
	}

    /**
     * Setter itemBoxDimArr
     * @param {any[]} value
     */
	public set itemBoxDimArr(value: any[]) {
		this._itemBoxDimArr = value;
	}

    /**
     * Setter supplierDropdown
     * @param {any[]} value
     */
	public set supplierDropdown(value: any[]) {
		this._supplierDropdown = value;
	}
    
}
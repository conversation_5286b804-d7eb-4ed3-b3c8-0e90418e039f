<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_PO, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4 *ngIf="!poImportId">{{'Add New'}} Purchase Order</h4>
            <h4 *ngIf="poImportId">Update Purchase Order</h4>
        </div>
        <div class="page-title-right">
            <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/purchases/po-import']"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </Button>
        </div>
    </div>
    <div class="content-area">
        <div class="card card-theme card-forms" [formGroup]="poImportForm">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div *ngIf="poImportObj?.purchaseOrder" class="form-group form-group-inline-control required">
                            <label class="form-label">Purchase Order#</label>
                            <div class="form-control-wrapper">
                                <input type="text" class="form-control" placeholder="Purchase Order#"
                                    [(ngModel)]="poImportObj.purchaseOrder" disabled
                                    [ngModelOptions]="{standalone: true}">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Order For</label>
                            <div class="form-control-wrapper">
                                <div class="radio radio-primary form-check-inline"
                                    *ngFor="let item of dropdown?.orderType; index as i">
                                    <input (change)="onChangeOrderFor()" type="radio" [id]="'regular-' + i"
                                        [attr.disabled]="true" [value]="item.value" [(ngModel)]="poImportObj.orderType"
                                        formControlName="orderType" />
                                    <label [for]="'regular-' + i">{{item.label}}</label>
                                </div>
                                <div class="mt-1" *ngIf="poImportObj?.orderType == enumForOrderFor.Customer">
                                    <ng-select placeholder="Select Customer" [multiple]="false" [clearable]="false"
                                        formControlName="customerId" [items]="dropdown?.customerDropdown"
                                        bindLabel="displayName" bindValue="id" [(ngModel)]="poImportObj.customerId">
                                    </ng-select>
                                    <div class="message error-message"
                                        *ngIf="poImportForm.controls['customerId'].hasError('required') &&  poImportForm.controls['customerId'].touched">
                                        {{utilsService.validationService.CUS_REQ}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Supplier</label>
                            <div class="form-control-wrapper">
                                <ng-select class="custom-ng-select" (open)="captureOldValue()" (change)="onSelectSupplier()"
                                    placeholder="Select Supplier" [multiple]="false" [clearable]="false"
                                    formControlName="supplierId" [items]="dropdown?.supplierDropdown"
                                    bindLabel="displayName" bindValue="id" [(ngModel)]="poImportObj.supplierId">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['supplierId'].hasError('required') &&  poImportForm.controls['supplierId'].touched">
                                    {{utilsService.validationService.SUPPLIER_REQ}}
                                </div>
                            </div>
                        </div>
                        @if(poImportObj?.supplierId && (poImportObj.debitAmount > 0 || poImportObj.creditAmount > 0)) {
                            <div class="form-group form-group-inline-control">
                                <label class="form-label"></label>
                                <div class="form-control-wrapper">
                                    @if(poImportObj.debitAmount > 0) {
                                        <label class="text-danger">Debit Amt : RMB {{(poImportObj.debitAmount || 0.0) | indianCurrency}}</label>
                                    }
                                    @if(poImportObj.creditAmount > 0) {
                                        <label class="text-success">Credit Amt : RMB {{(poImportObj.creditAmount || 0.0) | indianCurrency}}</label>
                                    }
                                </div>
                            </div>
                        }
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">Mobile No</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false"
                                        [items]="dropdown?.Country" bindLabel="label" bindValue="value"
                                        [(ngModel)]="poImportObj.mobileExtensionId" formControlName="mobileExtension">
                                    </ng-select>
                                    <input [(ngModel)]="poImportObj.mobileNo" formControlName="mobileNo" type="text"
                                        class="form-control" placeholder="Enter Mobile No">
                                </div>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['mobileExtension'].hasError('required') && poImportForm.controls['mobileExtension'].touched">
                                    {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['mobileNo'].hasError('required') && poImportForm.controls['mobileNo'].touched && poImportForm.controls['mobileExtension'].valid">
                                    {{utilsService.validationService.MOBILE_NUMBER_REQUIRED}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!poImportForm.controls['mobileNo'].hasError('required') && !poImportForm.controls['mobileNo'].valid && poImportForm.controls['mobileNo'].touched && poImportForm.controls['mobileExtension'].valid">
                                    {{utilsService.validationService.MOBILE_NUMBER_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Delivery Address</label>
                            <div class="form-control-wrapper">
                                <ng-select class="custom-ng-select" placeholder="Select Warehouse" [multiple]="false" [clearable]="false"
                                    [items]="dropdown?.warehouse" bindLabel="warehouseName" bindValue="id"
                                    [(ngModel)]="poImportObj.deliveryWarehouseId" formControlName="deliveryWarehouseId">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['deliveryWarehouseId'].hasError('required') &&  poImportForm.controls['deliveryWarehouseId'].touched">
                                    {{utilsService.validationService.DA_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">Bank Group</label>
                            <div class="form-control-wrapper">
                                <ng-select class="custom-ng-select" placeholder="Select Bank Group" [multiple]="false" [clearable]="true"
                                    [items]="dropdown?.bankGroup" bindLabel="bankName" bindValue="id"
                                    [(ngModel)]="poImportObj.bankGroupId" formControlName="bankGroupId">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['bankGroupId'].hasError('required') &&  poImportForm.controls['bankGroupId'].touched">
                                    {{utilsService.validationService.BANK_GROUP_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Conversion Rate Rs <br />/RMB</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <span class="input-group-text">Rs.</span>
                                    <input [(ngModel)]="poImportObj.conversationRate" formControlName="conversationRate"
                                        type="text" class="form-control" placeholder="Enter Rate" mask="separator.5"
                                        thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15">
                                </div>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['conversationRate'].hasError('required') &&  poImportForm.controls['conversationRate'].touched">
                                    {{utilsService.validationService.CONV_RATE_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!poImportForm.controls['conversationRate'].hasError('required') && !poImportForm.controls['conversationRate'].valid && poImportForm.controls['conversationRate'].touched">
                                    {{utilsService.validationService.CONV_RATE_NOT_ALLOW_ZERO}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="form-group form-group-inline-control required">
                            <label class="form-label">PO Date</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-icon-end">
                                    <i (click)="e.toggle()" class="th th-outline-calendar-1"></i>
                                    <input (click)="e.toggle()" (keydown.space)="e.toggle()" [maxDate]="maxDateNGB"
                                        [(ngModel)]="poImportObj.temp_date" formControlName="poImportDate" readonly
                                        type="text" class="form-control" placeholder="dd/mm/yyyy" ngbDatepicker
                                        #e="ngbDatepicker">
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">PO Limit (RMB)</label>
                            <div class="form-control-wrapper">
                                <input [(ngModel)]="poImportObj.poLimit" formControlName="poLimit" type="text"
                                    class="form-control" placeholder="Enter PO Limit" mask="separator.4"
                                    thousandSeparator="" [maxlength]="utilsService.validationService.MAX_10">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control "
                            *ngIf="poImportObj?.orderType == enumForOrderFor.Customer">
                            <label class="form-label">Advance Pay by Cus.</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <span class="input-group-text">Rs.</span>
                                    <!-- <ng-select class="" placeholder="Curr." [multiple]="false" [clearable]="false"
                                        [items]="dropdown?.currencyAdvCus" bindLabel="label" bindValue="value"
                                        [(ngModel)]="poImportObj.advancePayByCustomerCurrencyId"
                                        formControlName="advancePayByCustomerCurrencyId">
                                    </ng-select> -->
                                    <input [(ngModel)]="poImportObj.advancePayByCustomer" mask="separator.2"
                                        thousandSeparator="" formControlName="advancePayByCustomer" type="text"
                                        class="form-control" placeholder="Enter Amt">
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Payment Terms</label>
                            <div class="form-control-wrapper">
                                <ng-select class="custom-ng-select" (clear)="onClearPaymentTerms()" placeholder="Select Payment Terms"
                                    [multiple]="false" [clearable]="true" [items]="dropdown?.paymentTerms"
                                    bindLabel="label" bindValue="value" [(ngModel)]="poImportObj.paymentTermsId"
                                    formControlName="paymentTermsId">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="poImportForm.controls['paymentTermsId'].hasError('required') &&  poImportForm.controls['paymentTermsId'].touched">
                                    {{utilsService.validationService.PAYMENT_TERMS_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">Shipment Type</label>
                            <div class="form-control-wrapper">
                                <ng-select (change)="onChangeShipmentType()" placeholder="Select Shipment Type"
                                    [multiple]="false" [clearable]="true" [items]="dropdown?.shippingType"
                                    bindLabel="label" bindValue="value" [(ngModel)]="poImportObj.shippingTypes"
                                    formControlName="shippingTypes">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">CBM Price</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <span class="input-group-text">Rs.</span>
                                    <input [(ngModel)]="poImportObj.cbmPrice" formControlName="cbmPrice" type="text"
                                        class="form-control" placeholder="Enter Amt" mask="separator.5"
                                        thousandSeparator="" [maxlength]="utilsService.validationService.MAX_20">
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control">
                            <label class="form-label">Tags</label>
                            <div class="form-control-wrapper form-group-tag">
                                <ng-select [inputAttrs]="{maxlength: '100'}" placeholder="Enter Tags"
                                    formControlName="tags" [(ngModel)]="poImportObj.temp_tags" [items]="[]"
                                    [addTag]="addTagLimit" [multiple]="true" [selectOnTab]="true" [isOpen]="false"
                                    [clearable]="true">
                                    <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                        <span class="ng-value-label" [title]="item">{{ item }}</span>
                                        <span class="ng-value-icon right" (click)="clear(item)">×</span>
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control">
                            <label class="form-label">Order Note</label>
                            <div class="form-control-wrapper">
                                <textarea [maxlength]="utilsService.validationService.MAX_500" formControlName="notes"
                                    [(ngModel)]="poImportObj.notes" class="form-control"
                                    placeholder="Enter Order Note"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 col-sm-12" (dragover)="onSelectAttachments($event);doc.value = ''"
                        (drop)="onSelectAttachments($event);doc.value = ''" (paste)="onSelectAttachments($event)">
                        <div class="d-flex flex-column h-100">
                            <div class="attachments-wrapper">
                                <div class="form-group">
                                    <div class="form-label">Upload Files<i class="th th-outline-info-circle ms-1"
                                            [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom"
                                            container="body" triggers="hover"></i>
                                    </div>
                                </div>
                                <div class='attachments-container h-100'>
                                    <div class='attachments-content'>
                                        <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                        <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                                    </div>
                                    <input #doc type="file" ref={imageRef} multiple
                                        accept=".xls,.xlsx,.xlss,.csv,image/*,.pdf"
                                        (change)="onSelectAttachments($event);doc.value = ''" />
                                </div>
                                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                    <div class='attachments-upload-row'>
                                        <div class='attachments-upload-col'
                                            *ngFor="let item of poImportObj.poImportDocList; index as i">
                                            <div class='card-attachments-upload'>
                                                <div class='attachments-image'>
                                                    <ng-container *ngIf="utilsService.isImage(item.originalName)">
                                                        <img (click)="openLink(item.formattedName, null)" loading="lazy"
                                                            *ngIf="!item.file"
                                                            [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null"
                                                            alt="valamji" />
                                                        <img (click)="openLink(null, item.formattedName)" loading="lazy"
                                                            *ngIf="item.file"
                                                            [src]="item.formattedName ? (item.formattedName) : null"
                                                            alt="valamji" />
                                                    </ng-container>
                                                    <ng-container *ngIf="utilsService.isMedia(item.originalName)">
                                                        <img *ngIf="item.file"
                                                            (click)="openLink(null, item.formattedName)"
                                                            src="assets/images/files/file-video.svg" alt="valamji" />
                                                        <img *ngIf="!item.file"
                                                            (click)="openLink(item.formattedName, null)"
                                                            src="assets/images/files/file-video.svg" alt="valamji" />
                                                    </ng-container>
                                                    <ng-container *ngIf="utilsService.isExcel(item.originalName)">
                                                        <img *ngIf="item.file"
                                                            (click)="openLink(null, item.formattedName)"
                                                            src="assets/images/files/file-excel.svg" alt="valamji" />
                                                        <img *ngIf="!item.file"
                                                            (click)="openLink(item.formattedName, null)"
                                                            src="assets/images/files/file-excel.svg" alt="valamji" />
                                                    </ng-container>
                                                    <ng-container *ngIf="utilsService.isDocument(item.originalName)">
                                                        <img *ngIf="item.file"
                                                            (click)="openLink(null, item.formattedName)"
                                                            src="assets/images/files/file-pdf.svg" alt="valamji" />
                                                        <img *ngIf="!item.file"
                                                            (click)="openLink(item.formattedName, null)"
                                                            src="assets/images/files/file-pdf.svg" alt="valamji" />
                                                    </ng-container>
                                                </div>
                                                <div class="attachments-text"
                                                    [ngbTooltip]="item.fileName ? item.fileName : item.originalName"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <h6 class="file-name">{{item.fileName ? item.fileName :
                                                        item.originalName}}</h6>
                                                </div>
                                                <button (click)="removeAttachment(i, item)" class="btn-close"
                                                    variant="close"><i class='th th-close'></i></button>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-forms-total mt-4">
                                <table class="table table-theme table-hover">
                                    <tbody>
                                        <tr>
                                            <td>Sub Total</td>
                                            <th class="text-black">₹ {{poImportObj.subTotalRupees ?
                                                (poImportObj.subTotalRupees | indianCurrency) : 0}}</th>
                                            <th class="text-black">RMB {{poImportObj.subTotalRmb ?
                                                (poImportObj.subTotalRmb | indianCurrency) : 0}}</th>
                                        </tr>
                                        <tr>
                                            <td>GST Amount</td>
                                            <th class="text-black">₹ {{poImportObj.gstAmountRupees ?
                                                (poImportObj.gstAmountRupees | indianCurrency) : 0}}</th>
                                            <th class="text-black">RMB {{poImportObj.gstAmountRmb ?
                                                (poImportObj.gstAmountRmb | indianCurrency) : 0}}</th>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td>Grand Total</td>
                                            <th class="text-black fs-14">₹ {{poImportObj.grandTotalRupees ?
                                                (poImportObj.grandTotalRupees | indianCurrency) : 0}}</th>
                                            <th class="text-black">RMB {{poImportObj.grandTotalRmb ?
                                                (poImportObj.grandTotalRmb | indianCurrency) : 0}}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <hr />
                    </div>
                </div>
                <div class="row" *ngIf="poImportObj.supplierId">
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <div class="inner-title-wrapper">
                            <div class="inner-title-left">
                                <div class="inner-title-text">
                                    <h6 class="">Add Items*</h6>
                                </div>
                            </div>
                            <div class="inner-title-rigth">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <div class="table-responsive mb-3">
                            <table
                                class="table-theme table-hover table table-bordered  table-sticky table-hierarchy-two"
                                formArrayName="items">
                                <thead class="border-less">
                                    <tr class="tbl-bg-light-three">
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <!-- <th>-</th> -->
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <!-- <th>-</th>
                                        <th>-</th>
                                       -->
                                        <th>-</th>
                                        <th>A</th>
                                        <!-- <th>-</th>
                                        <th>-</th> -->
                                        <th>B</th>
                                        <th>C = A*B</th>
                                        <!-- <th>-</th>
                                        <th>-</th> -->
                                        <th>D</th>
                                        <th>E=C*D</th>
                                        <th>F</th>
                                        <th>G = E+F</th>
                                        <th>-</th>
                                        <th>H = G*Conv</th>
                                        <th>I = H/C</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.CBM">
                                            <th class="tbl-bg-secondary">J1</th>
                                            <th class="tbl-bg-secondary">K1=A*J1</th>
                                            <th class="tbl-bg-secondary">L1</th>
                                            <th class="tbl-bg-secondary">M1=K1*L1</th>
                                            <th class="tbl-bg-secondary">N1=M1/C</th>
                                        </ng-container>
                                        <th>O</th>
                                        <th>P=O*A</th>
                                        <th>Q=P/C</th>
                                        <th>-</th>
                                        <th>S=R/C</th>
                                        <th>T=H*30/100*0.18/C</th>
                                        <th>U</th>
                                        <th>V= U/C</th>
                                        <th>W=Q+S+T+V</th>
                                        <th>
                                            <div [ngSwitch]="poImportObj.shippingTypes">
                                                <div *ngSwitchCase="enumForShippingType.CBM">
                                                    <span>X = {{ "I + N1 + W" }}</span>
                                                </div>
                                                <div *ngSwitchCase="enumForShippingType.WEIGHT">
                                                    <span>X = {{ "I + N2 + W" }}</span>
                                                </div>
                                                <div *ngSwitchCase="enumForShippingType.PERCENTAGE">
                                                    <span>X = {{ "I + K3 + W" }}</span>
                                                </div>
                                                <div *ngSwitchCase="enumForShippingType.PIECE">
                                                    <span>X = {{ "I + J5 + W" }}</span>
                                                </div>
                                                <div *ngSwitchCase="enumForShippingType.DONE">
                                                    <span>X = {{ "I + W" }}</span>
                                                </div>
                                            </div>
                                        </th>
                                        <th>J2</th>
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.WEIGHT">
                                            <th class="tbl-bg-secondary-two">K2=J2*A</th>
                                            <th class="tbl-bg-secondary-two">L2</th>
                                            <th class="tbl-bg-secondary-two">M2=K2*L2</th>
                                            <th class="tbl-bg-secondary-two">N2=M2/C</th>
                                            <th class="tbl-bg-secondary-two">O2=I+W+N2</th>
                                        </ng-container>
                                        <ng-container
                                            *ngIf="poImportObj.shippingTypes == enumForShippingType.PERCENTAGE">
                                            <th class="tbl-bg-success">J3</th>
                                            <th class="tbl-bg-success">K3=I*J3/100</th>
                                            <th class="tbl-bg-success">L3 = I+W+K3</th>
                                        </ng-container>

                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.DONE">
                                            <th class="tbl-bg-danger">J4=I+W</th>
                                            <!-- <th class="tbl-bg-danger">K4=Wrong!</th> -->
                                        </ng-container>

                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.PIECE">
                                            <th class="tbl-bg-primary">J5</th>
                                            <th class="tbl-bg-primary">K5=I+W+J5</th>
                                        </ng-container>

                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <th>-</th>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <!-- <td>-</td> -->
                                        <td>-</td>
                                        <td>-</td>
                                        <td>-</td>
                                        <!-- <td>-</td> -->
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <th>Item Details</th>
                                        <th>Item Group</th>
                                        <th>Purchase <br />Breach Qty</th>
                                        <th>Season Date</th>
                                        <th>Advance PO <br /> Date</th>
                                        <th>Average Price <br />(Surat)</th>
                                        <th>Average Price Surat<br />(With GST)</th>
                                        <th>Sale Price</th>
                                        <!-- <th>Marka</th> -->
                                        <th>Color</th>
                                        <th>Note</th>
                                        <th>English <br />Comment</th>
                                        <th>China <br />Comment</th>
                                        <!-- <th>Payment <br />Status</th>
                                        <th>Item Status</th>
                                        <th>Order Date</th> -->
                                        <th>Expected <br />Delivery Date</th>
                                        <th>PO Carton</th>
                                        <!-- <th>Received <br />Cartons [CH]</th>
                                        <th>Receive Id <br />[CH]</th> -->
                                        <th>PCS/Carton</th>
                                        <th>Total PCS Qty</th>
                                        <!-- <th>Pending Qty <br />(Carton)</th>
                                        <th>Total Pending <br />Qty</th> -->
                                        <th>Price /PCS <br />(RMB)</th>
                                        <th>Total Amount <br />(RMB)</th>
                                        <th>Exp. Delivery <br />Cost (RMB)</th>
                                        <th>Total Amount <br />(RMB) + (Ship. Exp.)</th>
                                        <th>Conv. Rate <br /> (RBM {{'-->'}} INR)</th>
                                        <th>Total Amount <br />(INR)</th>
                                        <th>China Final <br />Expected Cost<br /> (Padatar)/Per Piece </th>
                                        <th>Shipping Type</th>
                                        <th>Carton Dimension</th>
                                        <th>Dimensions <br />Age</th>
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.CBM">
                                            <th class="tbl-bg-secondary">CBM/Carton</th>
                                            <th class="tbl-bg-secondary">Total CBM</th>
                                            <th class="tbl-bg-secondary">CBM Price</th>
                                            <th class="tbl-bg-secondary">Total CBM <br />Expense (₹)
                                            </th>
                                            <th class="tbl-bg-secondary">Shipping <br />Expense/PCS
                                            </th>
                                        </ng-container>
                                        <th>Transportation <br />charges <br />[Mumbai to
                                            Surat]/Carton</th>
                                        <th>Total <br />Transportation <br />charges
                                            <br />[Mumbai to Surat]
                                        </th>
                                        <th>Transportation <br />charges <br />[Mumbai to Surat]
                                            / PCS</th>
                                        <th>Total <br />Insurance (₹)</th>
                                        <th>Insurance <br />(₹)/PCS</th>
                                        <th>GST <br />Amount/PCS</th>
                                        <th>Crane <br />Expense [Total]</th>
                                        <th>Crane <br />Expense/PCS</th>
                                        <th>Total Expense</th>
                                        <th>China <br />To Surat <br />Final <br />Price
                                            <br />(Padatar)
                                        </th>
                                        <th> Weight<br />/Carton
                                        </th>
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.WEIGHT">
                                            <th class="tbl-bg-secondary-two"> Total Weight </th>
                                            <th class="tbl-bg-secondary-two"> Cost / <br />kg (RS)
                                            </th>
                                            <th class="tbl-bg-secondary-two"> Total Load. <br />AMT
                                                [Weight] (₹) </th>
                                            <th class="tbl-bg-secondary-two"> Shipping <br />Cost /
                                                Piece (₹) </th>
                                            <th class="tbl-bg-secondary-two"> Total Shipping <br />
                                                Expense / Weight </th>
                                        </ng-container>
                                        <ng-container
                                            *ngIf="poImportObj.shippingTypes == enumForShippingType.PERCENTAGE">
                                            <th class="tbl-bg-success"> Percentage -<br /> [%]</th>
                                            <th class="tbl-bg-success"> Total Expense <br />/ Piece
                                                (₹) [%]</th>
                                            <th class="tbl-bg-success"> Total Final<br /> Cost /
                                                <br />Piece(₹) [%]
                                            </th>
                                        </ng-container>

                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.DONE">
                                            <th class="tbl-bg-danger"> Item Amount <br />(₹) [Fixed]
                                            </th>
                                            <!-- <th class="tbl-bg-danger"> Total Item <br />Amount
                                                (₹)<br /> [Fixed]</th> -->
                                        </ng-container>

                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.PIECE">
                                            <th class="tbl-bg-primary">Expense/PCS </th>
                                            <th class="tbl-bg-primary"> Total Item <br />Amount (₹)
                                            </th>
                                        </ng-container>
                                        <th>Last 3 <br />Purchase <br />[Shop No, Price,<br />
                                            Cartons]</th>
                                        <th>Inquiry Shop<br /> with price</th>
                                        <th>Inquiry <br />(Cus Count) - <br />[Inquiry
                                            <br />(Total
                                            inq. Count)]
                                        </th>
                                        <th>QC Required</th>
                                        <th>Item Real <br/> Photo</th>
                                        <!-- <th>Container No</th>
                                        <th>Real Carton<br /> LxWxH cm</th>
                                        <th>Real CBM/<br /> Carton</th>
                                        <th>Total Real<br /> CBM</th>
                                        <th>Real Weight/<br /> Carton (kg)</th>
                                        <th>GST Invoice <br />Status</th>
                                        <th>Photo/Video<br /> Upload</th> -->
                                        <!-- <th>QC Checklist</th> -->
                                        <!-- <th>CHA - <br />Importer</th>
                                        <th>Received ID</th> -->
                                        <th>GST TAX %</th>
                                        <th>GST TAX <br />Amount</th>
                                        <th>Extra Expense</th>
                                        <th>Purchase <br />Ratio</th>
                                        <th>Item Dimension</th>
                                        <th>Item Weight</th>
                                        <th>Item Dimension <br /> (With Box)</th>
                                        <th>Item Weight <br /> (With Box)</th>
                                        <!-- <th>CBM</th> -->
                                        <th>Enter Tag</th>
                                        <th>Packing Type</th>
                                        <th>Measurement Code</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="tbl-add-row tbl-bg-white"
                                        *ngFor="let contact of items.controls; index as i" [formGroupName]="i"
                                        [ngClass]="{
                                            'tbl-bg-secondary-two': associatedItems[i].isDraft,
                                            'tbl-bg-secondary tr-hierarchy-collapse': associatedItems[i].isLink,
                                            'tbl-bg-danger': (associatedItems[i].chinaToSuratPadtar > 0 && associatedItems[i].chinaToSuratPadtar > associatedItems[i].itemPrice)
                                        }">
                                        <td class="tbl-user tbl-form-group">
                                            <div class="tbl-user-checkbox-srno">
                                                <span>{{(i + 1) | padNum}}.</span>
                                                <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100"
                                                    *ngIf="!associatedItems[i].id">
                                                    <div
                                                        class="d-flex align-items-center gap-2 justify-content-space-between">
                                                        <ng-select id="itemId-{{i}}" (clear)="onClearItem(i)" (open)="handleFocus()" class="po-import-ng"
                                                            (change)="setValueToAssociateItem(associatedItems[i], i)" appendTo=".theme-ngselect"
                                                            [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}" formControlName="itemId"
                                                            [(ngModel)]="associatedItems[i].associatedIds" [items]="associatedItems[i].itemDropdown" bindLabel="name"
                                                            placeholder="Select Item" [searchFn]="customSearchFn" bindValue="associatedIds">
                                                            <ng-template ng-header-tmp>
                                                                <div class="d-flex fw-bold bg-light border-bottom py-2 px-3">
                                                                    <div class="w-50 fs-14">Item</div>
                                                                    <div class="w-25 fs-14">Item Group</div>
                                                                    <div class="w-25 fs-14">Tag</div>
                                                                    <div class="w-25 fs-14">Qty/Carton</div>
                                                                </div>
                                                            </ng-template>
                                                            <ng-template ng-label-tmp let-item="item">
                                                                <div class="tbl-user">
                                                                    <div class="tbl-user-checkbox-srno">
                                                                        <div class="tbl-user-wrapper">
                                                                            <div class="tbl-user-image">
                                                                                <img loading="lazy"
                                                                                    [src]="item.itemFileFormatedName ? (utilsService.imgPath + item.itemFileFormatedName) : ''"
                                                                                    alt="valamji">
                                                                            </div>
                                                                            <div class="tbl-user-text-action">
                                                                                <div class="tbl-user-text">
                                                                                    <p>{{ item.displayName }} </p>
                                                                                    <span>{{item.itemSKUId}}</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-template>
                                                            <ng-template ng-option-tmp let-item="item">
                                                                <div class="d-flex align-items-center border-bottom py-2 px-3">
                                                                    <div class="w-50 fs-12">
                                                                        <div class="tbl-user">
                                                                            <div class="tbl-user-checkbox-srno">
                                                                                <div class="tbl-user-wrapper ">
                                                                                    <div class="tbl-user-image">
                                                                                        <img loading="lazy"
                                                                                            [src]="item.itemFileFormatedName ? (utilsService.imgPath + item.itemFileFormatedName) : ''"
                                                                                            alt="valamji">
                                                                                    </div>
                                                                                    <div class="tbl-user-text-action">
                                                                                        <div class="tbl-user-text po-description2">
                                                                                            <div [title]="item.displayName">{{ item.displayName }} </div>
                                                                                            <span>{{ item.itemSKUId }}</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="w-25 po-description fs-12">{{item.itemGroupName}}</div>
                                                                    <div class="w-25 po-description fs-12">{{item.tag}}</div>
                                                                    <div class="w-25 po-description fs-12">{{item.cartonQuantity}}</div>
                                                                </div>
                                                            </ng-template>
                                                            <ng-template ng-notfound-tmp>
                                                                <div class="d-flex align-items-center border-bottom py-2 px-3 justify-content-center fs-12">
                                                                    <div>No Item Found</div>
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                        <div class="dropdown">
                                                            <button class="btn btn-xs btn-light-white btn-icon"
                                                                id="actionDropDown" data-bs-toggle="dropdown"
                                                                aria-expanded="false"
                                                                data-bs-popper-config='{"strategy":"fixed"}'>
                                                                <i class="th th-outline-more"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                <li *ngIf="associatedItems[i].itemId">
                                                                    <a (click)="openImagePreviewModal(associatedItems[i])"
                                                                        class="dropdown-item"><i
                                                                            class="th th-outline-image"></i>View Images/Videos</a>
                                                                </li>
                                                                <li
                                                                    *ngIf="(!associatedItems[i].isLink && associatedItems[i].poCarton < 1)">
                                                                    <a class="dropdown-item"
                                                                        (click)="openLinkItemModal(associatedItems[i])">
                                                                        <i class="th th-outline-link-2"></i>
                                                                        Link Item ({{associatedItems[i].poCarton}})</a>
                                                                </li>
                                                                <li *ngIf="associatedItems[i].isLink"><a
                                                                        class="dropdown-item" (click)="onRemoveLink(i)">
                                                                        <i class="th th-outline-close-circle"></i>
                                                                        Remove From Link</a></li>
                                                                <li>
                                                                <li>
                                                                    <a (click)="openRemoveAIModal(i, associatedItems[i])"
                                                                        class="dropdown-item text-danger"><i
                                                                            class="th th-outline-trash"></i>Delete</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100"
                                                    *ngIf="associatedItems[i].id">
                                                    <div class="tbl-user-checkbox-srno new-ng-width">
                                                        <div
                                                            class="tbl-user-wrapper new-ng-width justify-content-space-between">
                                                            <div
                                                                *ngIf="!utilsService.isEmptyObjectOrNullUndefined(associatedItems[i]?.item?.formattedName); else fallbackImage">
                                                                <div class="tbl-user-image"
                                                                    (click)="openImagePreviewModal(associatedItems[i])">
                                                                    <img [src]="utilsService.imgPath + associatedItems[i]?.item?.formattedName"
                                                                        alt="valamji">
                                                                </div>
                                                            </div>
                                                            <ng-template #fallbackImage>
                                                                <div class="tbl-user-image">
                                                                    {{associatedItems[i].item?.displayName?.charAt(0).toUpperCase()}}
                                                                </div>
                                                            </ng-template>
                                                            <div class="tbl-user-text-action">
                                                                <div class="tbl-user-text">
                                                                    <p>{{ associatedItems[i]?.item?.displayName }} </p>
                                                                    <span>{{associatedItems[i]?.item?.skuId}}</span>
                                                                </div>
                                                            </div>
                                                            <div class="dropdown">
                                                                <button class="btn btn-xs btn-light-white btn-icon"
                                                                    id="actionDropDown" data-bs-toggle="dropdown"
                                                                    aria-expanded="false"
                                                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                                                    <i class="th th-outline-more"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="actionDropDown">
                                                                    <li>
                                                                        <a (click)="openImagePreviewModal(associatedItems[i])"
                                                                            class="dropdown-item"><i
                                                                                class="th th-outline-image"></i>View Images/Videos</a>
                                                                    </li>
                                                                    <li>
                                                                        <a (click)="utilsService.openItemDetailsInNewTab(associatedItems[i].itemId)" class="dropdown-item">
                                                                            <i class="th th-outline-eye"></i>
                                                                            View Item Details
                                                                        </a>
                                                                    </li>
                                                                    <li
                                                                        *ngIf="(!associatedItems[i].isLink && associatedItems[i].poCarton < 1)">
                                                                        <a class="dropdown-item"
                                                                            (click)="openLinkItemModal(associatedItems[i])">
                                                                            <i class="th th-outline-link-2"></i>
                                                                            Link Item
                                                                            ({{associatedItems[i].poCarton}})</a>
                                                                    </li>
                                                                    <li *ngIf="associatedItems[i].isLink"><a
                                                                            class="dropdown-item"
                                                                            (click)="onRemoveLink(i)">
                                                                            <i class="th th-outline-close-circle"></i>
                                                                            Remove From Link</a></li>
                                                                    <li>
                                                                        <ng-container
                                                                            *ngIf="(selectedTab == enumForStatus.PO_CREATED || convertToPo)">
                                                                    <li
                                                                        *ngIf="!associatedItems[i].isLink && !associatedItems[i].isDraft">
                                                                        <a (click)="onMoveToDraft(i)"
                                                                            class="dropdown-item">
                                                                            <i class="th-outline-arrow-left"></i>
                                                                            Move to Draft</a></li>
                                                                    <li>
                                                                    <li
                                                                        *ngIf="!associatedItems[i].isLink && associatedItems[i].isDraft">
                                                                        <a (click)="onRemoveFromDraft(i)"
                                                                            class="dropdown-item">
                                                                            <i class="th-outline-arrow-left"></i>
                                                                            Remove from Draft</a></li>
                                                                    <li>
                                                                        </ng-container>
                                                                    <li *ngIf="items.length > 1">
                                                                        <hr class="dropdown-divider">
                                                                    </li>
                                                                    <li>
                                                                        <a (click)="openRemoveAIModal(i, associatedItems[i])"
                                                                            class="dropdown-item text-danger"><i
                                                                                class="th th-outline-trash"></i>Delete</a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].itemGroupName ? associatedItems[i].itemGroupName :
                                            '-'}}</td>
                                        <td>{{associatedItems[i].levelBreachQtys ?
                                            (associatedItems[i].levelBreachQtys.levelName + ': ' +
                                            (associatedItems[i].levelBreachQtys?.breachQtys ?
                                            associatedItems[i].levelBreachQtys?.breachQtys : '')) : '-'}}</td>
                                        <td>{{associatedItems[i].fromDate ? ((associatedItems[i].fromDate | date:
                                            'dd-MM') + ' to ' + (associatedItems[i].toDate
                                            | date: 'dd-MM')) : '-'}}</td>
                                        <td>{{associatedItems[i].advanceDate ? (associatedItems[i].advanceDate | date:
                                            'dd-MM') : '-'}}</td>
                                        <td>{{associatedItems[i].averagePriceWithoutGST ? associatedItems[i].averagePriceWithoutGST : '-'}}</td>
                                        <td>{{associatedItems[i].averagePriceWithGST ? associatedItems[i].averagePriceWithGST : '-'}}</td>
                                        <td>{{associatedItems[i].itemPrice ? associatedItems[i].itemPrice : '-'}}</td>
                                        <!-- <td>{{associatedItems[i].marka ? associatedItems[i].marka : '-'}}</td> -->
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less theme-ngselect-multi">
                                                <ng-select id="colorId-{{i}}" class="" placeholder="Select Color" [clearable]="false" [closeOnSelect]="true"
                                                    [clearSearchOnAdd]="true"
                                                    [ngClass]="{'required': contact.get('colorId').invalid && contact.get('colorId').touched}"
                                                    [items]="associatedItems[i].associatedItemColors" [multiple]="true" bindLabel="colorName"
                                                    bindValue="poColorId" [(ngModel)]="associatedItems[i].colorId" formControlName="colorId"
                                                    appendTo=".theme-ngselect">
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm form-group-200 eng-china-comment"
                                                [ngClass]="{'form-error': contact.get('note').invalid && contact.get('note').touched}">
                                                <textarea id="note-{{i}}" autosize [minRows]="2" [maxRows]="7"
                                                    [(ngModel)]="associatedItems[i].note"
                                                    [maxlength]="utilsService.validationService.MAX_100"
                                                    formControlName="note" type="text" class="form-control"></textarea>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm form-group-200 eng-china-comment"
                                                [ngClass]="{'form-error': contact.get('englishComment').invalid && contact.get('englishComment').touched}">
                                                <textarea id="englishComment-{{i}}" autosize [minRows]="2" [maxRows]="7"
                                                    [(ngModel)]="associatedItems[i].englishComment"
                                                    [maxlength]="utilsService.validationService.MAX_100"
                                                    formControlName="englishComment" type="text"
                                                    class="form-control"></textarea>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm form-group-200 eng-china-comment"
                                                [ngClass]="{'form-error': contact.get('chinaComment').invalid && contact.get('chinaComment').touched}">
                                                <textarea id="chinaComment-{{i}}" autosize [minRows]="2" [maxRows]="7"
                                                    [(ngModel)]="associatedItems[i].chinaComment"
                                                    [maxlength]="utilsService.validationService.MAX_100"
                                                    formControlName="chinaComment" type="text"
                                                    class="form-control"></textarea>
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].expectedDeliveryDate ?
                                            (associatedItems[i].expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</td>
                                        <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('poCarton').invalid && contact.get('poCarton').touched}">
                                                <input id="poCarton-{{i}}" (input)="onCalPOCartonPCSCartonC(i)"
                                                    [(ngModel)]="associatedItems[i].poCarton"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="poCarton" type="text" class="form-control"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        <!-- <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('pricePerCarton').invalid && contact.get('pricePerCarton').touched}">
                                                <input (input)="onCalPOCartonPCSCartonC(i)"
                                                    [(ngModel)]="associatedItems[i].pricePerCarton"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="pricePerCarton" type="text" class="form-control"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td> -->
                                        <td>{{associatedItems[i].pricePerCarton ? associatedItems[i].pricePerCarton : '-'}}</td>
                                        <td>{{associatedItems[i].totalPcsQty ? associatedItems[i].totalPcsQty : '-'}}</td>
                                        <td>
                                            @let averagePriceCondition = (
                                                associatedItems[i].pricePerItem > 0 &&
                                                associatedItems[i].averagePriceWithGST > 0 &&
                                                associatedItems[i].pricePerItem > associatedItems[i].averagePriceWithGST
                                            );
                                            <div class="form-group form-group-sm form-group-200"
                                                [ngClass]="{'form-error': contact.get('pricePerItem').invalid && contact.get('pricePerItem').touched,
                                                    'border border-danger rounded': averagePriceCondition}">
                                                <input id="pricePerItem-{{i}}" (input)="onCalE(i)" [(ngModel)]="associatedItems[i].pricePerItem"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="pricePerItem" type="text" class="form-control"
                                                    mask="separator.4" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].totalAmount ? (associatedItems[i].totalAmount |
                                            indianCurrency) : 0}}</td>
                                        <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('expDeliveryCost').invalid && contact.get('expDeliveryCost').touched}">
                                                <input id="expDeliveryCost-{{i}}" (input)="onCalG(i)"
                                                    [(ngModel)]="associatedItems[i].expDeliveryCost"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="expDeliveryCost" type="text" class="form-control"
                                                    mask="separator.4" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].totalAmountWithExp ?
                                            (associatedItems[i].totalAmountWithExp | indianCurrency) : 0}}</td>
                                        <td>{{poImportObj.conversationRate ? poImportObj.conversationRate : '-'}}</td>
                                        <td>{{associatedItems[i].totalAmountWithExpInINR ?
                                            (associatedItems[i].totalAmountWithExpInINR | indianCurrency) : 0}} </td>
                                        <td>{{associatedItems[i].chinaFinalExpextedCode ?
                                            (associatedItems[i].chinaFinalExpextedCode | indianCurrency): 0}} </td>
                                        <td>{{poImportForm.controls['shippingTypes'].value ?
                                            (poImportForm.controls['shippingTypes'].value) : '-'}} </td>
                                        <td>
                                            <div class="form-group form-group-sm">
                                                <div class="form-control-wrapper">
                                                    <div
                                                        class="input-group input-group-sm input-group-select tbl-input-group">
                                                        <input (input)="onCalPOCartonPCSCartonC(i)" id="itemDimLength-{{i}}" [(ngModel)]="associatedItems[i].cartonLength"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemDimLength').invalid && contact.get('itemDimLength').touched}"
                                                            mask="separator.2" thousandSeparator="" formControlName="itemDimLength" type="text" class="form-control">
                                                        <input (input)="onCalPOCartonPCSCartonC(i)" id="itemDimWidth-{{i}}" [(ngModel)]="associatedItems[i].cartonWidth"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemDimWidth').invalid && contact.get('itemDimWidth').touched}"
                                                            mask="separator.2" thousandSeparator="" formControlName="itemDimWidth" type="text" class="form-control">
                                                        <input (input)="onCalPOCartonPCSCartonC(i)" id="itemDimHeight-{{i}}" [(ngModel)]="associatedItems[i].cartonHeight"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemDimHeight').invalid && contact.get('itemDimHeight').touched}"
                                                            mask="separator.2" thousandSeparator="" formControlName="itemDimHeight" type="text" class="form-control">
                                                        <button class="btn btn-sm btn-outline-secondary" type="button"
                                                            id="button-INR">
                                                            {{associatedItems[i].dimUniteName ?
                                                            associatedItems[i].dimUniteName : '-'}}
                                                        </button>
                                                        <!-- <ng-select class="" placeholder="Unit" [multiple]="false" [clearable]="false" [appendTo]="'body'"
                                                                                                    [ngClass]="{'required': contact.get('unitId').invalid && contact.get('unitId').touched}"
                                                                                                    [items]="associatedItems[i].unitDropdown" bindLabel="label" bindValue="value"
                                                                                                    formControlName="unitId" [(ngModel)]="associatedItems[i].unitId">
                                                                                                </ng-select> -->
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].dimAge ? associatedItems[i].dimAge : 0}} </td>
                                        <!-- J Start -->
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.CBM">
                                            <td>{{associatedItems[i].cbmPerCarton ? (associatedItems[i].cbmPerCarton |
                                                indianCurrency) : 0}} </td>
                                            <td>{{associatedItems[i].totalCbm ? (associatedItems[i].totalCbm |
                                                indianCurrency) : 0}} </td>
                                            <td>{{poImportObj.cbmPrice ? (poImportObj.cbmPrice | indianCurrency) : 0}}
                                            </td>
                                            <td>{{associatedItems[i].totalCBMExpenseINR ?
                                                (associatedItems[i].totalCBMExpenseINR | indianCurrency) : 0}} </td>
                                            <td>{{associatedItems[i].shippingExpPerPCS ?
                                                (associatedItems[i].shippingExpPerPCS | indianCurrency) : 0}} </td>
                                        </ng-container>
                                        <!-- J End -->
                                        <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('transportCharges').invalid && contact.get('transportCharges').touched}">
                                                <input id="transportCharges-{{i}}" (input)="onCalP(i)"
                                                    [(ngModel)]="associatedItems[i].transportCharges"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="transportCharges" type="text" class="form-control"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].totalTransportationChargesM2S ?
                                            (associatedItems[i].totalTransportationChargesM2S | indianCurrency) : 0}}
                                        </td>
                                        <td>{{associatedItems[i].transportationChargesM2SperPCS ?
                                            (associatedItems[i].transportationChargesM2SperPCS | indianCurrency) : 0}}
                                        </td>
                                        <td>{{associatedItems[i].totalInsurance ? (associatedItems[i].totalInsurance |
                                            indianCurrency) : 0}} </td>
                                        <td>{{associatedItems[i].insurancePerPcs ? (associatedItems[i].insurancePerPcs |
                                            indianCurrency) : 0}} </td>
                                        <td>{{associatedItems[i].gstAmtPerPcs ? (associatedItems[i].gstAmtPerPcs |
                                            indianCurrency): 0}} </td>
                                        <td>
                                            <div class="form-group form-group-sm form-group-100"
                                                [ngClass]="{'form-error': contact.get('craneExpense').invalid && contact.get('craneExpense').touched}">
                                                <input id="craneExpense-{{i}}" (input)="onCalV(i)" [(ngModel)]="associatedItems[i].craneExpense"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="craneExpense" type="text" class="form-control"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{associatedItems[i].craneExpPcs ? (associatedItems[i].craneExpPcs |
                                            indianCurrency) : 0}} </td>
                                        <td>{{associatedItems[i].totalExp ? (associatedItems[i].totalExp |
                                            indianCurrency) : 0}} </td>
                                        <td>{{associatedItems[i].chinaToSuratPadtar ?
                                            (associatedItems[i].chinaToSuratPadtar | indianCurrency) : 0}} </td>

                                        <td>
                                            <div class="form-group form-group-sm form-group-100"
                                                [ngClass]="{'form-error': contact.get('cartonWeight').invalid && contact.get('cartonWeight').touched}">
                                                <div class="form-control-wrapper">
                                                    <div class="input-group input-group-sm input-group-select tbl-input-group">
                                                        <input id="cartonWeight-{{i}}" (input)="onCalK2(i)" [(ngModel)]="associatedItems[i].cartonWeight"
                                                            [maxlength]="utilsService.validationService.MAX_10" formControlName="cartonWeight" type="text"
                                                            class="form-control" mask="separator.2" thousandSeparator="">
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" id="button-Weight">KG</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.WEIGHT">
                                            <td>{{associatedItems[i].totalWeight ? associatedItems[i].totalWeight : 0}}
                                            </td>
                                            <td>
                                                <div class="form-group form-group-sm form-group-100"
                                                    [ngClass]="{'form-error': contact.get('cartonWeightRu').invalid && contact.get('cartonWeightRu').touched}">
                                                    <input id="cartonWeightRu-{{i}}" (input)="onCalM2(i)"
                                                        [(ngModel)]="associatedItems[i].cartonWeightRu"
                                                        [maxlength]="utilsService.validationService.MAX_10"
                                                        formControlName="cartonWeightRu" type="text"
                                                        class="form-control" mask="separator.2" thousandSeparator="">
                                                </div>
                                            </td>
                                            <td>{{associatedItems[i].totalLoadAmt ? associatedItems[i].totalLoadAmt :
                                                0}}</td>
                                            <td>{{associatedItems[i].shippingCostperPieceINR ?
                                                associatedItems[i].shippingCostperPieceINR : 0}}</td>
                                            <td>{{associatedItems[i].totalShippingExpWeight ?
                                                associatedItems[i].totalShippingExpWeight : 0}}</td>
                                        </ng-container>

                                        <ng-container
                                            *ngIf="poImportObj.shippingTypes == enumForShippingType.PERCENTAGE">
                                            <td>
                                                <div class="form-group form-group-sm form-group-100"
                                                    [ngClass]="{'form-error': contact.get('percentage').invalid && contact.get('percentage').touched}">
                                                    <input id="percentage-{{i}}" (input)="onCalK3(i)"
                                                        [(ngModel)]="associatedItems[i].percentage"
                                                        [maxlength]="utilsService.validationService.MAX_10"
                                                        formControlName="percentage" type="text" class="form-control"
                                                        mask="separator.2" thousandSeparator="">
                                                </div>
                                            </td>
                                            <td>{{associatedItems[i].totalExpPCSper ? (associatedItems[i].totalExpPCSper
                                                | indianCurrency) : 0}} </td>
                                            <td>{{associatedItems[i].totalFinalCostPCSper ?
                                                (associatedItems[i].totalFinalCostPCSper | indianCurrency) : 0}} </td>
                                        </ng-container>

                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.DONE">
                                            <!-- J4 -->
                                            <td> {{associatedItems[i].j4Amt ? associatedItems[i].j4Amt : 0}} </td>
                                            <!-- K4 -->
                                            <!-- <td> - </td> -->
                                        </ng-container>


                                        <ng-container *ngIf="poImportObj.shippingTypes == enumForShippingType.PIECE">
                                            <td>
                                                <div class="form-group form-group-sm form-group-100"
                                                    [ngClass]="{'form-error': contact.get('expensePcs').invalid && contact.get('expensePcs').touched}">
                                                    <input id="expensePcs-{{i}}" (input)="onCalK5(i)"
                                                        [(ngModel)]="associatedItems[i].expensePcs"
                                                        [maxlength]="utilsService.validationService.MAX_10"
                                                        formControlName="expensePcs" type="text" class="form-control"
                                                        mask="separator.2" thousandSeparator="">
                                                </div>
                                            </td>
                                            <td>{{associatedItems[i].totalItemAmt ? (associatedItems[i].totalItemAmt |
                                                indianCurrency) : 0}} </td>
                                        </ng-container>

                                        <td>
                                            <ng-container *ngFor="let ls of associatedItems[i]?.lastRecord">
                                                {{ls?.supplierShortCode}}, {{ls?.pricePerCarton}}, {{ls?.poCarton}}
                                                <br />
                                            </ng-container>
                                        </td>
                                        <td> 
                                            @for(ls of associatedItems[i]?.inquiryShopWithPrice; track $index){
                                                <span class="w-100 d-block">{{ls?.supplierShortCode}}, {{ls?.pricePerItem}}</span>
                                            } @empty{
                                              <span>-</span>
                                            }
                                        </td>
                                        <td>
                                            <div *ngIf="associatedItems[i].customerCount && associatedItems[i].inquiryCount; else elseBlockInq">
                                                {{associatedItems[i].customerCount}} {{'[' + associatedItems[i].inquiryCount + ']'}}
                                            </div>
                                            <ng-template #elseBlockInq>
                                                -
                                            </ng-template>
                                        </td>
                                        <td>
                                            <div class="d-flex justify-content-center">
                                                <div class="checkbox checkbox-primary checkbox-small">
                                                    <input [(ngModel)]="associatedItems[i].isQCRequired" type="checkbox" id="qc-{{i}}"
                                                        formControlName="isQCRequired" class="material-inputs filled-in" />
                                                    <label for="qc-{{i}}"></label>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex justify-content-center">
                                                <div class="checkbox checkbox-primary checkbox-small">
                                                    <input [(ngModel)]="associatedItems[i].isPhotosRequired" type="checkbox" id="real-photo-{{i}}"
                                                        formControlName="isPhotosRequired" class="material-inputs filled-in" />
                                                    <label for="real-photo-{{i}}"></label>
                                                </div>
                                            </div>
                                        </td>
                                        <!-- <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select class="" placeholder="QC Checklist" [clearable]="true"
                                                    [ngClass]="{'required': contact.get('qcCheckListId').invalid && contact.get('qcCheckListId').touched}"
                                                    [items]="associatedItems[i].checkList" [multiple]="false"
                                                    bindLabel="label" bindValue="value"
                                                    [(ngModel)]="associatedItems[i].qcCheckListId"
                                                    formControlName="qcCheckListId" appendTo=".theme-ngselect">
                                                </ng-select>
                                            </div>
                                        </td> -->
                                        <td> {{associatedItems[i].rate ? (associatedItems[i].rate) : 0}} </td>
                                        <td> {{associatedItems[i].gst_amounts ? (associatedItems[i].gst_amounts | indianCurrency) : 0}} </td>
                                        <td>
                                            <div class="form-group form-group-sm form-group-100"
                                                [ngClass]="{'form-error': contact.get('extraExpense').invalid && contact.get('extraExpense').touched}">
                                                <input id="extraExpense-{{i}}" [(ngModel)]="associatedItems[i].extraExpense"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    formControlName="extraExpense" type="text" class="form-control"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>
                                            {{associatedItems[i].purchaseRatio ? (associatedItems[i].purchaseRatio ) :
                                            '-'}}
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm">
                                                <div class="form-control-wrapper">
                                                    <div class="input-group input-group-sm input-group-select tbl-input-group">
                                                        <input [(ngModel)]="associatedItems[i].itemLength" [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemLength').invalid && contact.get('itemLength').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemLength" type="text" class="form-control">
                                                        <input [(ngModel)]="associatedItems[i].itemWidth" [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWidth').invalid && contact.get('itemWidth').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWidth" type="text" class="form-control">
                                                        <input [(ngModel)]="associatedItems[i].itemHeight" [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemHeight').invalid && contact.get('itemHeight').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemHeight" type="text" class="form-control">
                                                        <ng-select class="unit-width" placeholder="Unit" [multiple]="false" [clearable]="false" [appendTo]="'body'"
                                                            [ngClass]="{'required': contact.get('itemDimUnitMaster').invalid && contact.get('itemDimUnitMaster').touched}"
                                                            [items]="associatedItems[i].itemDimArr" bindLabel="shortCode" bindValue="id"
                                                            formControlName="itemDimUnitMaster" [(ngModel)]="associatedItems[i].itemDimUnitMasterId">
                                                            <ng-template ng-option-tmp let-item="item">
                                                                <div [title]="item.shortCode">
                                                                    {{ item.shortCode }}
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm ">
                                                <div class="form-control-wrapper">
                                                    <div class="input-group input-group-sm input-group-select tbl-input-group ">
                                                        <input [(ngModel)]="associatedItems[i].itemWeight" [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWeight').invalid && contact.get('itemWeight').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWeight" type="text" class="form-control">
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" id="button-INR">
                                                            KG
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm">
                                                <div class="form-control-wrapper">
                                                    <div class="input-group input-group-sm input-group-select tbl-input-group">
                                                        <input [(ngModel)]="associatedItems[i].itemWithBoxLength"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWithBoxLength').invalid && contact.get('itemWithBoxLength').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWithBoxLength" type="text"
                                                            class="form-control">
                                                        <input [(ngModel)]="associatedItems[i].itemWithBoxWidth"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWithBoxWidth').invalid && contact.get('itemWithBoxWidth').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWithBoxWidth" type="text"
                                                            class="form-control">
                                                        <input [(ngModel)]="associatedItems[i].itemWithBoxHeight"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWithBoxHeight').invalid && contact.get('itemWithBoxHeight').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWithBoxHeight" type="text"
                                                            class="form-control">
                                                        <ng-select class="unit-width" placeholder="Unit" [multiple]="false" [clearable]="false"
                                                            [appendTo]="'body'"
                                                            [ngClass]="{'required': contact.get('itemWithBoxDimUnitMaster').invalid && contact.get('itemWithBoxDimUnitMaster').touched}"
                                                            [items]="associatedItems[i].itemBoxDimArr" bindLabel="shortCode" bindValue="id"
                                                            formControlName="itemWithBoxDimUnitMaster"
                                                            [(ngModel)]="associatedItems[i].itemWithBoxDimUnitMasterId">
                                                            <ng-template ng-option-tmp let-item="item">
                                                                <div [title]="item.shortCode">
                                                                    {{ item.shortCode }}
                                                                </div>
                                                            </ng-template>
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm ">
                                                <div class="form-control-wrapper">
                                                    <div class="input-group input-group-sm input-group-select tbl-input-group ">
                                                        <input [(ngModel)]="associatedItems[i].itemWithBoxWeight"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            [ngClass]="{'required': contact.get('itemWithBoxWeight').invalid && contact.get('itemWithBoxWeight').touched}"
                                                            mask="separator.3" thousandSeparator="" formControlName="itemWithBoxWeight" type="text"
                                                            class="form-control">
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" id="button-INR">
                                                            KG
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('tag').invalid && contact.get('tag').touched}">
                                                <input (input)="onChangeTagQty(associatedItems[i], i)" [(ngModel)]="associatedItems[i].tag"
                                                    [maxlength]="utilsService.validationService.MAX_30" formControlName="tag" type="text" class="form-control">
                                            </div>
                                        </td>
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select class="" placeholder="Select" [clearable]="false" [closeOnSelect]="false"
                                                    [ngClass]="{'required': contact.get('packingTypeIds').invalid && contact.get('packingTypeIds').touched}"
                                                    [items]="associatedItems[i].packingTypes" [multiple]="true" bindLabel="label" bindValue="value"
                                                    [clearSearchOnAdd]="true" [(ngModel)]="associatedItems[i].packingTypeIds"
                                                    formControlName="packingTypeIds" [appendTo]="'.theme-ngselect'">
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-sm"
                                                [ngClass]="{'form-error': contact.get('measurementCode').invalid && contact.get('measurementCode').touched}">
                                                <input [(ngModel)]="associatedItems[i].measurementCode" [maxlength]="utilsService.validationService.MAX_100"
                                                    formControlName="measurementCode" type="text" class="form-control">
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="tbl-add-new">
                                        <td colspan="100">
                                            <button (click)="addItem()"
                                                class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                    class="th-bold-add-circle"></i>
                                                Add New Row
                                            </button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group form-group-inline-control">
                            <label class="form-label">Add Comment</label>
                            <div class="form-control-wrapper">
                                <textarea [maxLength]="utilsService.validationService.MAX_200" class="form-control mb-2"
                                    [(ngModel)]="commentObj.comments" [ngModelOptions]="{standalone: true}" rows="2"
                                    placeholder="Enter your comment">
                                </textarea>
                                <div class="d-flex gap-2">
                                    <button [disabled]="!commentObj.comments" (click)="onSaveComment()" type="button"
                                        class="btn btn-sm btn-primary btn-icon-text">
                                        <i class="th th-outline-tick-circle"></i> {{isEditPhase ? 'Update' :
                                        'Save'}}</button>
                                </div>
                            </div>
                        </div>

                        <div class="comment-list-wrapper comment-overflow">
                            <div class="comment-user">
                                <div class="comment-user-wrapper" *ngFor="let item of commentList; index as i;">
                                    <div class="comment-user-rectangle">
                                        <span></span>
                                    </div>
                                    <div class="comment-user-image">
                                        <img [src]="item.profile ? (utilsService.imgPath + item.profile) : 'assets/images/avatar-default.svg'"
                                            alt="valamji">
                                    </div>
                                    <div class="comment-user-text-action">
                                        <div class="comment-user-text">
                                            <p>{{item.username}}</p>
                                            <span>{{item.comments}}</span>
                                        </div>
                                        <div class="comment-user-icon" *ngIf="item.userId == utilsService.userId">
                                            <div class="d-flex flex-row gap-2">
                                                <button (click)="onEditComment(item, i)"
                                                    class="btn btn-sm btn-icon btn-transparent text-black"
                                                    ngbTooltip="Edit" placement="bottom" container="body"
                                                    triggers="hover"><i class="th th-outline-edit"></i>
                                                </button>
                                                <button (click)="openDeleteModalComment(item, i)"
                                                    class="btn btn-sm btn-icon btn-transparent text-danger"
                                                    ngbTooltip="Delete" placement="bottom" container="body"
                                                    triggers="hover"><i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>

                <!--  -->
                <div class='bottombar-left' *ngIf="!poImportId">
                    <button [disabled]="checkIfCommentsNotSaved()" (click)="onSavePOImport(false)" type="button"
                        class="btn btn-primary btn-icon-text btn-sm">
                        <i class="th th-outline-tick-circle"></i>Save And Send
                    </button>
                    <button [disabled]="checkIfCommentsNotSaved()" *ngIf="poStatus == utilsService.poImportStatus.DRAFT"
                        (click)="onSavePOImport(true)" type="button"
                        class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-document-text"></i>Save as
                        Draft
                    </button>
                    <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"
                        [routerLink]="['/users/purchases/po-import']"><i
                            class="th th-outline-close-circle"></i>Cancel</button>
                </div>

                <!--  -->
                <div class='bottombar-left' *ngIf="poImportId && this.selectedTab == enumForStatus.DRAFT">
                    <button [disabled]="checkIfCommentsNotSaved()" (click)="onSavePOImport(false)" type="button"
                        class="btn btn-primary btn-icon-text btn-sm">
                        <i class="th th-outline-tick-circle"></i>Convert To PO
                    </button>
                    <button [disabled]="checkIfCommentsNotSaved()"
                        *ngIf="poStatus == utilsService.poImportStatus.DRAFT && !convertToPo"
                        (click)="onSavePOImport(true)" type="button"
                        class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-document-text"></i>Save
                    </button>
                    <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"
                        [routerLink]="['/users/purchases/po-import']"><i
                            class="th th-outline-close-circle"></i>Cancel</button>
                </div>

                <!--  -->
                <div class='bottombar-left' *ngIf="poImportId && this.selectedTab == enumForStatus.PO_CREATED">
                    <button [disabled]="checkIfCommentsNotSaved()" (click)="onSavePOImport(false)" type="button"
                        class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-document-text"></i>Save
                    </button>
                    <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"
                        [routerLink]="['/users/purchases/po-import']"><i
                            class="th th-outline-close-circle"></i>Cancel</button>
                </div>

                <div class='bottombar-right'>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Link Item Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class=" modal modal-theme fade" id="LinkItemModal" tabindex="-1" aria-labelledby="LinkItemModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title" id="exampleModalLabel">Link Items</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body p-0">
                <div class="modal-table">
                    <div class="card card-theme  ">
                        <div class="card-body p-0">
                            <div class="table-responsive ">
                                <table class="table-theme table-hover table table-bordered  table-sticky">
                                    <thead class="border-less">
                                        <tr>
                                            <th>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class=" checkbox checkbox-primary checkbox-small">
                                                        <input [(ngModel)]="flagForSelectAll" (change)="selectAll()"
                                                            type="checkbox" id="tbl-checkbox-link"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox-link"></label>
                                                    </div>
                                                    Item Details
                                                </div>
                                            </th>
                                            <th>Item Group</th>
                                            <th>Purchase <br />Breach Qty</th>
                                            <th>Season Date</th>
                                            <th>Advance PO <br /> Date</th>
                                            <th>Average Price <br />(Surat)</th>
                                            <th>Average Price Surat<br />(With GST)</th>
                                            <th>Sale Price</th>
                                            <!-- <th>Marka</th> -->
                                            <th>PO Carton</th>
                                            <th>PCS/Carton</th>
                                            <th>Total Qty</th>
                                            <th>Price /PCS <br />(RMB)</th>
                                            <th>Total Price <br />(RMB)</th>
                                            <th>Color</th>
                                            <th>Note</th>
                                            <th>English <br />Comment</th>
                                            <th>China <br />Comment</th>
                                            <th>Expected <br />Delivery Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of linkedItemList; index as i">
                                            <td class="tbl-user new-ng-width">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input [(ngModel)]="item.isSelected"
                                                            (change)="selectUnselect(item.id, i, item.isSelected)"
                                                            type="checkbox" id="tbl-checkbox2-link-{{i}}"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2-link-{{i}}"></label>
                                                    </div>
                                                    <span>{{(i + 1) | padNum}}.</span>
                                                    <div class="tbl-user-wrapper">
                                                        <div class="tbl-user-image" *ngIf="item?.formattedName">
                                                            <img [src]="utilsService.imgPath + item?.formattedName"
                                                                alt="valamji">
                                                        </div>
                                                        <div class="tbl-user-image" *ngIf="!item?.formattedName">
                                                            {{item?.displayName?.charAt(0).toUpperCase()}}
                                                        </div>
                                                        <div class="tbl-user-text-action">
                                                            <div class="tbl-user-text">
                                                                <p>{{item.displayName}}</p>
                                                                <span>{{item.skuId}}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{item.itemGroupName}}</td>
                                            <td>{{item.levelBreachQtys ? (item.levelBreachQtys.levelName + ': ' +
                                                (item.levelBreachQtys?.breachQtys ? item.levelBreachQtys?.breachQtys :
                                                '')) : '-'}}</td>
                                            <td>{{item.fromDate ? ((item.fromDate | date: 'dd-MM') + ' to ' +
                                                (item.toDate
                                                | date: 'dd-MM')) : '-'}}</td>
                                            <td>{{item.advanceDate ? (item.advanceDate | date: 'dd-MM') : '-'}}</td>
                                            <td>{{item.averagePriceWithoutGST || '-'}}</td>
                                            <td>{{item.averagePriceWithGST || '-'}}</td>
                                            <td>{{item.itemPrice}}</td>
                                            <!-- <td>{{item.marka ? item.marka : '-'}}</td> -->
                                            <td>{{item.poCarton}}</td>
                                            <td>{{item.pricePerCarton}}</td>
                                            <td>{{item.totalPcsQty}}</td>
                                            <td>{{item.pricePerItem}}</td>
                                            <td>{{item.totalAmount}}</td>
                                            <td class="tbl-level">
                                                @for(color of item.colorName; track $index) {
                                                    <span>{{color}}</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="tbl-po-notes">{{item.note ? item.note : '-'}}</div>
                                            </td>
                                            <td>
                                                <div class="tbl-po-notes">{{item.englishComment ? item.englishComment : '-'}}</div>
                                            </td>
                                            <td>
                                                <div class="tbl-po-notes">{{item.chinaComment ? item.chinaComment : '-'}}</div>
                                            </td>
                                            <td>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</td>
                                        </tr>
                                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(linkedItemList)">
                                            <td colspan="20" class="text-center">
                                                <span
                                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer p-3">
                <div class="modal-footer-group full-width-btn ">
                    <button (click)="onLinkItems()" type="button" class="btn btn-sm btn-primary btn-icon-text">
                        <i class="th th-outline-tick-circle"></i>Save </button>
                    <button type="button" class="btn btn-sm btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                 Link Item Modal End              -->
<!-- ----------------------------------------------------------------------- -->

<div class="modal modal-theme modal-confirmation modal-reject fade" id="associateItemPODeleteModal" tabindex="-1"
    aria-labelledby="associateItemPODeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p *ngIf="!itemObj.id">You want to Remove <b>{{"Selected Item"}}</b>.</p>
                        <p *ngIf="itemObj.id">You want to Remove <b>{{itemObj.item?.displayName}}</b> Item.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="removeAI()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Comment Start                          -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteCommentPoModal" tabindex="-1"
    aria-labelledby="deleteCommentPoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Selected <b>Comment</b></p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="onDeleteComment()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Comment End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- Supplier Change Warning  -->
<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="supplierChangeModal" tabindex="-1"
    aria-labelledby="supplierChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to switch Supplier? </p>
                        <p><b>Note:</b> Existing Items will be removed.</p>
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="supplierChanged()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>Confirm</button>
                </div>
            </div>
        </div>
    </div>
</div>


<!--  PO LIMIT INCREASE OR PODATE BACKDATED -->

<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="saveWarningPOModal" tabindex="-1"
    aria-labelledby="saveWarningPOModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to {{poImportId ? 'update' : 'create'}} this Purchase Order? </p>

                        <ng-container *ngIf="isDateError">
                            <p><b>Note:</b> This PO has a backdated date.</p>
                        </ng-container>

                        <ng-container *ngIf="!isDateError">
                            <p><b>Note:</b> PO Amount(RMB) is exceeding the provided PO Limit(RMB).</p>
                        </ng-container>

                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="onSavePOImport(isDraftFlag, true)" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>Confirm</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                       product modal Slider Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="product-slider-modal modal modal-theme fade" id="productmodalSliderPOModal" tabindex="-1"
    aria-labelledby="productmodalSliderPOModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" *ngIf="showSliderModal">
            <div class="modal-header">
                <div class="modal-header-details">
                    <div class="">
                        <h5 class="modal-title image-name"
                            [title]="currentItem?.originalName ? currentItem?.originalName : currentItem?.formattedName">
                            <div>
                                {{ currentItem?.originalName ? currentItem?.originalName :
                                currentItem?.formattedName }}
                            </div>
                        </h5>
                        <p>{{itemObj.skuId}}</p>
                    </div>
                    <button (click)="onCopy()" [disabled]="isCopied"
                        [copyText]="!currentItem?.isMediaLinks ? (utilsService.imgPath + currentItem?.formattedName) : currentItem?.formattedName"
                        type="button" class="btn btn-sm btn-light-white btn-icon-text">
                        <i class="th th-outline-tick-circle"></i> {{isCopied ? 'Copied' : 'Copy Link'}}
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <app-image-view-modal [selectedImageIndex]="selectedImageIndex" [itemImagesList]="itemImagesList"
                    (onSlideChange)="onSlideChange($event)" />
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                    product modal Slider Modal                             -->
<!-- ----------------------------------------------------------------------- -->
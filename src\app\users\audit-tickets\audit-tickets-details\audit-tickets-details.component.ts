import { Location } from '@angular/common';
import { Component, computed, inject, OnDestroy, OnInit, signal, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { EnumForQCProperty, EnumForTicketType, EnumForTicketTypeLabel } from '@enums/EnumForQCProperty';
import { EnumForTicketStatus } from '@enums/EnumForTicketStatus';
import { AuditDetailsList, ItemChildTickets } from '@modal/AuditTicket';
import { AvailableQtyModalData } from '@modal/SBTBranchStock';
import { NgbModalService } from '@service/ngb-modal.service';
import { UtilsService } from '@service/utils.service';
import { map, Observable, Subject, takeUntil, tap } from 'rxjs';

@Component({
  selector: 'app-audit-tickets-details',
  templateUrl: './audit-tickets-details.component.html',
  styleUrls: ['./audit-tickets-details.component.scss']
})
export class AuditTicketsDetailsComponent implements OnInit, OnDestroy {

  utilsService = inject(UtilsService);
  private modalService = inject(NgbModalService);

  selectedTicketId = signal<number>(null)
  selectedIndex = signal<number>(null)
  ticketId = signal<number | null>(null);

  branchId: number;
  selectedStatus: string;

  dropdown = { branch: [], status: [] }

  auditTicketList$: Observable<AuditDetailsList[]>;
  auditTicketDetails = signal<AuditDetailsList>(null);

  enumForTicketStatus = EnumForTicketStatus;
  enumForTicketType = EnumForTicketType;
  itemExpand = signal(true);
  updateHistoryExpand = signal(true);
  reassignExpand = signal(true);
  notesModalData = signal('');
  private destroy$ = new Subject<void>();

  emptyItemDetails = computed(() => this.auditTicketDetails()?.result?.length === 0);
  emptyReassignDetails = computed(() => this.auditTicketDetails()?.childTicket?.itemDetails?.length === 0);

  isItemWise = computed(() => this.auditTicketDetails()?.isItemWise);

  enumForQC = EnumForQCProperty;
  isQcExpand = signal(true);
  searchParam = signal({} as any);

  constructor(private route: ActivatedRoute, private location: Location, private router : Router) {
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      console.log('Received Ticket Data via State:', navigation.extras.state['searchParam']);
      this.searchParam.set(navigation.extras.state['searchParam'])

      // Output: { status: 'Open', priority: 'High', description: 'Initial audit ticket data.' }
    }
  }

  ngOnInit(): void {
    // Setting data from url
    this.selectedTicketId.set(+this.route.snapshot.params['ticketId'])
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(param => {
      this.branchId = +param['branchId'] || null
    })

    // Get ticket details using async pipe
    this.auditTicketList$ = this.getTicketDetails();
  }

  getTicketDetails = (): Observable<AuditDetailsList[]> => {

    this.auditTicketDetails.set(null);
    console.warn(this.searchParam())
    const param = this.searchParam() || {};
    param.branchId = this.branchId;
    param.status = this.selectedStatus;

    return this.utilsService.post(this.utilsService.serverVariableService.ASSIGN_TICKET_DETAILS, param, null, true).pipe(
      tap(init => {
        this.dropdown.branch = init.branches || [];
        this.dropdown.status = init.statuses || [];
      }),
      map(res => res.tickets || []),
      tap((data: AuditDetailsList[]) => {
        const ticketIndex = (data || []).findIndex(t => t.id === this.selectedTicketId());
        const indexList = ticketIndex === -1 ? 0 : ticketIndex
        const id = data[indexList]?.id
        if (id) {
          this.onChangeTicket(indexList, id);
          this.changeStateRoute();
        }
      })
    )
  }

  onChangeTicket = (index: number, id: number) => {
    this.selectedIndex.set(index);
    this.ticketId.set(id);
    this.changeStateRoute()
    this.getTicketByID();
  };

  getTicketByID = () => {
    const API = `${this.utilsService.serverVariableService.TICKET_DETAILS_BY_ID}?ticketId=${this.ticketId()}`

    this.utilsService.get(API, null, true).pipe(
      tap((res: AuditDetailsList) => {
        const result = (res?.auditHistory || [])?.map(history => ({
          dateTime: history.dateTime,
          user: history.user,
          newArray: (history?.locations || []).flatMap(loc =>
            (loc?.items || []).map(item => ({
              ...item,
              locationName: loc.locationName,
              locationId: loc.locationId,
              locationType: loc.locationType
            }))
          ),
          isExpand: true
        }));

        this.auditTicketDetails.set({
          ...res,
          result
        });
      })
    ).subscribe()
  }

  openNotesModal = (modal: TemplateRef<any>, notes: string) => {
    this.notesModalData.set(notes);
    this.modalService.open(modal, { windowClass: 'modal-lg' });
  }

  onChangeFilter(type: 'branch' | 'status') {

    switch (type) {
      case 'branch':
        this.changeStateRoute();
        break;
    }
    this.auditTicketList$ = this.getTicketDetails();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Available Qty modal api marka wise
  getAvailableQtyCommonAPI = (item: ItemChildTickets, index: number, parentIndex: number, isItemDetails?: boolean) => {
    
    const param = { itemId : item.itemId, 
                  fromLocationId : item.locationId, 
                  fromLocationType : item.locationType, 
                  isDropLocationData : true,
                  isDropFlag : true,
                  isWithoutGroupBy : false,
                  isWithoutMarkaGroupBy : true
     };
    const mapAvailableQtyModalData = (res: AvailableQtyModalData[]) => {
      return res.map(a => ({
        ...a,
        looseQty: a.looseQty || 0,
        totalCartonQty: (a.cartonQty || 0) * (a.piecesPerCarton || 0),
        totalQty: ((a.cartonQty || 0) * (a.piecesPerCarton || 0)) + (a.looseQty || 0)
      }));
    };

    this.utilsService.post(this.utilsService.serverVariableService.AVAILABLE_QTY_OF_ITEM_WISE, param, null, true).pipe(
      tap((res: AvailableQtyModalData[]) => {

        const availableQtyModalData = mapAvailableQtyModalData(res);

        this.auditTicketDetails.update(audit => {
          if (!audit) return audit;

          return {
            ...audit,
            result: audit.result.map((item, i) => i === parentIndex ? {
              ...item, newArray: item.newArray?.map((n, j) => j === index ? { ...n, availableQtyModalData } : n) || []
            } : item)
          };
        });
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  };



  changeStateRoute = () => {
    this.location.replaceState('/users/audit-tickets/audit-tickets-details/' + this.ticketId(), "branchId=" + this.branchId);
  }

  onExpand = (index: number) => {
    this.auditTicketDetails.update(state => {
      if (!state?.result) return state;

      const updatedResult = state.result.map((item, i) => {
        if (i !== index) return item;
        return {
          ...item,
          isExpand: !item.isExpand
        };
      });

      return {
        ...state,
        result: updatedResult
      };
    });
  };

  onExpandQC = () => {
    this.isQcExpand.update(state => !state);
  }
}

<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.AUDIT_TICKET, action: utilsService.enumForPage.VIEW_AUDIT_TICKET, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>{{auditTicketId() ? 'Edit' : 'Add New'}} Ticket </h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/audit-tickets/']"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class="card card-theme card-forms">
            <div class="card-body" [formGroup]="form">
                <div class="row mb-3">
                    <div class="col-lg-5 col-md-5 col-sm-12">
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Ticket Subject</label>
                            <div class="form-control-wrapper ">
                                <ng-select formControlName="ticketSubjectId" class="custom-ng-select"
                                    placeholder="Select Ticket Subject" [multiple]="false" [clearable]="false"
                                    [items]="dropdown.ticketSubject" bindLabel="label" bindValue="value">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="form.controls['ticketSubjectId'].hasError('required') && form.controls['ticketSubjectId'].touched">
                                    {{utilsService.validationService.TICKET_SUBJECT_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Branch</label>
                            <div class="form-control-wrapper ">
                                <ng-select formControlName="branchId" class="custom-ng-select"
                                    placeholder="Select Branch" [multiple]="false" [clearable]="false"
                                    [items]="dropdown.branches" bindLabel="branchName" bindValue="id">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="form.controls['branchId'].hasError('required') && form.controls['branchId'].touched">
                                    {{utilsService.validationService.BRANCH_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Warehouse</label>
                            <div class="form-control-wrapper ">
                                <ng-select formControlName="warehouseId" class="custom-ng-select"
                                    placeholder="Select Warehouse" [multiple]="false" [clearable]="false"
                                    [items]="dropdown.filteredWarehouse" bindLabel="warehouseName" bindValue="id" (change)="onChangeWarehouse()">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="form.controls['warehouseId'].hasError('required') && form.controls['warehouseId'].touched">
                                    {{utilsService.validationService.WAREHOUSE_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Assign User</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="custom-ng-select" formControlName="userId"
                                    placeholder="Select Assign User" [multiple]="false" [clearable]="true"
                                    [items]="dropdown.users" bindLabel="label" bindValue="value">
                                </ng-select>
                            </div>
                        </div>
                        <!-- <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Subject Status</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="custom-ng-select"
                                    placeholder="Select Subject Status" [multiple]="false" [clearable]="false"
                                    [items]="[]" bindLabel="label" bindValue="value">
                                </ng-select>
                            </div>
                        </div> -->

                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Ticket Note</label>
                            <div class="form-control-wrapper ">
                                <textarea placeholder="Enter Note" class="form-control" formControlName="ticketNote"
                                    [maxLength]="utilsService.validationService.MAX_500"></textarea>
                            </div>
                        </div>

                        <div class="form-group form-group-inline-control ">
                            <label class="form-label"></label>
                            <div class="form-control-wrapper ">
                                <div class="checkbox checkbox-primary checkbox-small">
                                    <input formControlName="isRequiredImage" type="checkbox" id="photos"
                                        class="material-inputs filled-in" />
                                    <label for="photos"> Required to add photos </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-5 col-md-5 col-sm-12 " (dragover)="onSelectAttachments($event);doc.value = ''"
                        (drop)="onSelectAttachments($event);doc.value = ''" (paste)="onSelectAttachments($event)">
                        <div class="attachments-wrapper">
                            <div class="form-group">
                                <div class="form-label">Upload Files<i class="th th-outline-info-circle ms-1"
                                        [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom"
                                        container="body" triggers="hover"></i>
                                </div>
                            </div>
                            <div class='attachments-container'>
                                <div class='attachments-content'>
                                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                    <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                                </div>
                                <input #doc type="file" ref={imageRef} multiple
                                    (change)="onSelectAttachments($event);doc.value = ''" />
                            </div>
                            <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                <div class='attachments-upload-row'>
                                    @for (item of attachmentsList.get(); track $index; let i = $index) {
                                    <div class="attachments-upload-col">
                                        <div class="card-attachments-upload">
                                            <div class="attachments-image">
                                                @if (utilsService.isImage(item.originalName)) {
                                                <img loading="lazy"
                                                    (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                    [src]="item.formattedName ? (item.file ? item.formattedName : utilsService.imgPath + item.formattedName) : null"
                                                    alt="valamji" />
                                                } @else if (utilsService.isMedia(item.originalName)) {
                                                <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                    src="assets/images/files/file-video.svg" alt="valamji" />
                                                } @else if (utilsService.isExcel(item.originalName)) {
                                                <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                    src="assets/images/files/file-excel.svg" alt="valamji" />
                                                } @else if (utilsService.isDocument(item.originalName)) {
                                                <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                    src="assets/images/files/file-pdf.svg" alt="valamji" />
                                                }
                                            </div>
                                            <div class="attachments-text" [ngbTooltip]="item.originalName"
                                                placement="bottom" container="body" triggers="hover">
                                                <h6 class="file-name">{{ item.originalName }}</h6>
                                            </div>
                                            <button (click)="removeAttachment(i, item)" class="btn-close"
                                                variant="close"><i class="th th-close"></i></button>
                                        </div>
                                    </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-lg-12 col-md-12 col-sm-12 mt-3">
                        <div class="inner-title-wrapper">
                            <div class="inner-title-left flex-column align-items-start gap-2 ">
                                <div class="inner-title-text">
                                    <h6 class="">Associated Item*</h6>
                                </div>
                                <div
                                    class="form-group form-group-inline justify-content-start align-items-center gap-3 mb-0">
                                    <label class="form-label mb-0 me-2 fw-semibold">Stock Check Type</label>
                                    <div class="form-check radio radio-primary form-check-inline">
                                        <input formControlName="isItemWise" type="radio" id="isItemWise"
                                            name="isItemWise" [value]="true">
                                        <label for="isItemWise">By Item</label>
                                    </div>

                                    <div class="form-check radio radio-primary form-check-inline">
                                        <input formControlName="isItemWise" type="radio" id="isLocationWise"
                                            name="isItemWise" [value]="false">
                                        <label for="isLocationWise">By Location</label>
                                    </div>
                                </div>
                            </div>
                            <div class="inner-title-rigth">
                            </div>
                        </div>
                    </div>
                </div>

                @let stockCheckType = form.get('isItemWise')?.value;

                <div class="row" *ngIf="stockCheckType">
                    <div class="col-12">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky"
                                formArrayName="auditTicketItemReqList">
                                <thead class="border-less">
                                    <tr>
                                        <th>Item</th>
                                        <th>Current Location</th>
                                        <th>Available Carton</th>
                                        <th>Total Carton Qty</th>
                                        <th>Loose Qty</th>
                                        <th>Total Qty</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let contact of items.controls; index as i" [formGroupName]="i"
                                        class="tbl-add-row tbl-bg-white">
                                        <td class="tbl-user tbl-form-group" style="width: 0;">
                                            <div class="tbl-user-checkbox-srno">
                                                <span>{{i + 1 | padNum}}</span>
                                                <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100">
                                                    <ng-select (change)="onItemChange($event, i)" id="itemId-{{i}}" class="new-po-select"
                                                        appendTo=".theme-ngselect" [items]="dropdown.items"
                                                        [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}"
                                                        formControlName="itemId" bindLabel="name" placeholder="Select Item" bindValue="id"
                                                        [virtualScroll]="true" [clearable]="false" [searchFn]="customSearchFn">
                                                        <ng-template ng-header-tmp>
                                                            <div class="d-flex fw-bold bg-light border-bottom py-2 px-3">
                                                                <div class="fs-14">Item</div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-label-tmp let-item="item">
                                                            <div class="tbl-user">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image">
                                                                            <img loading="lazy"
                                                                                [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                                                                alt="valamji">
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{ item.displayName }} </p>
                                                                                <span>{{item.skuId}}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-option-tmp let-item="item">
                                                            <div class="d-flex align-items-center border-bottom py-2 px-3">
                                                                <div>
                                                                    <div class="tbl-user">
                                                                        <div class="tbl-user-checkbox-srno">
                                                                            <div class="tbl-user-wrapper">
                                                                                <div class="tbl-user-image">
                                                                                    <img loading="lazy"
                                                                                        [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                                                                        alt="valamji">
                                                                                </div>
                                                                                <div class="tbl-user-text-action">
                                                                                    <div class="tbl-user-text po-description">
                                                                                        <p [title]="item.displayName">{{
                                                                                            item.displayName }} </p>
                                                                                        <span>{{ item.skuId }}</span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-notfound-tmp>
                                                            <div class="d-flex align-items-center border-bottom py-2 px-3 justify-content-center fs-12">
                                                                <div>No Item Found</div>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{contact.get('allLocation').value || '-'}}</td>
                                        <td>
                                            <app-available-qty-modal (openModal)="getAvailableQtyCommonAPI(contact.get('itemId').value, i)"
                                                [value]="contact.get('availableTotalCartons').value" [data]="contact.get('branchStockList').value || []" />
                                        </td>
                                        <td>{{contact.get('availableTotalPieces').value || '-'}}</td>
                                        <td>{{contact.get('availableTotalLooseQty').value || '-'}}</td>
                                        <td>{{contact.get('availableTotalQty').value || '-'}}</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    (click)="openRemoveAuditTicketItemModal(i, deleteNewAuditItem, true)"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="tbl-add-new">
                                        <td colspan="100">
                                            <button (click)="addItems()"
                                                class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                    class="th-bold-add-circle"></i>
                                                Add New Row
                                            </button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row" *ngIf="!stockCheckType">
                    <div class="col-4">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky"
                                formArrayName="auditTicketLocReqList">
                                <thead class="border-less">
                                    <tr>
                                        <th>Location</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let contact of locations.controls; index as i" [formGroupName]="i"
                                        class="tbl-add-row tbl-bg-white">
                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group theme-ngselect">
                                                <ng-select (change)="onLocationChange($event, i)"
                                                    placeholder="Select Location" [multiple]="false"
                                                    formControlName="location" [clearable]="false"
                                                    [items]="dropdown.location" bindLabel="label"
                                                    bindValue="label" appendTo=".theme-ngselect">
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    (click)="openRemoveAuditTicketItemModal(i, deleteNewAuditItem, false)"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="tbl-add-new">
                                        <td colspan="100">
                                            <button (click)="addLocations()"
                                                class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                    class="th-bold-add-circle"></i>
                                                Add New Row
                                            </button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>
                <div class='bottombar-left'>
                    <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-tick-circle"></i>
                        {{auditTicketId() ? 'Update' : 'Save'}}
                    </button>
                    <button [routerLink]="['/users/audit-tickets/']" type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
                            class="th th-outline-close-circle"></i>Cancel
                    </button>
                </div>
                <div class='bottombar-right'>
            
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete New Audit Item Modal -->
<ng-template #deleteNewAuditItem let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Remove {{isOpenFromItem() ? 'Item' : 'Location'}}.<br /> <b>{{isOpenFromItem() ? itemName() : locationName() ? locationName() : ""}}</b></p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="removeAuditTicketItem(modal)" type="button" class="btn btn-primary btn-icon-text">
                        <i class="th th-outline-tick-circle"></i> Remove</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<!-- Item Wise Change Warning Modal -->
<ng-template #itemWiseWarning let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.dismiss('cancel')"><i class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to switch Stock Check Type? </p>
                        <p><b>Note:</b> Existing <b>{{ stockCheckType ? 'Items' : 'Locations' }}</b> will be removed.</p>
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.dismiss('cancel')">Cancel</button>
                    <button (click)="modal.close('confirm')" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>Confirm</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
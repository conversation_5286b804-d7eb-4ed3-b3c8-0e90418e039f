import { CountdownConfig } from "ngx-countdown"
import { SaveResMarkaWise } from "./SalesOrder"

export interface SODraftPage {
    id: number
    mobileNo: string
    deliveryType: { label: string, value: string }
    notes: string
    orderStatus: string
    holdTill?: string
    holdDuration?: number
    salesOrderItems: SalesOrderItemDraftListing[]
    paymentTerms?: string
    customerName: string
    dateTime?: string
    isExpand: boolean
    salesOrderNo: string
    allCartonCnt: number
    allCartonQty: number
    allLooseCnt: number
    allOrderedQty: number
    paymentTermsName: string
    allTotalAmt: number
    allTotalFinalAmt: number
    holdReason: string,
    config: CountdownConfig
}

export interface SalesOrderItemDraftListing {
    id: number
    orderUnit: { label: string, value: string }
    orderQty: number
    totalQty: number
    rate: number
    totalAmount: number
    finalAmount: number
    note: string
    itemInfo: {
        displayName: string
        id: number
        image: string
        itemName: string
        skuId: string
    },
    gstTaxName: any
    averagePriceWithGST: number
    averagePriceWithoutGST: number
    discountPercent: number
    discountAmount: number
    isMarkaSelected: boolean
    totalCarton: number
    totalCartonQty: number
    totalLoose: number
    totalOrderQty: number
    lastCartonSellingPrice: number,
    markaQtyOutput: SaveResMarkaWise[]
    gstamount: number
    totalWithGSTAmount: number
}
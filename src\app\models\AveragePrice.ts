import { deserializeAs, serializeAs } from 'cerialize';

export class AveragePrice {

    @serializeAs('F_totalAmountNewWithoutGST')
    @deserializeAs('F_totalAmountNewWithoutGST')
    private _F_totalAmountNewWithoutGST: number;

    @serializeAs('D_newQty')
    @deserializeAs('D_newQty')
    private _D_newQty: number;

    @serializeAs('H_totalAmountOldNewWithGST')
    @deserializeAs('H_totalAmountOldNewWithGST')
    private _H_totalAmountOldNewWithGST: number;

    @serializeAs('G_totalQty')
    @deserializeAs('G_totalQty')
    private _G_totalQty: number;
    
    @serializeAs('containerName')
    @deserializeAs('containerName')
    private _containerName: string;
    
    @serializeAs('E_WithGST')
    @deserializeAs('E_WithGST')
    private _E_WithGST: number;
    
    @serializeAs('F_totalAmountNewWithGST')
    @deserializeAs('F_totalAmountNewWithGST')
    private _F_totalAmountNewWithGST: number;
    
    @serializeAs('newAvgPriceWithGST')
    @deserializeAs('newAvgPriceWithGST')
    private _newAvgPriceWithGST: number;
    
    @serializeAs('E_WithoutGST')
    @deserializeAs('E_WithoutGST')
    private _E_WithoutGST: number;
    
    @serializeAs('newAvgPriceWithoutGST')
    @deserializeAs('newAvgPriceWithoutGST')
    private _newAvgPriceWithoutGST: number;
    
    @serializeAs('previousSp')
    @deserializeAs('previousSp')
    private _previousSp: number;
    
    @serializeAs('currentSp')
    @deserializeAs('currentSp')
    private _currentSp: number;
    
    @serializeAs('H_totalAmountOldNewWithoutGST')
    @deserializeAs('H_totalAmountOldNewWithoutGST')
    private _H_totalAmountOldNewWithoutGST: number;

    @serializeAs('averageOldStock')
    @deserializeAs('averageOldStock')
    private _averageOldStock: AveragePriceOldStock;

    @serializeAs('item')
    @deserializeAs('item')
    private _item: AvgItemObj;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('newSPStaff')
    @deserializeAs('newSPStaff')
    private _newSPStaff: number;

    @serializeAs('newSP')
    @deserializeAs('newSP')
    private _newSP: number;

    @serializeAs('associatedItemId')
    @deserializeAs('associatedItemId')
    private _associatedItemId: number;
    
    constructor() {
        this.isSelected = false;
    }


    /**
     * Getter associatedItemId
     * @return {number}
     */
	public get associatedItemId(): number {
		return this._associatedItemId;
	}

    /**
     * Setter associatedItemId
     * @param {number} value
     */
	public set associatedItemId(value: number) {
		this._associatedItemId = value;
	}


    /**
     * Getter newSP
     * @return {number}
     */
	public get newSP(): number {
		return this._newSP;
	}

    /**
     * Setter newSP
     * @param {number} value
     */
	public set newSP(value: number) {
		this._newSP = value;
	}


    /**
     * Getter item
     * @return {AvgItemObj}
     */
	public get item(): AvgItemObj {
		return this._item;
	}

    /**
     * Setter item
     * @param {AvgItemObj} value
     */
	public set item(value: AvgItemObj) {
		this._item = value;
	}
    

    /**
     * Getter newSPStaff
     * @return {number}
     */
	public get newSPStaff(): number {
		return this._newSPStaff;
	}

    /**
     * Setter newSPStaff
     * @param {number} value
     */
	public set newSPStaff(value: number) {
		this._newSPStaff = value;
	}



    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


   
    /**
     * Getter F_totalAmountNewWithoutGST
     * @return {number}
     */
	public get F_totalAmountNewWithoutGST(): number {
		return this._F_totalAmountNewWithoutGST;
	}

    /**
     * Getter D_newQty
     * @return {number}
     */
	public get D_newQty(): number {
		return this._D_newQty;
	}

    /**
     * Getter H_totalAmountOldNewWithGST
     * @return {number}
     */
	public get H_totalAmountOldNewWithGST(): number {
		return this._H_totalAmountOldNewWithGST;
	}

    /**
     * Getter G_totalQty
     * @return {number}
     */
	public get G_totalQty(): number {
		return this._G_totalQty;
	}

    /**
     * Getter containerName
     * @return {string}
     */
	public get containerName(): string {
		return this._containerName;
	}

    /**
     * Getter E_WithGST
     * @return {number}
     */
	public get E_WithGST(): number {
		return this._E_WithGST;
	}

    /**
     * Getter F_totalAmountNewWithGST
     * @return {number}
     */
	public get F_totalAmountNewWithGST(): number {
		return this._F_totalAmountNewWithGST;
	}

    /**
     * Getter newAvgPriceWithGST
     * @return {number}
     */
	public get newAvgPriceWithGST(): number {
		return this._newAvgPriceWithGST;
	}

    /**
     * Getter E_WithoutGST
     * @return {number}
     */
	public get E_WithoutGST(): number {
		return this._E_WithoutGST;
	}

    /**
     * Getter newAvgPriceWithoutGST
     * @return {number}
     */
	public get newAvgPriceWithoutGST(): number {
		return this._newAvgPriceWithoutGST;
	}

    /**
     * Getter previousSp
     * @return {number}
     */
	public get previousSp(): number {
		return this._previousSp;
	}

    /**
     * Getter currentSp
     * @return {number}
     */
	public get currentSp(): number {
		return this._currentSp;
	}

    /**
     * Getter H_totalAmountOldNewWithoutGST
     * @return {number}
     */
	public get H_totalAmountOldNewWithoutGST(): number {
		return this._H_totalAmountOldNewWithoutGST;
	}

    /**
     * Getter averageOldStock
     * @return {AveragePriceOldStock}
     */
	public get averageOldStock(): AveragePriceOldStock {
		return this._averageOldStock;
	}

    /**
     * Setter F_totalAmountNewWithoutGST
     * @param {number} value
     */
	public set F_totalAmountNewWithoutGST(value: number) {
		this._F_totalAmountNewWithoutGST = value;
	}

    /**
     * Setter D_newQty
     * @param {number} value
     */
	public set D_newQty(value: number) {
		this._D_newQty = value;
	}

    /**
     * Setter H_totalAmountOldNewWithGST
     * @param {number} value
     */
	public set H_totalAmountOldNewWithGST(value: number) {
		this._H_totalAmountOldNewWithGST = value;
	}

    /**
     * Setter G_totalQty
     * @param {number} value
     */
	public set G_totalQty(value: number) {
		this._G_totalQty = value;
	}

    /**
     * Setter containerName
     * @param {string} value
     */
	public set containerName(value: string) {
		this._containerName = value;
	}

    /**
     * Setter E_WithGST
     * @param {number} value
     */
	public set E_WithGST(value: number) {
		this._E_WithGST = value;
	}

    /**
     * Setter F_totalAmountNewWithGST
     * @param {number} value
     */
	public set F_totalAmountNewWithGST(value: number) {
		this._F_totalAmountNewWithGST = value;
	}

    /**
     * Setter newAvgPriceWithGST
     * @param {number} value
     */
	public set newAvgPriceWithGST(value: number) {
		this._newAvgPriceWithGST = value;
	}

    /**
     * Setter E_WithoutGST
     * @param {number} value
     */
	public set E_WithoutGST(value: number) {
		this._E_WithoutGST = value;
	}

    /**
     * Setter newAvgPriceWithoutGST
     * @param {number} value
     */
	public set newAvgPriceWithoutGST(value: number) {
		this._newAvgPriceWithoutGST = value;
	}

    /**
     * Setter previousSp
     * @param {number} value
     */
	public set previousSp(value: number) {
		this._previousSp = value;
	}

    /**
     * Setter currentSp
     * @param {number} value
     */
	public set currentSp(value: number) {
		this._currentSp = value;
	}

    /**
     * Setter H_totalAmountOldNewWithoutGST
     * @param {number} value
     */
	public set H_totalAmountOldNewWithoutGST(value: number) {
		this._H_totalAmountOldNewWithoutGST = value;
	}

    /**
     * Setter averageOldStock
     * @param {AveragePriceOldStock} value
     */
	public set averageOldStock(value: AveragePriceOldStock) {
		this._averageOldStock = value;
	}
    

  
    
}

export class AveragePriceOldStock {

    @serializeAs('averageOldStock')
    @deserializeAs('averageOldStock')
    private _averageOldStock: number;

    @serializeAs('averagePriceWithGST')
    @deserializeAs('averagePriceWithGST')
    private _averagePriceWithGST: number;

    @serializeAs('averagePriceWithoutGST')
    @deserializeAs('averagePriceWithoutGST')
    private _averagePriceWithoutGST: number;

    @serializeAs('totalAmountOldStockWithGST')
    @deserializeAs('totalAmountOldStockWithGST')
    private _totalAmountOldStockWithGST: number;

    @serializeAs('totalAmountOldStockWithoutGST')
    @deserializeAs('totalAmountOldStockWithoutGST')
    private _totalAmountOldStockWithoutGST: number;

    constructor() {}

    /**
     * Getter totalAmountOldStockWithoutGST
     * @return {number}
     */
	public get totalAmountOldStockWithoutGST(): number {
		return this._totalAmountOldStockWithoutGST;
	}

    /**
     * Setter totalAmountOldStockWithoutGST
     * @param {number} value
     */
	public set totalAmountOldStockWithoutGST(value: number) {
		this._totalAmountOldStockWithoutGST = value;
	}

    /**
     * Getter averageOldStock
     * @return {number}
     */
	public get averageOldStock(): number {
		return this._averageOldStock;
	}

    /**
     * Setter averageOldStock
     * @param {number} value
     */
	public set averageOldStock(value: number) {
		this._averageOldStock = value;
	}

    /**
     * Getter averagePriceWithGST
     * @return {number}
     */
	public get averagePriceWithGST(): number {
		return this._averagePriceWithGST;
	}

    /**
     * Setter averagePriceWithGST
     * @param {number} value
     */
	public set averagePriceWithGST(value: number) {
		this._averagePriceWithGST = value;
	}

    /**
     * Getter averagePriceWithoutGST
     * @return {number}
     */
	public get averagePriceWithoutGST(): number {
		return this._averagePriceWithoutGST;
	}

    /**
     * Setter averagePriceWithoutGST
     * @param {number} value
     */
	public set averagePriceWithoutGST(value: number) {
		this._averagePriceWithoutGST = value;
	}

    /**
     * Getter totalAmountOldStockWithGST
     * @return {number}
     */
	public get totalAmountOldStockWithGST(): number {
		return this._totalAmountOldStockWithGST;
	}

    /**
     * Setter totalAmountOldStockWithGST
     * @param {number} value
     */
	public set totalAmountOldStockWithGST(value: number) {
		this._totalAmountOldStockWithGST = value;
	}

}

export class AvgItemObj {
    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: string;

    @serializeAs('skuId')
    @deserializeAs('skuId')
    private _skuId: string;

    @serializeAs('formattedName')
    @deserializeAs('formattedName')
    private _formattedName: string;

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;


    constructor() { }

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Getter skuId
     * @return {string}
     */
	public get skuId(): string {
		return this._skuId;
	}

    /**
     * Setter skuId
     * @param {string} value
     */
	public set skuId(value: string) {
		this._skuId = value;
	}

    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

}
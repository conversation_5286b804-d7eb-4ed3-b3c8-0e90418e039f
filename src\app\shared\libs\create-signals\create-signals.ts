import { signal } from "@angular/core";

/*
 * Signals - shortcut methods for array
 */
export function createArraySignal<T>(initial: T[] = []) {
    const internal = signal<T[]>([...initial]);

    return {
        get: () => internal(),
        set: (val: T[]) => internal.set([...val]),
        update: (fn: (val: T[]) => T[]) => internal.update(prev => [...fn([...prev])]),
        push: (item: T) => internal.update(prev => [...prev, item]),
        pushAt: (index: number, item: T) => internal.update(prev => {
            const filled = [...prev];
            while (filled.length <= index) filled.push(undefined as any);
            filled[index] = item;

            return filled;
        }),
        removeAt: (index: number) => internal.update(prev => prev.filter((_, i) => i !== index)),
        replaceAt: (index: number, item: T) => internal.update(prev => prev.map((v, i) => (i === index ? item : v))),
        updateAt: (index: number, updater: (item: T) => T) => internal.update(prev => prev.map((item, i) => (i === index ? updater({ ...item }) : item))),
        removeAtKeepUndefined: (index: number) => internal.update(prev => {
            const filled = [...prev];
            if (index < filled.length) {
                filled[index] = undefined as any;
            }
            return filled;
        }),
    };
}

/*
 * Signals - shortcut methods for objects
 */
export function createObjectSignal<T extends Record<string, any>>(initial: T = {} as T) {
    const internal = signal<T>({ ...initial });
    const initialCopy = { ...initial };

    return {
        get: () => internal(),
        set: (val: T) => internal.set({ ...val }),
        update: (fn: (val: T) => T) => internal.update(prev => ({ ...fn({ ...prev }) })),
        setProp: <K extends keyof T>(key: K, value: T[K]) => internal.update(prev => ({ ...prev, [key]: value })),
        removeProp: <K extends keyof T>(key: K) => internal.update(prev => {
            const { [key]: _, ...rest } = prev;
            return rest as T;
        }),
        reset: () => internal.set({ ...initialCopy }),
    };
}
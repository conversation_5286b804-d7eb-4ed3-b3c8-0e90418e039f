@let a = auditTicketService;
<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search1">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="a.onChangeFilter($event, 'search')" [ngModel]="a.paginationRequest.get().searchById" type="text"
                    class="form-control" placeholder="Search by ID">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                @if(a.paginationRequest.get().dateRange) {
                    <i class="th-bold-close-circle cursor-pointer" (click)="a.onClearDateOnly()"></i>
                }   @else {
                    <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                }
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [ngModel]="a.paginationRequest.get().dateRange"
                    (ngModelChange)="a.onChangeFilter($event, 'date')" [showCustomRangeLabel]="true"
                    [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                    [showClearButton]="false" [placeholder]="'Ticket Date'" [autoApply]="true" [showRangeLabelOnInput]="true"
                    startKey="start" endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
            <ng-select (ngModelChange)="a.onChangeFilter($event, 'ticketSubject')" placeholder="Ticket Subject"
                [multiple]="false" [clearable]="true" [items]="a.dropdown()?.subjectNames" bindLabel="value" bindValue="id"
                [ngModel]="a.paginationRequest.get().ticketSubject">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
            <ng-select (ngModelChange)="a.onChangeFilter($event, 'assignTo')" placeholder="Assign to" [multiple]="false"
                [clearable]="true" [items]="a.dropdown()?.assignToUsers" bindLabel="value" bindValue="id"
                [ngModel]="a.paginationRequest.get().assignTo">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
            <ng-select (ngModelChange)="a.onChangeFilter($event, 'ticketType')" placeholder="Ticket Type" [multiple]="false"
                [clearable]="true" [items]="a.dropdown()?.ticketTypes" bindValue="key" 
                [ngModel]="a.paginationRequest.get().ticketType">
                <ng-template ng-option-tmp let-item="item">
                    <span class="fs-13" [title]="item.value">{{ item.value }} ({{item.key}})</span>
                </ng-template>
                <ng-template ng-label-tmp let-item="item">
                    <span class="fs-13" [title]="item.value">{{ item.value }} ({{item.key}})</span>
                </ng-template>
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
            <ng-select (ngModelChange)="a.onChangeFilter($event, 'subjectStatus')" placeholder="Subject Status"
                [multiple]="false" [clearable]="true" [items]="a.dropdown()?.ticketStatuses" bindLabel="value" bindValue="key"
                [ngModel]="a.paginationRequest.get().subjectStatus">
            </ng-select>
        </div>
        <button (click)="a.onClear()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
        <div class="dropdown export-dropdown">
            <button [disabled]="a.isAuditTicketListEmpty()" type="button"
                class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                Export
            </button>
            <ul class="dropdown-menu">
                <li><a (click)="a.exportReport()" class="dropdown-item">Excel</a></li>
            </ul>
        </div>
        <button [disabled]="a.isAuditTicketListEmpty()" (click)="a.onExpandAll()" class="btn btn-outline-white btn-sm">
            <i
                [ngClass]="{'th th-outline-arrow-down-1': a.flagForExpandAll(), 'th th-outline-arrow-right-3': !a.flagForExpandAll()}"></i>
        </button>
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="accordion accordion-group" id="accordionSingle">
            @for(item of a.auditTicketList.get(); let i = $index; track $index) {
            <div class="accordion-item">
                <h2 class="accordion-header" id="accordionSingleHeadingOne">
                    <button class="accordion-button" type="button" (click)="a.onExpand(i)"
                        [ngClass]="{'collapsed': !item.isExpand}">
                        <div class="accordion-header-left">
                            <ul class="accordion-header-item">
                                <li>{{item.branchName}}</li>
                            </ul>
                        </div>
                        <div class="accordion-header-right">

                        </div>
                    </button>
                </h2>
                @if(item.isExpand) {
                <div class="accordion-collapse" #collapse="ngbCollapse" [ngbCollapse]="!item.isExpand"
                    [ngClass]="{'collapse show': !item.isExpand}">
                    <div class="accordion-body tbl-accordion-body p-0">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky tbl-collapse">
                                <thead class="border-less">
                                    <tr>
                                        <th class="d-flex align-items-center gap-2">
                                            <!-- <div class="checkbox checkbox-primary checkbox-small">
                                                <input type="checkbox" id="tbl-checkbox2"
                                                    class="material-inputs filled-in" />
                                                <label for="tbl-checkbox2"></label>
                                            </div> -->
                                            Ticket ID
                                        </th>
                                        <th>Warehouse</th>
                                        <th>Ticket Subject</th>
                                        <th>Date & Time</th>
                                        <th>Ticket Type</th>
                                        @if(a.selectedTab() === a.enumForTabs.NEW) {
                                            <th>Subject Status</th>
                                        }
                                        <th>Created By</th>
                                        <th>Assign to</th>
                                        <th>Note</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(child of item.tickets; let j = $index; track $index) {
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a class="cursor-pointer" (click)="a.redirectToDetails(child.id, item.branchId)">
                                                <div class="tbl-user-checkbox-srno">
                                                    #{{child.ticketId}}
                                                </div>
                                            </a>
                                        </td>
                                        <td>{{child.warehouseName || '-'}}</td>
                                        <td>
                                            <div>{{child.ticketSubject}}</div>
                                        </td>
                                        <td>{{child.createdDate || '-'}}</td>
                                        <td class="text-primary"> {{a.enumForTicketTypeLabel[child.ticketType]}}</td>
                                        @if(a.selectedTab() === a.enumForTabs.NEW) {
                                            <td>
                                                <div class="badge"
                                                    [ngClass]="{'badge-success-light': child.status === a.enumForTicketStatus.COMPLETED,
                                                                'badge-warning-light': child.status === a.enumForTicketStatus.IN_PROGRESS,
                                                                'badge-info-light': child.status === a.enumForTicketStatus.PENDING}">
                                                    {{child.status}}
                                                </div>
                                            </td>
                                        }
                                        <td>{{child.createdBy}}</td>
                                        <td>
                                            @if(child?.assignTo !== 'Unassigned') {
                                                <span class="w-100 d-block">{{child.assignTo}}</span>
                                                <span class="w-100 d-block fs-10 fw-600">{{child.assignDay}}</span>
                                            } @else {
                                                <span class="w-100 d-block text-secondary-two">Not Assigned</span>
                                            }
                                        </td>
                                        <td class="tbl-description">
                                            <div [title]="child.note || ''">
                                                {{child.note || '-'}}
                                            </div>
                                        </td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                @if(a.selectedTab() === a.enumForTabs.NEW && child.status === a.enumForTicketStatus.PENDING && child.ticketType === a.enumForTicketType.SC && child.ticketActionType !== 'Auto' ) {
                                                    <button [pageAccess]="{page: a.utilsService.enumForPage.AUDIT_TICKET, action: a.utilsService.enumForPage.EDIT_AUDIT_TICKET}"
                                                        [routerLink]="['/users/audit-tickets/edit-audit-tickets', child.id]" class="btn btn-xs btn-light-white btn-icon"
                                                        ngbTooltip="Edit" placement="left" container="body" triggers="hover">
                                                        <i class="th th-outline-edit"></i>
                                                    </button>
                                                }
                                                @if(a.selectedTab() === a.enumForTabs.NEW && child.status === a.enumForTicketStatus.COMPLETED && !child.childTicketId) {
                                                    <button (click)="a.openResolveTicketModal(child, successAuditTicket)" class="btn btn-xs btn-light-success btn-icon"
                                                        ngbTooltip="Resolve" placement="left" container="body" triggers="hover">
                                                        <i class="th th-outline-tick-circle"></i>
                                                    </button>
                                                }
                                                @if(a.selectedTab() === a.enumForTabs.NEW && (child.status === a.enumForTicketStatus.PENDING || (child.status === a.enumForTicketStatus.COMPLETED && (child.ticketType === a.enumForTicketType.QC_CHECK || child.ticketType === a.enumForTicketType.REAL_PHOTO)))) {
                                                    <button (click)="a.openTicketModal(child, reAssignAuditTicket, true)" class="btn btn-xs btn-light-warning btn-icon"
                                                        ngbTooltip="Assign User" placement="left" container="body" triggers="hover">
                                                        <i class="th th-outline-user-add"></i>
                                                    </button>
                                                }
                                                @if(child.status === a.enumForTicketStatus.PENDING && child.ticketActionType !== 'Auto'  ) {
                                                <button
                                                    [pageAccess]="{page: a.utilsService.enumForPage.AUDIT_TICKET, action: a.utilsService.enumForPage.DELETE_AUDIT_TICKET}"
                                                    (click)="a.openTicketModal(child, deleteAuditTicket)" class="btn btn-xs btn-light-danger btn-icon"
                                                    ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                                }
                                                <div>
                                                    <app-attachment-download-dropdown [fileList]="(child.documents || [])" [isDelete]="false" />
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                }
            </div>
            } @empty {
                <app-no-record />
            }
        </div>
    </div>
</div>
<!-- <div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="a.addPageSizeData($event)" (pageNumber)="a.pageNumber($event)"
        [page]="a.paginationRequest.get().pageNumber" [pageSize]="a.paginationRequest.get().pageSize"
        [totalData]="a.paginationRequest.get().totalData">
    </app-pagination>
</div> -->

<!-- Re-Assign Audit Ticket -->
<ng-template #reAssignAuditTicket let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Re-Assign {{a.enumForTicketTypeLabel[a.ticketObj.get()?.ticketType]}} ticket (#{{a.ticketObj.get()?.ticketId}})</h5>
                <button type="button" class="btn-close" (click)="modal.close()"><i class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">User</label>
                            <ng-select [formControl]="a.userControl" class="" placeholder="Select User"
                                [multiple]="false" [clearable]="false" [items]="a.reAssignUserDropdown" bindLabel="name" bindValue="id">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="a.userControl.hasError('required') && a.userControl.touched">
                                {{utilsService.validationService.USER_REQ}}
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="a.onReAssignTicket(modal)" [disabled]="a.userControl.invalid" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<!-- Delete Ticket Modal -->
<ng-template #deleteAuditTicket let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()"><i class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>#{{a.ticketObj.get()?.ticketId}}</b> <br/> {{a.enumForTicketTypeLabel[a.ticketObj.get()?.ticketType]}} Ticket.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="a.onTicketChanges(modal, 'delete')" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<!-- Success Modal -->
<ng-template #successAuditTicket let-modal>
    <div class="modal-theme modal-confirmation modal-approve">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()"><i class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-tick-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to resolve the {{a.enumForTicketTypeLabel[a.ticketObj.get()?.ticketType]}} Ticket<br /> with the ID
                            <b>#{{a.ticketObj.get()?.ticketId}}</b></p>
                        </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="a.onTicketChanges(modal, 'approve')" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
                        Confirm</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
import { formatDate } from "@angular/common";
import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { Branch } from "@modal/Branch";
import { UtilsService } from "@service/utils.service";
import { Deserialize, Serialize } from "cerialize";
import dayjs from "dayjs";
import moment from "moment";
import { DaterangepickerDirective } from "ngx-daterangepicker-material";

@Component({
  selector: 'app-new-branch',
  templateUrl: './new-branch.component.html',
  styleUrls: ['./new-branch.component.css']
})
export class NewBranchComponent implements OnInit {

  @ViewChild(DaterangepickerDirective, { static: false }) pickerDirective: DaterangepickerDirective;
  @ViewChild('doc') doc: ElementRef;
  branchId: number;
  branchForm: FormGroup;
  branchObj = new Branch();

  dropdown: any;
  city: any[] = [];
  state: any[] = [];

  maxDate: any = formatDate(new Date(2050, 11, 31), 'yyyy-MM-dd', 'en');
  minDate: any = formatDate(new Date(new Date().getFullYear() - 1, 0, 1), 'yyyy-MM-dd', 'en');

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private route: ActivatedRoute) {
  }

  ngOnInit() {

    this.branchFormGroup();
    this.addGSTIN();
    this.branchId = Number(this.route.snapshot.paramMap.get('id'));
    if(!this.branchId) {
      this.branchObj.isActive = true;
      this.branchForm.get('fy').patchValue(null);
    }
    setTimeout(() => {
      this.getBranchByID();
    }, 50);

    document.getElementById('f').focus();

    //
    // this.maxDate = dayjs().year(2050);
    // this.minDate = dayjs()
    //
  }

  branchFormGroup() {
    this.branchForm = this.fb.group({
      branchName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      shortCode: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      userId: [null, Validators.compose([Validators.required])],
      branchEmail: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      branchPhone: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isActive: [true, Validators.compose([Validators.required])],
      isMainBranch: [false],
      isAllowLocalPurchase: [false],
      isAllowImportPurchase: [false],
      isConsiderGSTAvgPrice: [false],
      zipCode: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_NUMBER)])],
      address: [null],
      landmark: [null],
      locationLink: [null],
      companyType: [null, Validators.compose([Validators.required])],
      idOfState: [null],
      idOfCountry: [null],
      idOfCity: [null],
      idOfCurrency: [null],
      fy: [null],
      gstin: this.fb.array([]),
      bankGroup: [null]

    });
  }

  openDatepicker() {
    if(!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  get gstin() {
    return (this.branchForm.get('gstin') as FormArray);
  }

  gstinNum(): FormGroup {
    return this.fb.group({
      number: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
    });
  }

  addGSTIN() {
    this.gstin.push(this.gstinNum());
  }

  removeGSTIN(index: number) {
    if (this.gstin.length > 1) {
      this.gstin.removeAt(index);
      this.branchObj.gstNO.splice(index, 1);
    }
  }

  getBranchByID() {
    this.dropdown = null;
    let API = this.branchId ? this.utilsService.serverVariableService.BRANCH_DATA_BY_ID + `?id=${this.branchId}` : this.utilsService.serverVariableService.BRANCH_DATA_BY_ID;
    this.utilsService.postMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.currencies = this.dropdown.currencies ? this.utilsService.transformDropdownItems(this.dropdown.currencies) : [];
        this.dropdown.bankGroups = this.dropdown.bankGroups ? this.utilsService.transformDropdownItems(this.dropdown.bankGroups) : [];
        this.dropdown.country = this.dropdown.country ? this.utilsService.transformDropdownItems(this.dropdown.country) : [];
        this.dropdown.state = this.dropdown.state ? this.utilsService.transformDropdownItems(this.dropdown.state) : [];
        this.dropdown.city = this.dropdown.city ? this.utilsService.transformDropdownItems(this.dropdown.city) : [];
        this.dropdown.users = this.dropdown.users ? this.utilsService.transformDropdownItems(this.dropdown.users) : [];

        if (response.branch) {
          this.branchObj = Deserialize(response.branch, Branch);
          this.branchObj.branchDoc = Deserialize(response.documents)
          this.branchObj.companyType = response.branch.companyType.value
          this.branchObj.idOfCountry = response.branch?.countryMaster?.id
          this.branchObj.idOfState = response.branch?.stateMaster?.id
          this.branchObj.idOfCity = response.branch?.cityMaster?.id
          this.branchObj.idOfCurrency = response.branch?.currency?.id
          this.branchObj.idOfUser = response.branch?.users?.id;

          // from to date
          this.branchObj.date = this.branchObj.date ?? {};
          this.branchObj.date.start = moment(this.branchObj.yearFromDate).toDate();
          this.branchObj.date.end = moment(this.branchObj.yearToDate).toDate();
          //
          // this.branchObj.gstNO = this.branchObj.gstNos.split(",");
          // const gstin = (this.branchForm.get('gstin') as FormArray);
          // this.branchObj.gstNO?.map(v => {
          //   if (v) {
          //     gstin.push(this.fb.group({
          //       number: [v, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
          //     }));
          //   }
          // })
          //
          const availableStates = this.dropdown.state.filter(state => state.country?.id === this.branchObj.idOfCountry);
          this.state = availableStates ? this.utilsService.transformDropdownItems(availableStates) : [];

          const availableCities = this.dropdown.city.filter(city => city.state?.id === this.branchObj.idOfState);
          this.city = availableCities ? this.utilsService.transformDropdownItems(availableCities) : [];

          this.dropdown.country = this.utilsService.filterIsActive(this.dropdown?.country, this.branchObj.idOfCountry ? this.branchObj.idOfCountry : null);
          this.dropdown.state = this.utilsService.filterIsActive(this.dropdown?.state, this.branchObj.idOfState ? this.branchObj.idOfState : null);
          this.dropdown.city = this.utilsService.filterIsActive(this.dropdown?.city, this.branchObj.idOfCity ? this.branchObj.idOfCity : null);
          this.dropdown.users = this.utilsService.filterIsActive(this.dropdown?.users, this.branchObj.idOfUser ? this.branchObj.idOfUser : null);
        }


        // disable if ON on edit main branch switch
        if (this.branchObj.isMainBranch) {
          this.branchForm.controls['isActive'].disable();
          this.branchForm.controls['isMainBranch'].disable();
        } else {
          this.branchForm.controls['isActive'].enable();
          this.branchForm.controls['isMainBranch'].enable();
        }
        this.branchForm.controls['isMainBranch'].updateValueAndValidity();
        this.branchForm.controls['isActive'].updateValueAndValidity();

        this.dropdown.country = this.utilsService.filterIsActive(this.dropdown?.country, this.branchObj.idOfCountry ? this.branchObj.idOfCountry : null);
        this.dropdown.state = this.utilsService.filterIsActive(this.dropdown?.state, this.branchObj.idOfState ? this.branchObj.idOfState : null);
        this.dropdown.city = this.utilsService.filterIsActive(this.dropdown?.city, this.branchObj.idOfCity ? this.branchObj.idOfCity : null);
        this.dropdown.currencies = this.utilsService.filterIsActive(this.dropdown?.currencies, this.branchObj.idOfCurrency ? this.branchObj.idOfCurrency : null);

        setTimeout(() => {
          if (response.branch) {
            this.state = this.utilsService.filterIsActive(this.state, this.branchObj.idOfState ? this.branchObj.idOfState : null);
            this.city = this.utilsService.filterIsActive(this.city, this.branchObj.idOfCity ? this.branchObj.idOfCity : null);
            this.dropdown.bankGroups = this.utilsService.filterIsActiveMultiple(this.dropdown?.bankGroups, this.branchObj.idsOfBankGroup ? this.branchObj.idsOfBankGroup : null);
          }
          if (!this.branchId) {
            this.dropdown.bankGroups = this.utilsService.filterIsActiveMultiple(this.dropdown?.bankGroups, null);

            const objIndia = this.dropdown.country?.find(v => v.name.toLowerCase() === ('India').toLowerCase())
            this.branchObj.idOfCountry = objIndia ? objIndia.id : null;
            this.onChangeCountry()
          }
        }, 100);


      }
    })
  }

  onSaveNewBranch() {

    const formData = new FormData();

    if (this.branchForm.invalid) {
      this.branchForm.markAllAsTouched();
      return;
    }

    // if(this.utilsService.isEmptyObjectOrNullUndefined(this.branchObj.date)) {
    //   return;
    // }

    this.branchObj.branchDoc.map(v => {
      if(v.file) {
        formData.append('branchDocs', v.file)
      }
    })

    this.branchObj.gstNos = this.branchObj.gstNO.toString()
    if(this.branchObj.date) {
      this.branchObj.yearFromDate = dayjs(this.branchObj.date?.start).format('YYYY-MM-DD');
    }
    if(this.branchObj.date) {
      this.branchObj.yearToDate = dayjs(this.branchObj.date?.end).format('YYYY-MM-DD');
    }
    
    let param = this.utilsService.trimObjectValues(Serialize(this.branchObj));

    formData.set('saveBranchInfo', JSON.stringify(param));
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.BRANCH_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.utilsService.redirectTo('/users/branch-management/')
      }
    })
  }

  //country, state, city fn

  onClearCountry() {
    this.branchObj.idOfCountry = null;
  }

  onClearState() {
    this.branchObj.idOfState = null;
  }

  onClearCity() {
    this.branchObj.idOfCity = null;
  }

  onChangeCountry() {
    this.city = [];
    this.state = [];
    this.branchObj.idOfState = null;
    this.branchObj.idOfCity = null;
    this.getStates();
  }

  getStates() {
    if (!this.branchObj.idOfCountry) {
      return;
    }
    this.state = (this.dropdown.state || []).filter(a => a.country?.id == this.branchObj.idOfCountry);
  }

  onChangeState() {
    this.city = [];
    this.branchObj.idOfCity = null;
    this.getCities();
  }

  getCities() {
    if (!this.branchObj.idOfState) {
      return;
    }
    this.city = (this.dropdown.city || []).filter(a => a.state?.id == this.branchObj.idOfState);
  }

  //attachments 

  onSelectAttachments(event): void {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = `image-${Date.now()}.${item.type.split('/')[1]}`;
          const fileFromBlob = new File([blob], fileName, { type: item.type });
          dataTransfer.items.add(fileFromBlob);
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpeg', 'png', 'jpg', 'jfif', 'csv', 'xlsx', 'webp', 'avif', 'xlss', 'pdf', 'xls'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE);
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = {
              id: null,
              originalname: fileUrl,
              fileName: file.name,
              file,
              isFlag: false,
            };
            this.branchObj?.branchDoc.push(fileData);

            if (this.doc?.nativeElement) {
              this.doc.nativeElement.value = '';
            }
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION);
        }
      });
    }
  }

  removeAttachment(i: number, file) {
    this.doc.nativeElement.value = "";
    this.branchObj.branchDoc.splice(i, 1)
    if (file.id) {
      this.branchObj.deletedDocsID.push(file.id)
    }
  }

  openLink(link, newUpload: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  onChangeMainBranch() {
    if (this.branchObj.isMainBranch) {
      this.branchObj.isActive = true;
      this.branchForm.controls['isActive'].disable();
      this.branchForm.controls['isActive'].updateValueAndValidity();
    }
    else {
      this.branchForm.controls['isActive'].enable();
      this.branchForm.controls['isActive'].updateValueAndValidity();
    }
  }

  onClearBG() {
    this.branchObj.idsOfBankGroup = null;
  }

  onClearCurrency() {
    this.branchObj.idOfCurrency = null;
  }
  onClearManager() {
    this.branchObj.idOfUser = null;
  }
}

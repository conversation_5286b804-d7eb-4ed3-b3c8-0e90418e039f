@let service = salesService;
<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="service.onChangeFilter($event, 'search')"
                    [ngModel]="service.paginationRequest.get().searchText" type="text" class="form-control"
                    placeholder="Search by item / SKU">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                @if(service.paginationRequest.get().dateRange) {
                    <i class="th-bold-close-circle cursor-pointer" (click)="service.onClearDateOnly()"></i>
                }   @else {
                    <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                }
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [ngModel]="service.paginationRequest.get().dateRange" (ngModelChange)="service.onChangeFilter($event, 'date')"
                    [showCustomRangeLabel]="true" [alwaysShowCalendars]="true" [ranges]="utilsService.ranges"
                    [linkedCalendars]="false" [showClearButton]="false" placeholder="Inquiry Date" [autoApply]="true"
                    [showRangeLabelOnInput]="true" startKey="start" endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect filter-item-all">
            <ng-select (ngModelChange)="service.onChangeFilter($event, 'customerId')"
                [ngModel]="service.paginationRequest.get().customerId" placeholder="Customer" [multiple]="false"
                [clearable]="true" [items]="service.dropdown.customerLead" bindLabel="label" bindValue="value">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect filter-search1">
            <ng-select (ngModelChange)="service.onChangeFilter($event, 'inquiryType')"
                [ngModel]="service.paginationRequest.get().inquiryType" placeholder="Inq. Type" [multiple]="false"
                [clearable]="true" [items]="service.dropdown.inquiryType" bindLabel="label" bindValue="value">
            </ng-select>
        </div>
        <button (click)="service.onClear()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
        <div class="record-status">
            <ul class="list-unstyled">
                <li class="rs-urgent-inquiry ">
                    <span></span> Urgent Inquiry
                </li>
                <li class="rs-nit ">
                    <span></span> NIT
                </li>
            </ul>
        </div>
        <div class="dropdown export-dropdown">
            <button [disabled]="service.isInquiryListEmpty()" type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                aria-expanded="false">
                Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" (click)="service.exportReport()">Excel</a></li>
            </ul>
        </div>
    </div>
</div>
@let priceAndFollowRole = salesOrderService.utilsService.checkPageAccess([salesOrderService.utilsService.enumForPage.APPROVE_SALE_PRICE_FOLLOWUP_DATE]);
<div class="card card-theme card-table-sticky-inq">
    <div class="card-body p-0">
        <div class="accordion accordion-group" id="accordionSingle">
            @for(item of service.inquiryList.get(); let i = $index; track $index) {
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button" type="button" (click)="service.onExpand(i, item)" [ngClass]="{'collapsed': !item.isExpand}">
                        <div class="accordion-header-left">
                            <ul class="accordion-header-item text-nowrap">
                                <li class="ng-expense"><span class="fs-13">Customer Name:  </span> <b>{{item.displayName}}</b> </li>
                                <li>{{item.countryExtension}} {{item.phone}}</li>
                            </ul>
                        </div>
                        <div class="accordion-header-right align-items-center">
                            <button
                                [pageAccess]="{page: salesOrderService.utilsService.enumForPage.INQUIRY, action: salesOrderService.utilsService.enumForPage.DELETE_INQUIRY}"
                                ngbTooltip="Delete" placement="left" container="body" triggers="hover"
                                (click)="service.openDeleteSalesInqModal(item, deleteSalesInquiryModal); $event.stopPropagation()"
                                class="btn btn-xs btn-light-danger btn-icon">
                                <i class="th th-outline-trash"></i>
                            </button>
                            <button
                                [pageAccess]="{page: salesOrderService.utilsService.enumForPage.SALES_ORDERS, action: salesOrderService.utilsService.enumForPage.ADD_SO}"
                                ngbTooltip="Convert To SO" placement="left" container="body" triggers="hover"
                                (click)="service.openDeleteSalesInqModal(item, convertInquiryToSo); $event.stopPropagation()"
                                class="btn btn-sm btn-outline-white btn-icon-text text-primary">
                                <i class="th th-outline-trash">Convert To SO</i>
                            </button>
                        </div>
                    </button>
                </h2>
                @if(item.isExpand) {
                    <div class="accordion-collapse" #collapse="ngbCollapse" [ngbCollapse]="!item.isExpand" [ngClass]="{'collapse show': !item.isExpand}">
                        <div class="accordion-body tbl-accordion-body ">
                            <div class="table-responsive">
                                <table class="table-theme table-hover table-hover table table-bordered table-sticky">
                                    <thead class="border-less">
                                        <tr>
                                            <th>Item Details</th>
                                            <th>Inq. Country</th>
                                            <th>Order Unit</th>
                                            <th>Carton</th>
                                            <th>PCS/Carton</th>
                                            <th>Total Qty</th>
                                            <th>Loose PCS</th>
                                            <th class="text-center">Total Inquiry <br/> (PCS)</th>
                                            <!-- <th class="text-center">Total Inquiry <br/> (Carton)</th> -->
                                            <th>Sales <br/> Executive <br/> Offered Price</th>
                                            <th>Sales <br/> Manager <br/> Price</th>
                                            <th>Customer <br/> Price</th>
                                            <th class="text-center">Last 3 Sale <br/> (Price | Qty | Date)</th>
                                            <th>Average Price</th>
                                            <!-- <th>Supplier <br/> Name</th> -->
                                            <th>Inq. Date & <br/> Time</th>
                                            <th>Follow-up <br/> Date & Time</th>
                                            <th>NIT Reason</th>
                                            <th>Note</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for(child of item.items; let j = $index; track child.id) {
                                            @let inquiry = child.inquiryType?.value;
                                            @let enumInq = salesService.enumForInquiryType;
                                            @let isLoose = child.enumForOrderUnit?.value === salesService.enumForOrderUnit.LOOSE;
                                            <tr [ngClass]="{'tbl-bg-primary': inquiry === enumInq.URGENT_INQUIRY, 
                                                            'tbl-bg-secondary': inquiry === enumInq.NIT}">
                                                @if(child?.item){
                                                <td class="tbl-user">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <div class="product-checkbox checkbox checkbox-primary checkbox-small">
                                                            <input type="checkbox" [ngModel]="child?.item?.isSelected"
                                                                id="checkbox2-grid-{{i}}{{j}}" class="material-inputs filled-in">
                                                            <label for="checkbox2-grid-{{i}}{{j}}"></label>
                                                        </div>
                                                        <div class="tbl-user-wrapper">
                                                            <div class="tbl-user-image">
                                                                <img [src]="utilsService.imgPath + child?.item?.formattedName" alt="valamji">
                                                            </div>
                                                            <div class="tbl-user-text-action">
                                                                <div class="tbl-user-text">
                                                                    <p>{{child?.item?.displayName}}</p>
                                                                    <span>#{{child?.item?.skuId}}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                } @else {
                                                    <td class="tbl-user">
                                                        <div class="tbl-user-checkbox-srno">
                                                            <div class="tbl-user-wrapper">
                                                                <div class="tbl-user-text-action">
                                                                    <div class="tbl-user-text">
                                                                        <p>{{child?.inquiryItemName}}</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                }
                                                <td>{{child.enumsItemsBelong}}</td>
                                                <td>{{child.enumForOrderUnit?.label}}</td>
                                                <td>
                                                    @if(!isLoose) {
                                                    <b>{{child.inquiryQuantity || 0}}</b>
                                                    } @else {
                                                    <span>-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if(!isLoose) {
                                                    {{child.piecePerCarton || '-'}}
                                                    } @else {
                                                    <span>-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if(!isLoose) {
                                                    {{child.totalPiece || 0}}
                                                    } @else {
                                                    <span>-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if(isLoose) {
                                                    <b>{{child.inquiryQuantity || 0}}</b>
                                                    } @else {
                                                    <span>-</span>
                                                    }
                                                </td>
                                                <td>{{child.totalPiece || '-    '}}</td>
                                                <td>{{child.salesExecutiveOfferedPrice || 0}}</td>
                                                <td>
                                                    <div class="form-group form-group-200">
                                                        <input [disabled]="!priceAndFollowRole"
                                                            type="text" class="form-control" placeholder="Enter" mask="separator.2" thousandSeparator=""
                                                            [ngModel]="child.salesManagerPrice" [maxLength]="utilsService.validationService.MAX_10"
                                                            (input)="service.onUpdateSalesManagerPrice(child, i, j, $event, false)">
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center justify-content-between gap-1">
                                                        <span>{{child.customerRequestedPrice || 0}}</span>
                                                        <button *ngIf="priceAndFollowRole"
                                                            [copyText]="child.customerRequestedPrice.toString()" class="btn btn-xs btn-icon btn-transparent"
                                                            (click)="service.onUpdateSalesManagerPrice(child, i, j, $event, true)"><i
                                                                class="th th-outline-copy"></i></button>
                                                    </div>
                                                </td>
                                                <td>
                                                    <!-- <span | 10 | 12/12/25span class="d-block w-100"></span> -->
                                                    <div class="dropdown-branch-marka-sm dropdown-with-tables dropdown-no-arrow" ngbDropdown placement="auto"
                                                        #dropdown="ngbDropdown" container="body">
                                                        @if(child.lastSalesInfo?.length > 0) {
                                                            <button class="btn btn-link" type="button" ngbDropdownToggle>
                                                                Last 3 Sales
                                                            </button>
                                                        } @else {
                                                            <span>-</span>
                                                        }
                                                        <div ngbDropdownMenu class="dropdown-last-3" dropdownClass="dropdown-last-3" *ngIf="dropdown.isOpen">
                                                            <app-last-three [lastThreeInfo]="child.lastSalesInfo" [dropdown]="dropdown" />
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{child?.item?.averagePriceWithoutGST ? (child.item.averagePriceWithoutGST | currency:'INR') : '-'}}</td>
                                                <!-- <td></td> -->
                                                <td>{{child.itemInquiryDate ? (child.itemInquiryDate | date:'dd/MM/yyyy h:mm a') : '-'}}</td>
                                                @let dateAfterInquiry = moment(child.itemInquiryDate).format('DD-MM-YYYY HH:mm');
                                                <td>
                                                    <div class="form-group form-group-300">
                                                        <app-date-time-picker [disabled]="!priceAndFollowRole"
                                                            [minDate]="dateAfterInquiry" [timer]="true" timeFormat="12-hour" outputFormat="moment"
                                                            displayFormat="DD/MM/YYYY hh:mm A" placeholder="" [ngModel]="child.followUpDateTime"
                                                            (ngModelChange)="service.onUpdateFollowUpDateTime(child, i, j, $event)" [showClearButton]="true"/>
                                                    </div>
                                                </td>
                                                <td>{{child.reasonName ? child.reasonName : '-'}}</td>
                                                <td>
                                                    <div class="tbl-po-notes">
                                                        {{child.note || '-'}}
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                        @if(item?.items?.length === 0) {
                                            <tr>
                                                <td colspan="20" class="text-center">
                                                    <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }
            </div>
            }
        </div>
        @if(service.isInquiryListEmpty()) {
            <app-no-record />
        }
    </div>
</div>
<div class='bottombar-wrapper bottom-fixed-2' *ngIf="priceAndFollowRole">
    <div class='bottombar-container'>
        <div class='bottombar-left'>
            <button [disabled]="service.isInquiryListEmpty() || service.inquiryAllCollappsed()" (click)="service.onSubmitResetSalesPriceChanges(false)"
                type="button" class="btn btn-primary btn-icon-text btn-sm"> <i class="th th-outline-tick-circle"></i>Submit
                Changes</button>
            <button [disabled]="service.isInquiryListEmpty()" (click)="service.onSubmitResetSalesPriceChanges(true)"
                type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
                    class="th th-outline-close-circle"></i>Clear</button>
        </div>
    </div>
</div>
<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="service.addPageSizeData($event)" (pageNumber)="service.pageNumber($event)"
        [page]="service.paginationRequest.get().pageNo" [pageSize]="service.paginationRequest.get().pageSize"
        [totalData]="service.paginationRequest.get().totalData">
    </app-pagination>
</div>

<!-- Delete Sales Inquiry Modal -->
<ng-template #deleteSalesInquiryModal let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete Inquiry for <b>{{salesOrderService.inquiryListObj.get().displayName}}</b> Customer.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="salesOrderService.onDeleteSalesInq(modal)" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #convertInquiryToSo let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Convert Inquiry to Sales Order for <b>{{salesOrderService.inquiryListObj.get().displayName}}</b> Customer.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="salesOrderService.onCovertInquiryToSo(modal)" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i> Yes, Create</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
export interface StockShiftingList {
    id: number,
    transferId: string,
    warehouseName: string,
    shiftingDate: string,
    lastModifiedDate: string,
    lastModifiedBy: string,
    createdBy: string,
    status: string,
    note: string,
    reasonName: string,
    isExpand: boolean,
    loadedChild?: boolean,
    items: StockShiftingListItems[]
}

export interface StockShiftingListItems {
    assoItemId: number,
    fromLocation: string,
    fromLocationId: number,
    fromLocationType: string,
    itemId: number,
    itemName: string,
    qty: number,
    skuId: string,
    toLocation: string,
    toLocationId: number,
    formattedName: string,
    toLocationType: string,
    marka: string,
    data: StockShiftingListItemsQty[],
    ticketData: StockShiftingTicketStatusData[],
    loadedChild?: boolean,
    cartonQty?: number,
    looseQty?: number,
}

export interface StockShiftingListItemsQty {
    marka: string,
    piecesPerCarton: number,
    qtyCarton: number,
    qtyCartonItem: number,
    qtyLoose: number,
    totalQty: number,
    total: { qtyCarton: number, qtyCartonItem: number, qtyLoose: number, totalQty: number }
}

export interface StockShiftingTicketStatusData {
    id: number,
    status: string,
    warehouseId: string,
    ticketId: string,
    assignDateTime: string,
    assignTo: string,
    warehouseName: string
}
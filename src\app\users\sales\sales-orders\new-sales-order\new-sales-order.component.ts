import { Component, computed, effect, inject, OnInit, signal, TemplateRef } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { EnumForDeliveryTypes } from '@enums/EnumForDeliveryTypes';
import { EnumForOrderUnit } from '@enums/EnumForOrderUnit.enum';
import { createArraySignal, resetValidators, sumArr, disableFieldUtil, createObjectSignal } from '@libs';
import { Attachment, InquiryBranchMarkaModal, InquiryItemDropdown, MarkaBranchData, MarkaStocks, UpcomingQty } from '@modal/SalesInquiry';
import { Customer, SalesOrderDropdowns } from '@modal/SalesOrderRequired';
import { UtilsService } from '@service/utils.service';
import moment from 'moment';
import { map, Subject, takeUntil, tap } from 'rxjs';
import { MarkaWiseQtyList, SalesOrder, SalesOrderItemList, SaveResMarkaWise, SaveResUpcomingQty } from '@modal/SalesOrder';
import { EnumForSalesOrderSaveStatus } from '@enums/EnumForSalesOrderSaveStatus';
import { ActivatedRoute } from '@angular/router';
import { Serialize } from 'cerialize';
import { UPCOMING_SO_STOCK } from "src/app/shared/constants/constant";
import { NgbModalService } from '@service/ngb-modal.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { SalesOrderService } from '../sales-order.service';

const TO_RAISE_PO = 'TO_RAISE_PO'

@Component({
  selector: 'app-new-sales-order',
  templateUrl: './new-sales-order.component.html',
  styleUrls: ['./new-sales-order.component.scss']
})
export class NewSalesOrderComponent implements OnInit {

  private modalService = inject(NgbModalService);
  utilsService = inject(UtilsService)
  salesOrderService = inject(SalesOrderService);
  moment = moment;

  enumForDeliveryTypes = EnumForDeliveryTypes
  enumForOrderUnit = EnumForOrderUnit
  enumForSalesOrderSaveStatus = EnumForSalesOrderSaveStatus

  isRouteFromInquiry = signal(false);
  customerId = signal<number>(null);
  inquiryId = signal<number>(null);
  salesOrderId = signal<number>(null);

  allSalesOrderDropdowns: SalesOrderDropdowns = {
    bankGroups: [],
    courierCompany: [],
    customers: [],
    deliveryPerson: [],
    item: [],
    orderUnit: [],
    packingTypes: [],
    paymentTerms: [],
    paymentTypes: [],
    shippingAddress: [],
    supplierTypes: [],
    travellers: [],
    transportBranches: [],
    filteredTransportBranches: [],
    transporters: [],
    waitForPaymentOptions: [],
    gstAddress: [],
    deliveryTypes: [],
    warehouses: [],
    countryExtensions: [],
    filteredWaitForPaymentOptions: [],
  }

  allMarkaBranchData: MarkaBranchData = {
    activeTab: null,
    markaDropDown: null,
    upcomingQty: [],
    warehouse: [],
    upcomingCartonQty: null,
    totalUpcomingQty: null,
    allBranchSumTotalInput: [],
  }

  sumTotals = {
    totalQuantity: 0,
    totalAmount: 0,
    gstPer: 0,
    totalAmountWithGst: 0,
    gstAmountTotal: 0,
    grandTotal: 0,
    totalFinalAmt: 0
  }

  deleteItemKeys = {
    deleteDocumentIdList: [] as number[],
    deletedOrderItemIdList: [] as number[],
  }
  
  saveResMarkaWise: SaveResMarkaWise[] = [];
  activeTabQtyTab: number;

  customerDetails = createObjectSignal<Customer>(null);
  form: FormGroup;
  selectedItemIndex = signal<number>(null);

  fromTab = signal<string>('')
  tabIsDraft = computed(() => this.fromTab() === this.enumForSalesOrderSaveStatus.DRAFT)
  tabIsSOCreated = computed(() => this.fromTab() === this.enumForSalesOrderSaveStatus.CREATED)
  tabIsHold = computed(() => this.fromTab() === this.enumForSalesOrderSaveStatus.HOLD)
  
  attachmentsList = createArraySignal<Attachment>([])

  filteredMarkaList: MarkaStocks[] = []
  filteredUpcomingQtyList: UpcomingQty[] = []
  searchText: FormControl;
  warehouseIdFilter: number[] = []

  isUpcomingTab = false;
  isQtyUpcomingTab = false

  private destroy$ = new Subject<void>();

  markaDataCache = new Map<string, any>();
  allBranchSumTotalInputMap = new Map<string, number>();
  itemName = signal<string>('');
  moveToSoBtn = signal(false);
  result = this.salesOrderService.totalSum();
  constructor(private fb: FormBuilder, private route: ActivatedRoute) {
  }

  ngOnInit(): void {

    this.searchText = new FormControl<string>(null);

    this.isRouteFromInquiry.set(this.route.snapshot.params['inquiryId'] && this.route.snapshot.params['customerId'])
    this.customerId.set(this.route.snapshot.params['customerId'] ? +this.route.snapshot.params['customerId'] : null);
    this.inquiryId.set(this.route.snapshot.params['inquiryId'] ? +this.route.snapshot.params['inquiryId'] : null);

    if (Number(this.route.snapshot.paramMap.get('id'))) {
      this.salesOrderId.set(+(this.route.snapshot.paramMap.get('id')))
      this.fromTab.set(this.enumForSalesOrderSaveStatus.CREATED)
    }
    if (Number(this.route.snapshot.paramMap.get('draftId'))) {
      this.salesOrderId.set(+(this.route.snapshot.paramMap.get('draftId')))
      this.fromTab.set(this.enumForSalesOrderSaveStatus.DRAFT)
    }
    if (this.route.snapshot.paramMap.get('convertToSoId')) {
      this.salesOrderId.set(+(this.route.snapshot.paramMap.get('convertToSoId')))
      this.fromTab.set(this.enumForSalesOrderSaveStatus.DRAFT)
      this.moveToSoBtn.set(true)
    }
    console.error(this.result , "result");

    // const nav = this.router.getCurrentNavigation();
    // console.warn(nav?.extras.state, nav?.extras.state?.['items'] || []);

    this.initForm()

    if (this.salesOrderId()) {
      // Case 1: Editing existing sales order
      this.getRequiredData(null, null, this.salesOrderId());
    } else if (this.isRouteFromInquiry()) {
      // Case 2: New sales order coming from inquiry
      this.form.get('customerId')?.setValue(this.customerId());
      this.onChangeCustomer(this.inquiryId());
    } else {
      // Case 3: Fresh new sales order (no inquiry, no salesOrderId)
      this.getRequiredData(null, null, null);
    }
    setTimeout(() => {
      console.error(this.salesOrderService.convertToSOIds);
    }, 1000);

    this.totalCalcs();
    this.onChangeDeliveryType();
    this.onChangeTransporterId();

    this.form.get('isWaitForPayment')?.valueChanges.pipe(
      tap((value: boolean) => {
        const ctrl = this.form.get('waitForPaymentOption');
        value ? ctrl?.addValidators([Validators.required]) : ctrl?.clearValidators();
        ctrl?.updateValueAndValidity();
      }),
      takeUntil(this.destroy$)
    ).subscribe();

    this.form.get('isNeedRepacking')?.valueChanges.pipe(
      tap((value: boolean) => {
        const ctrl = this.form.get('repackingAmount');
        value ? ctrl?.addValidators([Validators.required]) : ctrl?.clearValidators();
        ctrl?.updateValueAndValidity();
      }),
      takeUntil(this.destroy$)
    ).subscribe();

    this.form.get('isIncludeGST')?.valueChanges.pipe(
      tap(() => {
        for (let i = 0; i < this.items.controls.length; i++) {
          this.updateItemCalculation(i, 'discount');
        }
        this.onChangeAdjustMent();
      }),
      takeUntil(this.destroy$)
    ).subscribe();

    this.form.get('isNeedRepacking')?.valueChanges.pipe(tap(() => this.onChangeAdjustMent()), takeUntil(this.destroy$)).subscribe();
    this.form.get('advancePaymentAmount')?.valueChanges.pipe(tap(() => this.onChangeAdjustMent()), takeUntil(this.destroy$)).subscribe();
    this.form.get('repackingAmount')?.valueChanges.pipe(tap(() => this.onChangeAdjustMent()), takeUntil(this.destroy$)).subscribe();

  }

  get formValue() {
    return this.form.value
  }

  initForm = () => {
    this.form = this.fb.group({
      id: [null],
      countryId: [null, Validators.compose([Validators.required])],
      customerId: [null, Validators.compose([Validators.required])],
      mobileNo: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      supplierType: [null],
      deliveryType: [null, Validators.compose([Validators.required])],
      notes: [null],
      orderStatus: [null],
      dateTime: [null],
      expectedDeliveryDate: [null],
      isIncludeGST: [null],
      isWaitForPayment: [null],
      waitForPaymentOption: [null],
      paymentDueDate: [null],
      isNeedRepacking: [null],
      isNeedShrinkPacking: [null],
      repackingAmount: [null],
      bankGroupId: [null],
      advancePaymentAmount: [null],
      shippingAddressId: [null, Validators.compose([Validators.required])],
      deliveryPersonName: [null],
      deliveryPersonMobileNo: [null],
      paymentTermsId: [null],
      paymentTypeId: [null],
      customerGSTId: [null],
      packingTypeMasterId: [null],
      holdReason: [null],
      adjustmentAmount: [null],
      deliveryPersonId: [null],
      salesOrderItemList: this.fb.array([]),
      transporterId: [null],
      courierLink: [null],
      transportBranchId: [null],
      travellerId: [null],
      courierMasterId: [null],
      customerPickupWarehouseId: [null],
      billID: [null],
    });

    this.form.patchValue({
      isIncludeGST: true,
      isWaitForPayment: false,
      isNeedRepacking: false,
      isNeedShrinkPacking: false,
    })

    this.form.get('dateTime')?.setValue(moment());
    this.form.get('dateTime')?.disable();
    this.form.get('dateTime')?.updateValueAndValidity();

    this.addItem();

  }

  getRequiredData = (customerId: number, inquiryId: number, salesOrderId: number) => {

    let param = {};
    if (customerId && !inquiryId) {
      param['customerId'] = customerId;
    } else if (inquiryId && customerId) {
      param['inquiryId'] = inquiryId;
      param['customerId'] = customerId;
    }

    if(salesOrderId) {
      param['salesOrderId'] = salesOrderId;
    }

    this.utilsService.post(this.utilsService.serverVariableService.SO_REQUIRED_DATA, param).pipe(
      map((res) => res?.data),
      tap((data: SalesOrderDropdowns) => {
        this.allSalesOrderDropdowns = data;

        for (const key of Object.keys(this.allSalesOrderDropdowns)) {
          const arr = ['supplierTypes', 'orderUnit', 'waitForPaymentOptions', 'deliveryTypes', 'item']
          if (!arr.includes(key)) {
            if (this.allSalesOrderDropdowns[key] && Array.isArray(this.allSalesOrderDropdowns[key])) {
              this.allSalesOrderDropdowns[key] = this.utilsService.transformDropdownItems(this.allSalesOrderDropdowns[key]);
            }
          }
        }

        if(customerId) {
          this.form.get('shippingAddressId')?.setValue((this.allSalesOrderDropdowns?.shippingAddress || []).find((item) => item.isDefault)?.id);
        }

        // wait for payment options filter
        if(this.allSalesOrderDropdowns?.waitForPaymentOptions) {
          this.allSalesOrderDropdowns.filteredWaitForPaymentOptions = Serialize(this.allSalesOrderDropdowns.waitForPaymentOptions.filter((item) => item.value !== TO_RAISE_PO));
        }
        
        if(this.isRouteFromInquiry()) {
          this.setCustomerData()
          this.setItemData(data)
        }

        if(this.salesOrderId()) {
          this.setItemData(data)

          // disable customerId
          this.form.get('customerId')?.disable();
          this.form.get('customerId')?.updateValueAndValidity();
        }

        setTimeout(() => {
          this.getDropdownActiveInactiveCheck();
        }, 150);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }
  
  setCustomerData = () => { 
    const selectedCustomer = this.allSalesOrderDropdowns?.customers?.find((item) => item.id === this.form.get('customerId').value);
    this.customerDetails.setProp('fullName', selectedCustomer?.fullName);
    this.form.get('countryId')?.setValue(selectedCustomer?.countryId);
    this.form.get('mobileNo')?.setValue(selectedCustomer?.phone);
  }

  setItemData = (res: any) => {
    let data = res['SalesOrder'] as SalesOrder
    let responseItem = res['salesOrderItems'] as SalesOrderItemList[]

    if (!responseItem) {
      return;
    };

    if(!this.utilsService.isEmptyObjectOrNullUndefined(data)) {

      const obj = Serialize(data) as SalesOrder;

      if(res['salesOrderDocs']?.length > 0) {
        this.attachmentsList.set(res['salesOrderDocs']);
      }

      this.form.patchValue({
        ...obj,
        deliveryType: obj.deliveryType?.['value'],
        supplierType: obj.supplierType?.['value'],
        waitForPaymentOption: obj.waitForPaymentOption?.['value'],
        countryId: obj.countryExtensionId,
        expectedDeliveryDate: obj.expectedDeliveryDate ? moment(obj.expectedDeliveryDate) : null,
      })

      if (this.form.get('orderStatus').value === this.enumForSalesOrderSaveStatus.HOLD) {
        this.fromTab.set(this.enumForSalesOrderSaveStatus.HOLD);
      }

      this.onChangeSaleType(obj.waitForPaymentOption?.['value']);
      const selectedCustomer = this.allSalesOrderDropdowns?.customers?.find((item) => item.id === this.form.get('customerId').value);
      this.customerDetails.setProp('fullName', selectedCustomer?.fullName);
    }

    this.items.clear();
    for (let index = 0; index < responseItem.length; index++) {
      const item = responseItem[index];
      this.addItem();

      let id = null;
      if(!this.isRouteFromInquiry()) {
        id = item.itemInfo?.id;
      } else {
        id = item.itemId;
      }

      const itemDropdown = this.allSalesOrderDropdowns?.item?.find(dropdownItem => dropdownItem.id === id);
      this.items.at(index)?.patchValue({ itemId: id });
      this.onItemChange(itemDropdown, index);

      setTimeout(() => {
        
        const upComingQtyList = (item?.upComingQtyList || []).map((item) => { return { ...item } });

        this.items.at(index)?.patchValue({
          ...item,
          orderUnit: item.orderUnit['value'],
          isMarkaSelected: item.isMarkaSelected || false,
          discount: item?.discountPercent ?? null,
          orderQty: item?.orderQty ?? null,
          hsnCodeId: item?.hsnCodeMasterId,
          saveResMarkaWise: item.markaQtyOutput,
          totalMarkaQty: item.totalQty,
          saveResUpcomingQty: upComingQtyList,
          markaWiseQtyList: item.markaWiseQtyList,
          upcomingQtyTotal: sumArr((item.upComingQtyList || []), v => v?.qty || 0),
        });

        disableFieldUtil(['orderQty', 'piecePerCarton'], this.items.at(index) as FormGroup, item.isMarkaSelected);

        this.items.at(index)?.get('hsnDropdown')?.setValue(itemDropdown?.hsnCodes || []);
        // const outOfStockNum = item.orderUnit['value'] === this.enumForOrderUnit.CARTON ? itemDropdown?.cartonOutOfStock : itemDropdown?.looseOutOfStock;
        this.items.at(index).get('outOfStock')?.setValue(itemDropdown.outOfStock);
        
        this.checkOutOfStock(this.items.at(index).get('outOfStock')?.value, index, this.items.at(index)?.get('upcomingQtyTotal')?.value);
        this.getTotalMarkaQty(index);
        this.onHsnCodeChange(index);

      }, 50);
    }
  }

  onChangeCustomer = (inquiryId: number = null) => {
    this.setCustomerData()
    this.form.get('customerGSTId')?.reset();
    this.getRequiredData(this.form.get('customerId').value, inquiryId, null)
  }

  get items() {
    return this.form.get('salesOrderItemList') as FormArray
  }

  addItem = () => {
    this.items.push(this.fb.group({
      id: [null],
      itemId: [null, Validators.compose([Validators.required])],
      orderUnit: [null, Validators.compose([Validators.required])],
      orderQty: [null],
      totalQty: [null],
      rate: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      totalAmount: [null],
      discount: [null, Validators.compose([Validators.max(100), Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      note: [null],
      isMarkaSelected: [null],
      //
      discountAmount: [null],
      finalAmount: [null],
      piecePerCarton: [null],
      outOfStock: [null],
      //
      hsnCodeId: [null],
      hsnDropdown: [null],
      taxName: [null],
      gstRate: [null],
      gstAmount: [null],
      totalAmountWithGst: [null],

      totalMarkaQty: [null],
      markaData: [null],
      saveResUpcomingQty: [null],
      upcomingQtyTotal: [null],
      //
      saveResMarkaWise: [null],
      markaWiseQtyList: [null],
      isUpcomingAvailable: [null],
      branchMaster: [null],
      isMarkaSaved: [false],
      isUpcomingSaved: [false],
      isSellCarton: [false],
      orderUnits: [null],
    }))
  }

  openRemoveSalesItemModal(index: number, content: TemplateRef<any>) {
    const itemName = (this.allSalesOrderDropdowns?.item || []).find((a) => a.id === this.items.at(index)?.get('itemId')?.value)?.displayName;
    this.itemName.set(itemName ?? null);
    this.selectedItemIndex.set(index);
    this.modalService.open(content);
  }

  removeSalesItem(modal: NgbModalRef) {
    const id = this.items.at(this.selectedItemIndex())?.get('id')?.value;
    if (id) {
      this.deleteItemKeys.deletedOrderItemIdList.push(id)
    }
    const itemId = this.items.at(this.selectedItemIndex())?.get('itemId')?.value;
    this.items.removeAt(this.selectedItemIndex());
    this.modalService.close(modal);
    this.onChangeAdjustMent();

    if (itemId) {
      this.commonCacheClear(itemId, null);
    }
  }

  commonCacheClear = (itemId: number, index: number) => {

    if (!itemId && index >= 0) {
      this.markaDataCache.forEach((_, key) => {
        if (key.endsWith(`-${index}`)) {
          this.markaDataCache.delete(key);
        }
      });
    };

    this.markaDataCache.forEach((_, key) => {
      if (key.startsWith(`${itemId}-`)) {
        this.markaDataCache.delete(key);
      }
    });
  };
 
  updateItemCalculation = (index: number, type: 'hsnCodeId' | 'discount') => {
    const item = this.items.at(index);
    if (!item) return;

    switch (type) {
      case 'hsnCodeId': {
        const value = item?.get('hsnCodeId')?.value;
        const hsnDropdown = item?.get('hsnDropdown')?.value;
        const dropdown = hsnDropdown?.find((item: any) => item.id === value) || null;
        item.get('taxName')?.setValue(dropdown?.taxName);
        item.get('gstRate')?.setValue(dropdown?.rate || null);

        const totalAmount = +item.get('totalAmount')?.value || 0;
        const gstRate = +item.get('gstRate')?.value || 0;
        const gstAmount = isFinite(totalAmount) && isFinite(gstRate) ? parseFloat((totalAmount * gstRate / 100).toFixed(2)) : 0;
        const totalAmountWithGst = parseFloat((totalAmount + gstAmount).toFixed(2));

        item.get('gstAmount')?.setValue(gstAmount);
        item.get('gstAmount')?.updateValueAndValidity();
        item.get('totalAmountWithGst')?.setValue(totalAmountWithGst);
        item.get('totalAmountWithGst')?.updateValueAndValidity();
        break;
      }
      case 'discount': {

        const isIncludeGST = this.form.get('isIncludeGST')?.value as boolean;
        const a = isIncludeGST ? 'totalAmountWithGst' : 'totalAmount';

        const discount = +item.get('discount')?.value || 0;
        const totalAmount = +item.get(a)?.value || 0;
        const discountAmount = isFinite(totalAmount) && isFinite(discount) ? parseFloat((totalAmount * discount / 100).toFixed(2)) : 0;
        const finalAmount = parseFloat((totalAmount - discountAmount).toFixed(2));

        item.get('discountAmount')?.setValue(discountAmount);
        item.get('discountAmount')?.updateValueAndValidity();
        item.get('finalAmount')?.setValue(finalAmount);
        item.get('finalAmount')?.updateValueAndValidity();
        break;
      }
    }
  };

  // on Change hsn code
  onHsnCodeChange = (index: number) => {
    this.updateItemCalculation(index, 'hsnCodeId');
    this.updateItemCalculation(index, 'discount');
  }

  onItemChange = (selectedItem: InquiryItemDropdown, index: number) => {

    this.items.at(index).patchValue({
      taxName: null,
      gstRate: null,
      gstAmount: null,
      totalAmountWithGst: null,
      hsnCodeId: null,
      rate: null,
      piecePerCarton: null,
      markaData: null,
      totalMarkaQty: null,
      saveResMarkaWise: null,
      saveResUpcomingQty: null,
      discountAmount: null,
      discount: null,
      finalAmount: null,
      orderUnit: null,
      outOfStock: selectedItem?.outOfStock || false,
      note: null,
      isMarkaSelected: false,
      upcomingQtyTotal: null,
      isUpcomingAvailable: selectedItem?.isUpcomingAvailable || false,
      orderQty: null,
      branchMaster: [],
      isMarkaSaved: false,
      isUpcomingSaved: false,
      isSellCarton: selectedItem?.isSellCarton || false
    });

    
    this.items.at(index).get('orderUnit').setValue(this.enumForOrderUnit.CARTON);
    this.onOrderUnitChange(index);
    this.commonCacheClear(null, index);
    this.getTotalMarkaQty(index);

    const hsnDropdown = this.allSalesOrderDropdowns?.item?.find(item => item.id === selectedItem?.id)?.hsnCodes || [];
    this.items.at(index).get('hsnDropdown')?.setValue(hsnDropdown);

    const orderUnits = this.allSalesOrderDropdowns?.item?.find(item => item.id === selectedItem?.id)?.orderUnits || [];
    this.items.at(index).get('orderUnits')?.setValue(orderUnits);

    if (!this.utilsService.isEmptyObjectOrNullUndefined(selectedItem)) {
      this.items.at(index).get('branchMaster')?.setValue(selectedItem?.branchMaster || []);
      this.allMarkaBranchData.activeTab = 0;
    }
  }

  // Attachment

  onSelectAttachments = (event: any): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        const allowedExts = ['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'];

        if (allowedExts.includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if ((this.attachmentsList.get() || []).length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }
            this.attachmentsList.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_INVALID_EXTENSION);
        }
      });
    }
  }

  openLink = (link: string, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment = (i: number, file) => {
    if (file.id) {
      this.deleteItemKeys.deleteDocumentIdList.push(file.id)
    }
    this.attachmentsList.removeAt(i)
  }

  // Marka Modals
  openMarkaPopup = (index: number, isOnlyUpcoming: boolean) => {

    const branchMaster = this.items.at(index).get('branchMaster')?.value;

    this.selectedItemIndex.set(index);
    this.isUpcomingTab = isOnlyUpcoming;
    this.resetMarkaModalFilters();

    const itemId = this.items.at(index).get('itemId')?.value as number;

    const isBranchMasterEmpty = (branchMaster || []).length === 0;
    if (isBranchMasterEmpty) {
      this.isUpcomingTab = true;
    }
    let branchId = null;
    let API = null;

    if (!this.isUpcomingTab && !isBranchMasterEmpty) {
      branchId = branchMaster[this.allMarkaBranchData.activeTab !== null ? this.allMarkaBranchData.activeTab : 0].id;
      API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&branchId=${branchId}&isUpComingQty=${this.isUpcomingTab}`;
    } else {
      API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&isUpComingQty=${this.isUpcomingTab}`;
    }

    if (this.salesOrderId()) {
      if (!isBranchMasterEmpty && !this.isUpcomingTab) {
        API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&branchId=${branchId}&isUpComingQty=${this.isUpcomingTab}&salesOrderId=${this.salesOrderId()}`;
      } else {
        API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&isUpComingQty=${this.isUpcomingTab}&salesOrderId=${this.salesOrderId()}`;
      }
    }

    const cacheKey = `${itemId}-${branchId ?? 'null'}-${this.isUpcomingTab}-${this.salesOrderId() ?? 'null'}-${index}`;

    if (this.markaDataCache.has(cacheKey)) {
      const cachedData = this.markaDataCache.get(cacheKey);
      this.setMarkaPopupDataCache(cachedData, this.isUpcomingTab, index);
      return;
    }


    this.resetMarkaModalFilters();

    this.utilsService.get(API).pipe(
      map(res => res?.data),
      tap((data: any) => {
        this.markaDataCache.set(cacheKey, data);
        this.setMarkaPopupDataCache(data, this.isUpcomingTab, index);
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  };
  
  setMarkaPopupDataCache(data: any, isOnlyUpcoming: boolean, itemIndex: number) {

    const isMarkaSaved = this.items.at(itemIndex).get('isMarkaSaved')?.value as boolean;
    const isUpcomingSaved = this.items.at(itemIndex).get('isUpcomingSaved')?.value as boolean;
    
    this.allMarkaBranchData.markaDropDown = data['markaDropDown'] ?? null;
    this.allMarkaBranchData.upcomingQty = data?.['upComingQty']?.['upComingQty'] ?? [];
    this.allMarkaBranchData.warehouse = data['warehouse'] ?? [];
    this.allMarkaBranchData.upcomingCartonQty = data?.['upComingQty']?.['cartonQty'] ?? 0;
    this.allMarkaBranchData.totalUpcomingQty = data?.['upComingQty']?.['totalQty'] ?? 0;

    if (this.allMarkaBranchData.markaDropDown?.markaStocks?.length > 0 && !isMarkaSaved) {
      for (let i = 0; i < this.allMarkaBranchData.markaDropDown.markaStocks.length; i++) {
        this.allMarkaBranchData.markaDropDown.markaStocks[i].qty = this.allMarkaBranchData.markaDropDown.markaStocks[i]?.orderQty ?? this.allMarkaBranchData.markaDropDown.markaStocks[i]?.qty;
      }
    }
    if (this.allMarkaBranchData?.markaDropDown?.markaStocks?.length > 0) {
      this.allMarkaBranchData.markaDropDown.markaStocks = this.calculateMarkaStockSums(this.allMarkaBranchData.markaDropDown.markaStocks);
    }

    if (this.allMarkaBranchData.upcomingQty?.length > 0 && !isUpcomingSaved) {
      for (let i = 0; i < this.allMarkaBranchData.upcomingQty.length; i++) {
        this.allMarkaBranchData.upcomingQty[i].upcomingQty = this.allMarkaBranchData.upcomingQty[i]?.orderQty ?? this.allMarkaBranchData.upcomingQty[i]?.upcomingQty;
      }
    }

    this.filtersMarkaModal(null, null);
    this.filterUpcomingQty(null, null);
  
    if (isOnlyUpcoming) {
      this.isUpcomingTab = true;
      this.allMarkaBranchData.activeTab = null;
    } else {
      this.isUpcomingTab = false;
      this.changeBranchTab(itemIndex, this.allMarkaBranchData.activeTab ?? 0, true);
    }
  }

  calculateMarkaStockSums(markaStocks: MarkaStocks[]): MarkaStocks[] {
    return markaStocks.map(marka => {
      const sumTotalCartonQty = sumArr((marka?.warehouseStocks || []), x => Number(x?.totalCartonQty ? x.totalCartonQty : 0) || 0);
      const sumTotalQty = sumArr((marka?.warehouseStocks || []), x => Number(x?.totalQty ? x.totalQty : 0) || 0);
      const sumCartonPcs = sumArr((marka?.warehouseStocks || []), x => Number(x?.totalCartons ? x.totalCartons : 0) || 0);

      return { ...marka, sumTotalCartonQty, sumTotalQty, sumCartonPcs };
    });
  }

  changeBranchTab = (itemIndex: number, activeTab: number, firstTimeCall: boolean) => {

    if(this.allMarkaBranchData.activeTab === activeTab) {
      return;
    }
    this.allMarkaBranchData.markaDropDown = {} as InquiryBranchMarkaModal;

    this.resetMarkaModalFilters();

    this.allMarkaBranchData.activeTab = firstTimeCall ? 0 : activeTab;
    this.openMarkaPopup(itemIndex, false);
  }

  changeToUpcomingTab = (index: number) => {
    this.openMarkaPopup(index, true);
  }

  resetMarkaModalFilters = () => {
    this.searchText.reset();
    this.warehouseIdFilter = [];
  }

  clearWarhouseId = () => {
    this.warehouseIdFilter = [];
  }
  
  filtersMarkaModal(searchType: 'marka' | 'warehouseId', searchValue: string | number[]) {
    let filteredResults = [];

    if (!searchValue || (Array.isArray(searchValue) && searchValue.length === 0)) {
      this.filteredMarkaList = (this.allMarkaBranchData.markaDropDown?.markaStocks || []);
      return;
    }

    if (searchType === 'marka') {
      filteredResults = (this.allMarkaBranchData.markaDropDown?.markaStocks || []).filter(markaStock =>
        markaStock.marka.toLowerCase().includes(searchValue.toString().toLowerCase())
      );
    }

    else if (searchType === 'warehouseId') {
      filteredResults = (this.allMarkaBranchData.markaDropDown?.markaStocks || []).filter(markaStock =>
        markaStock.warehouseStocks.some(warehouseStock =>
          Array.isArray(searchValue) ? searchValue.includes(warehouseStock.warehouseId) : false
        )
      );
    }
    this.filteredMarkaList = filteredResults;
  }
  
  filterUpcomingQty(searchType: 'marka', searchValue: string) {
    let filteredResults = [];

    if (!searchValue) {
      this.filteredUpcomingQtyList = (this.allMarkaBranchData.upcomingQty || []);
      return;
    }

    if (searchType === 'marka') {
      filteredResults = (this.allMarkaBranchData.upcomingQty || []).filter(upcomingQty =>
        upcomingQty.marka.toLowerCase().includes(searchValue.toString().toLowerCase())
      );
    }
    this.filteredUpcomingQtyList = filteredResults;
  }

  onSaveUpcomingQty = () => {
    const totalMarkaQtys = sumArr(this.filteredUpcomingQtyList || [], x => Number(x.upcomingQty) || 0);

    const tempFilteredData = this.filteredUpcomingQtyList.map((markaStock) => ({
      id: markaStock.id ?? null,
      qty: markaStock.upcomingQty,
      marka: markaStock.marka,
      poImportItemId: markaStock.poImportItemId,
      // importStatus: markaStock.stage['value']
    })).filter((a => a?.qty > 0));

    const selectedItem = this.items.at(this.selectedItemIndex());
    if (!selectedItem) return;

    selectedItem.get('saveResUpcomingQty')?.setValue([...tempFilteredData]);
    selectedItem.get('upcomingQtyTotal')?.setValue(totalMarkaQtys);
    selectedItem.get('isUpcomingSaved')?.setValue(true);

    this.getTotalMarkaQty(this.selectedItemIndex());
    this.getItemStock(this.selectedItemIndex());
    this.checkOutOfStock(true, this.selectedItemIndex(), selectedItem.get('upcomingQtyTotal')?.value);
  };
  

  onSaveMarkaQty = () => {

    const tempFilteredData = Array.from(this.markaDataCache.values()).flatMap(entry => {
      const branchId = entry.markaDropDown?.branchId;
      return entry.markaDropDown?.markaStocks?.filter((a: MarkaStocks) => a?.qty > 0).map((markaStock: MarkaStocks) => ({
        ...markaStock,
        branchId
      }));
    })

    const totalMarkaQtys = sumArr(tempFilteredData || [], x => Number(x?.qty) || 0);
    this.items.at(this.selectedItemIndex())?.get('orderQty')?.setValue(totalMarkaQtys || null);
    this.items.at(this.selectedItemIndex())?.get('markaData')?.setValue([...tempFilteredData]);
    this.items.at(this.selectedItemIndex())?.get('isMarkaSelected')?.setValue(true);
    this.items.at(this.selectedItemIndex())?.get('isMarkaSaved')?.setValue(true);

    this.getTotalMarkaQty(this.selectedItemIndex());
    this.getItemStock(this.selectedItemIndex());
  }

  getTotalMarkaQty = (index: number) => {
    const orderUnit = this.items.at(index)?.get('orderUnit')?.value;
    const rate = this.items.at(index)?.get('rate')?.value;
    const totalMarkaQty = this.items.at(index)?.get('totalMarkaQty')?.value;

    let value = null;

    if (orderUnit === this.enumForOrderUnit.LOOSE) {
      value = this.items.at(index)?.get('orderQty')?.value || 0;
    }

    if (orderUnit === this.enumForOrderUnit.CARTON) {
      const piecePerCarton = this.items.at(index)?.get('piecePerCarton')?.value || 1;
      value = (this.items.at(index)?.get('orderQty')?.value * piecePerCarton) || 0;
    }

    const isSaveResMarka = this.items.at(index)?.get('saveResMarkaWise')?.value;

    if(!isSaveResMarka) {
      this.items.at(index)?.get('totalMarkaQty')?.setValue(value || null);
    }

    if (rate) {
      this.items.at(index)?.get('totalAmount')?.setValue(value * rate || null);
    } else {
      this.items.at(index)?.get('totalAmount')?.setValue(null);
    }

    if(isSaveResMarka) {
      this.items.at(index)?.get('totalAmount')?.setValue((totalMarkaQty * rate) || null);
    } else {
      this.items.at(index)?.get('totalAmount')?.setValue(null);
    }

    //HSN
    this.onHsnCodeChange(index);
  }

  // Input changes item
  onOrderUnitChange = (index: number) => {
    const value = this.items.at(index)?.get('orderUnit')?.value;
    switch (value) {
      case this.enumForOrderUnit.CARTON:
        this.items.at(index).get('piecePerCarton')?.enable();
        break;
      default:
        this.items.at(index).get('piecePerCarton')?.disable();
        break;
    }

    const itemId = this.items.at(index)?.get('itemId')?.value;
    const item = this.allSalesOrderDropdowns?.item?.find(item => item.id === itemId) || null;
    const rate = value === this.enumForOrderUnit.LOOSE ? item?.itemPrice : item?.cartonPrice;
    const outOfStock = item?.outOfStock || false;
    const upcomingQty = this.items.at(index).get('upcomingQtyTotal').value;

    this.checkOutOfStock(outOfStock, index, upcomingQty);

    this.items.at(index).patchValue({
      rate: rate || null,
      outOfStock: outOfStock,
      piecePerCarton: null,
      orderQty: 0,
      markaData: null,
      totalMarkaQty: null,
      isMarkaSelected: false,
    });

    this.items.at(index).get('piecePerCarton')?.updateValueAndValidity();
    this.onClearOrderQty(index, true);

    if(this.items.at(index)?.get('upcomingQtyTotal')?.value > 0) {
      this.getItemStock(index);
    }
  }

  onChangeAdjustMent = () => {
    const finalTotal = this.sumTotals.totalFinalAmt || 0;
    const { adjustmentAmount, isNeedRepacking } = this.form.getRawValue();
    const repackingAmountValue = Number(isNeedRepacking ? this.form.get('repackingAmount')?.value : 0);
    const advancePaymentAmountValue = Number(this.form.get('advancePaymentAmount')?.value) * -1;

    const sum = Number((adjustmentAmount || 0) + finalTotal + repackingAmountValue + advancePaymentAmountValue).toFixed(2);
    this.sumTotals.grandTotal = Number(sum);
  }

  // On Save SO

  onSave = (saveStatus: string, fromModal: boolean = false, content?: TemplateRef<any>, modal?: NgbModalRef) => {
    const formData = new FormData();

    if(this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    if (this.items?.length === 0) {
      this.utilsService.toasterService.error('Minimum One Item is required.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (!this.utilsService.isEverythingUniqueMultiple(this.items.value, 'itemId', 'orderUnit')) {
      this.utilsService.toasterService.error(this.utilsService.validationService.SAME_ITEM_SAME_ORDER_UNIT, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const isLooseAndSellInCarton = this.items.controls.some(a => a.get('orderUnit')?.value === this.enumForOrderUnit.LOOSE && a.get('isSellCarton')?.value)
    if(isLooseAndSellInCarton) {
      return;
    }

    const someItemsOutOfStock = this.items.controls.some(a => {
      const upcomingQty = a.get('upcomingQtyTotal')?.value || 0;
      return a.get('outOfStock').value && upcomingQty === 0
    })
    if (someItemsOutOfStock) {
      this.utilsService.toasterService.error(this.utilsService.validationService.SOME_ITEM_OUT_OF_STOCK, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const isOrderQtyEmptyAndUpcomingEmpty = this.items.controls.some(a => {
      const orderQty = a.get('orderQty')?.value || 0;
      const upcomingQty = a.get('upcomingQtyTotal')?.value || 0;
      return orderQty === 0 && upcomingQty === 0
    })
    if (isOrderQtyEmptyAndUpcomingEmpty) {
      this.utilsService.toasterService.error(this.utilsService.validationService.ATLEAST_ONE_QTY_OR_UPCOMING_QTY_REQUIRED, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const isLooseUpcoming = this.items.controls.some(a => {
      const orderUnit = a.get('orderUnit')?.value;
      const upcomingQty = a.get('upcomingQtyTotal')?.value || 0;
      return orderUnit === this.enumForOrderUnit.LOOSE && upcomingQty > 0
    })

    if (isLooseUpcoming) {
      this.utilsService.toasterService.error(this.utilsService.validationService.CANT_SELL_UPCOMING_INTO_SALE, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if ((this.attachmentsList.get() || []).length > 0) {
      for (const file of this.attachmentsList.get() || []) {
        if (file.file) {
          formData.append('images', file.file);
        }
      }
    }

    const expectedDeliveryDate = this.form.get('expectedDeliveryDate')?.value ? this.form.get('expectedDeliveryDate')?.value : null;
    const dateTime = this.form.get('dateTime')?.getRawValue() ? this.form.get('dateTime')?.getRawValue() : null;
    const paymentDueDate = this.form.get('paymentDueDate')?.getRawValue() ? this.form.get('paymentDueDate')?.getRawValue() : null;

    const param: SalesOrder = {
      ...this.form.value,
      orderStatus: saveStatus,
      countryExtensionId: this.form.value.countryId,

      expectedDeliveryDate: expectedDeliveryDate ? moment(expectedDeliveryDate).format('YYYY-MM-DD HH:mm:00') : null,
      dateTime: dateTime ? moment(dateTime).format('YYYY-MM-DD HH:mm:00') : null,
      paymentDueDate: paymentDueDate ? paymentDueDate : null,
      deleteDocumentIdList: this.deleteItemKeys.deleteDocumentIdList || [],
      deletedOrderItemIdList: this.deleteItemKeys.deletedOrderItemIdList || [],

      salesOrderItemList: this.form.value.salesOrderItemList.map((item: SalesOrderItemList, i: number) => {
        const markaWiseQtyList = (item.saveResMarkaWise as SaveResMarkaWise[] || [])?.flatMap(branch =>
          branch.markaStocks?.flatMap(markaStock =>
            markaStock.warehouseStocks?.flatMap(warehouseStock => ({
              marka: markaStock.marka ?? null,
              warehouseId: warehouseStock.warehouseId,
              qty: item.orderUnit === this.enumForOrderUnit.CARTON ? warehouseStock.totalCartons : warehouseStock.totalLooseQty,
              totalCartons: warehouseStock.totalCartons,
              totalLooseQty: warehouseStock.totalLooseQty
            }))
          )
        );

        const upComingQtyList: SaveResUpcomingQty[] = item.saveResUpcomingQty || [];

        const isMarkaSelected = item.isMarkaSelected;

        const obj = {
          ...item,
          hsnCodeMasterId: item.hsnCodeId,
          markaWiseQtyList,
          totalQty: item.totalMarkaQty,
          isMarkaSelected,
          upComingQtyList,
          orderQty: this.items.at(i).get('orderQty')?.getRawValue()
        };

        const finalObj = this.utilsService.removeKeys(obj, ['hsnDropdown', 'totalMarkaQty', 'markaData', 'outOfStock', 
                'saveResMarkaWise', 'hsnCodeId', 'saveResUpcomingQty', 'branchMaster', 'isUpcomingSaved', 'isMarkaSaved']);
        return finalObj;
      })
    };

    const finalParam = this.utilsService.removeKeys(param, ['countryId']);
    
    this.form.get('holdReason').reset()
    
    const isUpcomingQty = this.items.value.some((item: SalesOrderItemList) => item.upcomingQtyTotal > 0);
    if (saveStatus === this.enumForSalesOrderSaveStatus.HOLD && !fromModal && this.fromTab() != saveStatus) {
      if (!isUpcomingQty) {
        setTimeout(() => {
          const modalRef = this.modalService.open(content, { windowClass: 'modal-lg' });
          modalRef.hidden.pipe(takeUntil(this.destroy$)).subscribe(() => {
            this.form.get('holdReason')?.clearValidators();
            this.form.get('holdReason')?.updateValueAndValidity();
          });
          this.form.get('holdReason')?.setValidators([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)]);
          this.form.get('holdReason')?.updateValueAndValidity();
        }, 50);
      } else {
        this.utilsService.toasterService.error(this.utilsService.validationService.HOLD_NOT_ALLOWED_UPCOMING_QTY, '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
      }
      return;
    }

    this.utilsService.emptyingNullValuesJSON(finalParam)
    formData.set('salesInfo', JSON.stringify(finalParam));
    this.utilsService.post(this.utilsService.serverVariableService.SO_SALES_ORDER_SAVE, formData, { toast: true }, true).pipe(
      tap(() => {
        this.utilsService.redirectTo('/users/sales/sales-orders')
        this.modalService?.close(modal);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
    
  }

  onChangeDeliveryType = () => {
    this.form.get('deliveryType')?.valueChanges.pipe(
        tap((value) => {
          const controlsToReset = ['deliveryPersonMobileNo', 'deliveryPersonName', 'deliveryPersonId', 'customerPickupWarehouseId', 'courierMasterId',
            'transporterId', 'transportBranchId', 'travellerId'];
          controlsToReset.forEach(ctrlName => {
            const ctrl = this.form.get(ctrlName);
            if (ctrl) {
              resetValidators(ctrl);
            }
          });

          switch (value) {
            case this.enumForDeliveryTypes.CUSTOMER_PICKUP:
              this.form.get('customerPickupWarehouseId')?.addValidators([Validators.required]);
              break;

            case this.enumForDeliveryTypes.DELIVERY_BY_COMPANY_TEMPO:
              this.form.get('deliveryPersonId')?.addValidators([Validators.required]);
              break;

            case this.enumForDeliveryTypes.DELIVERY_BY_OUTSIDE_TEMPO:
              this.form.get('deliveryPersonName')?.addValidators([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)]);
              this.form.get('deliveryPersonMobileNo')?.addValidators([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)]);
              break;

            case this.enumForDeliveryTypes.COURIER:
              this.form.get('courierMasterId')?.addValidators([Validators.required]);
              break;

            case this.enumForDeliveryTypes.TRANSPORT:
              this.form.get('transporterId')?.addValidators([Validators.required]);
              this.form.get('transportBranchId')?.addValidators([Validators.required]);
              break;

            case this.enumForDeliveryTypes.TRAVELLER:
              this.form.get('travellerId')?.addValidators([Validators.required]);
              break;
          }

          controlsToReset.forEach(ctrlName => {
            this.form.get(ctrlName)?.updateValueAndValidity();
          });
        }),
        takeUntil(this.destroy$)
      ).subscribe();
  };

  totalCalcs = () => {
    this.items.valueChanges.pipe(
      tap((values) => {
        this.sumTotals.totalQuantity = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.totalMarkaQty) || 0);
        }, 0);
        this.sumTotals.totalAmount = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.totalAmount) || 0);
        }, 0);
        this.sumTotals.gstPer = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.gstRate) || 0);
        }, 0);
        this.sumTotals.totalAmountWithGst = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.totalAmountWithGst) || 0);
        }, 0);
        this.sumTotals.gstAmountTotal = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.gstAmount) || 0);
        }, 0);
        this.sumTotals.totalFinalAmt = values.reduce((sum: number, item: any) => {
          return sum + (Number(item.finalAmount) || 0);
        }, 0);

        this.onChangeAdjustMent();
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }
  
  // get item stock api

  getItemStock = (index: number) => {

    this.activeTabQtyTab = 0;
    this.isQtyUpcomingTab = false
    const isMarkaSelected = this.items.at(index)?.get('isMarkaSelected')?.value;
    const markaData = this.items.at(index)?.get('markaData')?.value as MarkaStocks[];
    const markaWiseQtyList = this.items.at(index)?.get('markaWiseQtyList')?.value as MarkaWiseQtyList[];
    const upcomingQtyList = (this.items.at(index)?.get('saveResUpcomingQty')?.value || []) as SaveResUpcomingQty[];

    let markaReq = (markaData || []).filter((marka: MarkaStocks) => marka?.qty > 0).map((marka: MarkaStocks) => {
      return {
        marka: marka.marka,
        brandId: marka.branchId,
        qty: marka.qty
      }
    })

    let itemStockRes = (markaWiseQtyList || []).filter((a: MarkaWiseQtyList) => a?.qty > 0).map((a: MarkaWiseQtyList) => {
      return {
        marka: a.marka,
        brandId: a.branchId,
        qty: a.qty,
      }
    })

    let upComingReq = (upcomingQtyList || []).filter((a: SaveResUpcomingQty) => a?.qty > 0).map((a: SaveResUpcomingQty) => {  
      return {
        marka: a.marka,
        poImportItemId: a.poImportItemId,
        qty: a.qty,
      }
    }) || []

    const param = {
      itemId: this.items.at(index)?.get('itemId')?.value,
      qty: this.items.at(index)?.get('orderQty')?.value || 0,
      salesOrderId: this.salesOrderId(),
      pcsPerCarton: this.items.at(index)?.get('piecePerCarton')?.value ?? null,
      orderUnit: this.items.at(index)?.get('orderUnit')?.value,
      markaReq: (markaReq.length > 0 && isMarkaSelected) ? markaReq : itemStockRes,
      upComingReq: upComingReq
    }
    const API = this.utilsService.serverVariableService.SO_GET_ITEM_STOCK;

    this.utilsService.post(API, param, null, true).pipe(
      tap((res: SaveResMarkaWise[]) => {
        if(res) {
          this.saveResMarkaWise = res;
          this.items.at(index)?.get('saveResMarkaWise')?.setValue(res);

          const onlyUpcoming = res.find(a => a.branchName === UPCOMING_SO_STOCK)
          if (res?.length === 1 && onlyUpcoming) {
              this.isQtyUpcomingTab = true
          }
          const isLoose = this.items.at(index)?.get('orderUnit')?.value === this.enumForOrderUnit.LOOSE;
          let totalAvailableQtySum = 0;
          let availableCartonsQtySum = 0;
          let totalOrderQty = 0;
          for (const item of res || []) {
            if (item.branchName !== UPCOMING_SO_STOCK) {
              availableCartonsQtySum += Number(item.availableCartonsQty) || 0;
              totalOrderQty += Number(item.totalAvailableQty) || 0;
            }
            totalAvailableQtySum += Number(item.totalAvailableQty) || 0;
          }

          this.items.at(index)?.get('totalMarkaQty')?.setValue(totalAvailableQtySum);
          this.items.at(index)?.get('orderQty')?.setValue(!isLoose ? availableCartonsQtySum : totalOrderQty);
          this.items.at(index)?.get('piecePerCarton')?.setValue(null);
          
          this.getTotalMarkaQty(index);

        }

      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }
  
  onChangePaymentTerms = () => {
    const paymentTermsId = this.form.get('paymentTermsId')?.value;
    const days = this.allSalesOrderDropdowns.paymentTerms.find((item) => item.id === paymentTermsId)?.noOfDays;
    if (days) {
      this.form.get('paymentDueDate')?.setValue(moment().add(days, 'days').format('YYYY-MM-DD'));
    }
  }

  customSearchFn(term: string, item: InquiryItemDropdown) {
    const lowerCaseTerm = term.toLocaleLowerCase();
    return item?.skuId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
  }

  onChangeTransporterId = () => {
    this.form.get('transporterId')?.valueChanges.pipe(
      tap((value) => {
        this.form.get('transportBranchId')?.setValue(null);
        this.allSalesOrderDropdowns.filteredTransportBranches = this.allSalesOrderDropdowns.transportBranches.filter((item) => item.transportMasterId === value) || [];
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onChangeSaleType = (value: string = null) => {
    const supplierType = this.form.get('supplierType')?.value;
    this.form.get('waitForPaymentOption')?.setValue(value || null);

    if (supplierType) {
      this.allSalesOrderDropdowns.filteredWaitForPaymentOptions = Serialize(this.allSalesOrderDropdowns.waitForPaymentOptions)
    } else {
      this.allSalesOrderDropdowns.filteredWaitForPaymentOptions = Serialize(this.allSalesOrderDropdowns.waitForPaymentOptions.filter((item) => item.value !== TO_RAISE_PO));
    }
  }

  getDropdownActiveInactiveCheck = () => {
    this.allSalesOrderDropdowns.transporters = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.transporters, this.form.get('transporterId')?.value ?? null);
    this.allSalesOrderDropdowns.deliveryPerson = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.deliveryPerson, this.form.get('deliveryPersonId')?.value ?? null);
    this.allSalesOrderDropdowns.courierCompany = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.courierCompany, this.form.get('courierMasterId')?.value ?? null);
    this.allSalesOrderDropdowns.bankGroups = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.bankGroups, this.form.get('bankGroupId')?.value ?? null);
    this.allSalesOrderDropdowns.paymentTypes = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.paymentTypes, this.form.get('paymentTypeId')?.value ?? null);
    this.allSalesOrderDropdowns.paymentTerms = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.paymentTerms, this.form.get('paymentTermsId')?.value ?? null);
    this.allSalesOrderDropdowns.warehouses = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.warehouses, this.form.get('customerPickupWarehouseId')?.value ?? null);
    this.allSalesOrderDropdowns.countryExtensions = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.countryExtensions, this.form.get('countryId')?.value ?? null);
    this.allSalesOrderDropdowns.transportBranches = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.transportBranches, this.form.get('transportBranchId')?.value ?? null);
    this.allSalesOrderDropdowns.gstAddress = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.gstAddress, this.form.get('customerGSTId')?.value ?? null);
    this.allSalesOrderDropdowns.customers = this.utilsService.filterIsActive(this.allSalesOrderDropdowns?.customers, this.form.get('customerId')?.value ?? null);
  }

  onClearValue = (key: string) => {
    this.form.get(key)?.setValue(null);
  }

  checkOutOfStock(outOfStock: boolean, index: number, upcomingQty) {
    if (!upcomingQty) {
      this.items.at(index).get('orderQty')?.addValidators(Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]));
    } else {
      this.items.at(index).get('orderQty')?.clearValidators();
    }
    this.items.at(index).get('orderQty')?.updateValueAndValidity();
  }


  clearUpcomingQty = (itemId: number, index: number) => {
    this.items.at(index)?.get('saveResUpcomingQty')?.setValue([]);
    this.items.at(index)?.get('upcomingQtyTotal')?.setValue(0);
    this.items.at(index)?.get('isUpcomingSaved')?.setValue(false);

    const delKey = `${itemId}-${null}-${true}-${this.salesOrderId() ?? 'null'}-${index}`;
    this.markaDataCache.delete(delKey)

    this.getItemStock(index);
  }

  onClearOrderQty = (index: number, isFromOrderUnitChange = false) => {
    this.items.at(index)?.get('orderQty')?.setValue(null);
    this.items.at(index)?.get('isMarkaSelected')?.setValue(false);

    this.markaDataCache.forEach((_, key) => {
      const keyArr = key.split('-');
      if (keyArr[1] !== 'null') {
        this.markaDataCache.delete(key);
      }
    })

    if(!isFromOrderUnitChange) {
      this.getItemStock(index);
    }
  }
 
  onChangeOrderQty = (index: number) => {
    const orderUnit = this.items.at(index)?.get('orderUnit')?.value;
    if(orderUnit) {
      this.getItemStock(index);
    }
  }

  openStockAvailModal(content: TemplateRef<any>) {
    this.modalService.open(content, { windowClass: 'modal-lg-two'})
  }
}

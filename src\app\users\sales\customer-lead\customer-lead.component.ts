import { Component, computed, inject, OnDestroy, OnInit, signal, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { createArraySignal, createObjectSignal } from '@libs';
import { CustomerLead } from '@modal/CustomerLead';
import { CustomerLeadPagination } from '@modal/request/CustomerLeadPagination';
import { UtilsService } from '@service/utils.service';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { Subject, takeUntil, tap, map } from 'rxjs';
import { CUSTOMER_LEAD_TH } from 'src/app/shared/constants/constant';
declare var window: any;

@Component({
  selector: 'app-customer-lead',
  templateUrl: './customer-lead.component.html',
  styleUrls: ['./customer-lead.component.css']
})
export class CustomerLeadComponent implements OnInit, OnDestroy {

  @ViewChild(DaterangepickerDirective) pickerDirective: DaterangepickerDirective;

  utilsService = inject(UtilsService)
  formGroup: FormGroup
  customerLeadTH = CUSTOMER_LEAD_TH
  enumForSortOrder = this.utilsService.enumForSortOrder

  paginationRequest = createObjectSignal({} as CustomerLeadPagination);
  customerLeadObj = createObjectSignal({} as CustomerLead);
  customerLeadList = createArraySignal([] as CustomerLead[])

  customerLeadListEmpty = computed(() => this.customerLeadList.get().length === 0);
  checkIfAllSelected = computed(() => this.customerLeadList.get().every(item => item.isSelected));
  filteredList = computed(() => this.customerLeadList.get().filter(item => item.isSelected).map(item => item.id))
  flagForSelectAll = signal(false);

  countryCodeDropdown: any[] = [];
  isAdd = signal(false);

  private destroy$ = new Subject<void>();
  private hasDateInit = false;

  private addCustomerLeadModal: any;
  private deleteCustomerLeadModal: any;
  private pagination: any;

  constructor(private fb: FormBuilder) {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: "100" }));
  }

  ngOnInit() {
    this.initForm()
    this.getAllCustomerLead()

    this.addCustomerLeadModal = new window.bootstrap.Modal(
      document.getElementById('addCustomerLeadModal')
    );

    this.deleteCustomerLeadModal = new window.bootstrap.Modal(
      document.getElementById('deleteCustomerLeadModal')
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onRefresh = () => {
    this.getAllCustomerLead()
  }

  get formValue() {
    return this.formGroup.value;
  }

  initForm = () => {
    this.formGroup = this.fb.group({
      id: [null],
      firstName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      middleName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      lastName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      email: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      countryId: [null, Validators.compose([Validators.required])],
      phone: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isActive: [null],
    })
  }

  getAllCustomerLead = () => {

    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData'])
    this.flagForSelectAll.set(false)

    this.utilsService.post(this.utilsService.serverVariableService.CUSTOMER_LEAD_LISTING, param).pipe(
      map((res) => res.data),
      tap((res) => {
        this.pagination = res.pagination;
        this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'] }));
      }),
      map((res) => res?.['content']),
      tap((content: CustomerLead[]) => {
        this.customerLeadList.set(content || [])
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  openAddEditForm = (obj: CustomerLead, isAdd: boolean) => {
    this.isAdd.set(isAdd);
    this.formGroup.reset();
    this.addCustomerLeadModal.show();
    this.utilsService.get(this.utilsService.serverVariableService.CUSTOMER_LEAD_REQ_DATA).pipe(
      map(res => res.data),
      tap((data: { Country: any[] }) => {
        this.countryCodeDropdown = this.utilsService.transformDropdownItems(data.Country || []);
      }),
      tap(() => {
        if (!isAdd) {
          this.formGroup.patchValue({
            id: obj.id,
            firstName: obj.firstName,
            middleName: obj.middleName,
            lastName: obj.lastName,
            email: obj.email,
            countryId: obj.countryId,
            phone: obj.phone,
            isActive: obj.isActive,
          });
        } else {
          const indiaCode = this.countryCodeDropdown.find((item) => item.countryExtension === '+91')

          this.formGroup.patchValue({ 
            isActive: true,
            countryId: indiaCode.isActive ? indiaCode?.id : null,
          });
        }
        this.countryCodeDropdown = this.utilsService.filterIsActive(this.countryCodeDropdown, this.formGroup.value.countryId || null);
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onSave = () => {
    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched()
      return
    }

    const param = this.formGroup.value
    this.utilsService.post(this.utilsService.serverVariableService.CUSTOMER_LEAD_SAVE_EDIT_DELETE, param, { toast: true }).pipe(
      tap(() => {
        this.addCustomerLeadModal.hide()
        this.getAllCustomerLead()
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // Date picker functions
  open = () => {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else {
      this.pickerDirective.hide();
    }
  }

  // Filters Clear
  onClear = () => {
    this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null, searchText: null }));
  }

  // Filter
  onChangeFilter = (event: any, type: 'date' | 'search') => {
    switch (type) {
      case 'date':
        if (!this.hasDateInit) {
          this.hasDateInit = true;
          return;
        }
        const fromDate = event.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
        const toDate = event.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
        this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
        break;
      case 'search':
        this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
        break;
    }
    this.destroy$.complete()
    this.getAllCustomerLead()
  }

  exportReport = () => {

    const param = {
      ids: this.filteredList().length > 0 ? this.filteredList() : null,
      searchText: this.paginationRequest.get().searchText,
      fromDate: this.paginationRequest.get().fromDate,
      toDate: this.paginationRequest.get().toDate,
      sortOrder: this.paginationRequest.get().sortOrder,
      sortColumn: this.paginationRequest.get().sortColumn,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.CUSTOMER_LEAD_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Customer Lead Sheet');
    });
  }

  // DELETE 
  openDeleteCustomerLeadModal = (item: CustomerLead) => {
    this.customerLeadObj.set(item)
    this.deleteCustomerLeadModal.show();
  }

  deleteCustomerLead = () => {
    const API = this.utilsService.serverVariableService.CUSTOMER_LEAD_SAVE_EDIT_DELETE + `?id=${this.customerLeadObj.get().id}`;

    this.utilsService.delete(API, { toast: true }).pipe(
      tap(() => {
        this.getAllCustomerLead();
        this.deleteCustomerLeadModal.hide();
        if (!this.pagination.first && this.pagination.last && this.pagination.numberOfElements === 1) {
          this.paginationRequest.update(a => ({ ...a, pageNo: this.paginationRequest.get().pageNo - 1 }));
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  // Sort
  onSortTH(key: string) {
    if (this.utilsService.isEmptyObjectOrNullUndefined(this.customerLeadListEmpty())) {
      return;
    }
    const { A, D } = this.enumForSortOrder;
    const sortOrder = (key === this.paginationRequest.get().sortColumn && this.paginationRequest.get().sortOrder === A) ? D : (key === this.paginationRequest.get().sortColumn && this.paginationRequest.get().sortOrder === D) ? A : D;
    this.paginationRequest.update(a => ({ ...a, sortColumn: key, sortOrder: sortOrder }));
    this.getAllCustomerLead();
  }

  addPageSizeData(event) {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
    this.getAllCustomerLead();
  }

  pageNumber(event) {
    this.paginationRequest.update(a => ({ ...a, pageNo: event }));
    this.getAllCustomerLead();
  }

  // Select Deselect
  selectAll($event: any) {
    for (const item of this.customerLeadList.get()) {
      const index = this.customerLeadList.get().indexOf(item);
      this.selectUnselect(index, $event);
    }
  }

  selectUnselect(index: number, $event: any) {
    this.customerLeadList.updateAt(index, item => ({ ...item, isSelected: $event }));
    this.flagForSelectAll.set(this.checkIfAllSelected());
  }

  // redirect to registration add
  redirectToRegAdd = (id: number) => {
    this.utilsService.redirectTo(`/users/registration/edit-registration/${id}`);
  }

}

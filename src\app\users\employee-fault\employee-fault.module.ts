import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EmployeeFaultComponent } from './employee-fault.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { EmployeeFaultListComponent } from './employee-fault-list/employee-fault-list.component';
import { NewEmployeeFaultComponent } from './new-employee-fault/new-employee-fault.component';


const routes: Routes = [
  { path: '', redirectTo: 'employee-fault', pathMatch: 'full' },
  { path: '', component: EmployeeFaultListComponent },
  { path: 'new-employee-fault', component: NewEmployeeFaultComponent },

];

@NgModule({
  imports: [
    CommonModule,
        RouterModule.forChild(routes),
        SharedModule.forRoot()
  ],
  declarations: [EmployeeFaultComponent, EmployeeFaultListComponent, NewEmployeeFaultComponent]
})
export class EmployeeFaultModule { }

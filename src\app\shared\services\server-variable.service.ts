import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ServerVariableService {

  STRING_WHEN_NO_RECORDS_FOUND = 'No records found.';
  PATH_FOR_API = '/api';

  // AUTH
  LOGIN_API = this.PATH_FOR_API + '/auth/login';
  REGISTRATION_API = this.PATH_FOR_API + '';

  // FORGET PASSWORD 
  OTP_GENERATION = this.PATH_FOR_API + '/auth/forgotPwdSendEmail'
  OTP_VERIFICATION = this.PATH_FOR_API + '/auth/forgotPwdOtpVerification'
  CHANGE_PASSWORD_FORGOT = this.PATH_FOR_API + '/auth/forgotPwd'

  //REFRESH TOKEN
  REFRESH_TOKEN = this.PATH_FOR_API + '/auth/refreshToken';

  //COLOR
  COLOR_LISTING = this.PATH_FOR_API + '/color/page';
  SAVE_EDIT_DELETE = this.PATH_FOR_API + '/color/';
  COLOR_STATUS_CHANGE = this.PATH_FOR_API + '/color/setTypeFlag/';
  COLOR_EXPORT = this.PATH_FOR_API + '/color/excelExport'

  //UNIT
  UNIT_LISTING = this.PATH_FOR_API + '/unit/page';
  UNIT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/unit/';
  UNIT_STATUS_CHANGE = this.PATH_FOR_API + '/unit/setTypeFlag/';
  UNIT_EXPORT = this.PATH_FOR_API + '/unit/excelExport';
  UNIT_ALL_CATEGORY = this.PATH_FOR_API + '/unit/getAllUnitCategory';

  //SIZE
  SIZE_LISTING = this.PATH_FOR_API + '/size/page';
  SIZE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/size/';
  SIZE_STATUS_CHANGE = this.PATH_FOR_API + '/size/setTypeFlag/';
  SIZE_EXPORT = this.PATH_FOR_API + '/size/excelExport'

  //HSN
  HSN_LISTING = this.PATH_FOR_API + '/hsn/page';
  HSN_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/hsn/';
  HSN_STATUS_CHANGE = this.PATH_FOR_API + '/hsn/setTypeFlag/';
  HSN_DROPDOWN_DATA = this.PATH_FOR_API + '/hsn/requiredDataForHsnCode';
  HSN_BY_ID = this.PATH_FOR_API + '/hsn/getDataById';
  HSN_EXPORT = this.PATH_FOR_API + '/hsn/excelExport'
  HSN_IMPORT = this.PATH_FOR_API + '/hsn/uploadExcel'

  //PACKING TYPE
  PACKING_LIST = this.PATH_FOR_API + '/packing/page';
  PACKING_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/packing/';
  PACKING_STATUS_CHANGE = this.PATH_FOR_API + '/packing/setTypeFlag/';
  PACKING_EXPORT = this.PATH_FOR_API + '/packing/excelExport'

  //TAXES COMPLIANCE
  TAXES_COMP_LISTING = this.PATH_FOR_API + '/taxesCompliance/page';
  TAXES_COMP_STATUS_CHANGE = this.PATH_FOR_API + '/taxesCompliance/setTypeFlag/';
  TAXES_COMP_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/taxesCompliance/'
  TAX_REPORT = this.PATH_FOR_API + '/taxesCompliance/excelExport'

  //BANK GROUP
  BANKGROUP_LISTING = this.PATH_FOR_API + '/bankGroup/page'
  BANKGROUP_STATUS_CHANGE = this.PATH_FOR_API + '/bankGroup/setTypeFlag/'
  BANKGROUP_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/bankGroup/'
  BANKGROUP_REPORT = this.PATH_FOR_API + '/bankGroup/excelExport'
  BANKGROUP_DATA_BY_ID = this.PATH_FOR_API + '/bankGroup/getDataById'

  // COUNTRY STATE CITY
  COUNTRY_LISTING = this.PATH_FOR_API + '/country/page';
  STATE_LISTING = this.PATH_FOR_API + '/state/page';
  CITY_LISTING = this.PATH_FOR_API + '/city/page';
  COUNTRY_STATUS = this.PATH_FOR_API + '/country/updateStatus/';
  COUNTRY_BY_ID = this.PATH_FOR_API + '/country/byID'
  STATE_BY_ID = this.PATH_FOR_API + '/state/byID';
  STATE_STATUS = this.PATH_FOR_API + '/state/updateStatus/';
  CITY_STATUS = this.PATH_FOR_API + '/city/updateStatus/';
  SAVE_COUNTRY = this.PATH_FOR_API + '/country/';
  SAVE_STATE = this.PATH_FOR_API + '/state/';
  SAVE_CITY_STATE = this.PATH_FOR_API + '/city/';
  COUNTRY_STATE_CITY_DROPDOWN = this.PATH_FOR_API + '/traveller/requiredDataForTraveller'
  REPORT_EXPORT = this.PATH_FOR_API + '/country/excelExport'
  S_REPORT_EXPORT = this.PATH_FOR_API + '/state/excelExport'
  CITY_REPORT_EXPORT = this.PATH_FOR_API + '/city/excelExport'

  //TICKET MASTER
  TICKET_LISTING = this.PATH_FOR_API + '/ticket/page';
  TICKET_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/ticket/';
  TICKET_STATUS_CHANGE = this.PATH_FOR_API + '/ticket/setTypeFlag/';
  TICKET_EXPORT = this.PATH_FOR_API + '/ticket/excelExport'

  //COURIER COMPANY
  COURIER_LISTING = this.PATH_FOR_API + '/courier/page';
  COURIER_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/courier/';
  COURIER_STATUS_CHANGE = this.PATH_FOR_API + '/courier/setTypeFlag/';
  COURIER_EXPORT = this.PATH_FOR_API + '/courier/excelExport'
  COURIER_DATA_BY_ID = this.PATH_FOR_API + '/courier/getDataById'

  //MARKET TYPE MASTER
  MARKET_LISTING = this.PATH_FOR_API + '/market/page';
  MARKET_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/market/';
  MARKET_STATUS_CHANGE = this.PATH_FOR_API + '/market/setTypeFlag/';
  MARKET_EXPORT = this.PATH_FOR_API + '/market/excelExport'

  //PAYMENT TERMS TYPE MASTER
  PT_LISTING = this.PATH_FOR_API + '/paymentTerms/page';
  PT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/paymentTerms/';
  PT_STATUS_CHANGE = this.PATH_FOR_API + '/paymentTerms/setTypeFlag/';
  PT_EXPORT = this.PATH_FOR_API + '/paymentTerms/excelExport';

  //PAYMENT TYPE TYPE MASTER
  PAYMENT_TYPE_LISTING = this.PATH_FOR_API + '/paymentType/page';
  PAYMENT_TYPE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/paymentType/';
  PAYMENT_TYPE_STATUS_CHANGE = this.PATH_FOR_API + '/paymentType/setTypeFlag/';
  PAYMENT_TYPE_EXPORT = this.PATH_FOR_API + '/paymentType/excelExport';

  //CURRENCY TYPE MASTER
  CURRENCY_LISTING = this.PATH_FOR_API + '/currency/page';
  CURRENCY_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/currency/';
  CURRENCY_STATUS_CHANGE = this.PATH_FOR_API + '/currency/setTypeFlag/';
  CURRENCY_EXPORT = this.PATH_FOR_API + '/currency/excelExport';

  //SEASON TYPE MASTER
  SEASON_LISTING = this.PATH_FOR_API + '/season/page';
  SEASON_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/season/';
  SEASON_STATUS_CHANGE = this.PATH_FOR_API + '/season/setTypeFlag/';
  SEASON_EXPORT = this.PATH_FOR_API + '/season/excelExport';

  //SALES ORDER TYPE MASTER
  SALES_OT_LISTING = this.PATH_FOR_API + '/salesOrderType/page';
  SALES_OT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/salesOrderType/';
  SALES_OT_STATUS_CHANGE = this.PATH_FOR_API + '/salesOrderType/updateStatus/';
  SALES_OT_EXPORT = this.PATH_FOR_API + '/salesOrderType/excelExport';

  //DEPARTMENT TYPE MASTER
  DEPT_LISTING = this.PATH_FOR_API + '/department/page';
  DEPT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/department/';
  DEPT_STATUS_CHANGE = this.PATH_FOR_API + '/department/updateStatus/';
  DEPT_EXPORT = this.PATH_FOR_API + '/department/excelExport';

  //EXPANSE TYPE TYPE MASTER
  EXP_LISTING = this.PATH_FOR_API + '/expenseType/page';
  EXP_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/expenseType/';
  EXP_STATUS_CHANGE = this.PATH_FOR_API + '/expenseType/expenseTypeFlag/';
  EXP_EXPORT = this.PATH_FOR_API + '/expenseType/excelExport';

  //REASON MASTER
  REASON_LISTING = this.PATH_FOR_API + '/reason/page';
  REASON_STATUS_CHANGE = this.PATH_FOR_API + '/reason/setTypeFlag/';
  REASON_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/reason/'
  REASON_REPORT = this.PATH_FOR_API + '/reason/excelExport'

  //TRAVELLER COMPANY
  TRAVELLER_LISTING = this.PATH_FOR_API + '/traveller/page';
  TRAVELLER_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/traveller/';
  TRAVELLER_STATUS_CHANGE = this.PATH_FOR_API + '/traveller/setTypeFlag/';
  TRAVELLER_EXPORT = this.PATH_FOR_API + '/traveller/excelExport'
  TRAVELLER_DATA_BY_ID = this.PATH_FOR_API + '/traveller/getDataById'

  //IMPORTER COMPANY
  IMPORTER_LISTING = this.PATH_FOR_API + '/importer/page';
  IMPORTER_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/importer/';
  IMPORTER_STATUS_CHANGE = this.PATH_FOR_API + '/importer/setTypeFlag/';
  IMPORTER_EXPORT = this.PATH_FOR_API + '/importer/excelExport'
  IMPORTER_DATA_BY_ID = this.PATH_FOR_API + '/importer/getDataById'

  //TRANSPORT COMPANY
  TRANSPORT_LISTING = this.PATH_FOR_API + '/transport/page';
  TRANSPORT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/transport/';
  TRANSPORT_STATUS_CHANGE = this.PATH_FOR_API + '/transport/setTypeFlag/';
  TRANSPORT_EXPORT = this.PATH_FOR_API + '/transport/excelExport'
  TRANSPORT_DROPDOWN = this.PATH_FOR_API + '/transport/requiredData'
  BRANCH_TRANSPORT_STATUS = this.PATH_FOR_API + '/branchTransport/setTypeFlag/';
  BRANCH_TRANSPORT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/branchTransport/';
  BRANCH_TRANSPORT_DATA_BY_ID = this.PATH_FOR_API + '/branchTransport/getDataById'

  //QC COMPANY
  QC_LISTING = this.PATH_FOR_API + '/checklist/page';
  QC_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/checklist/';
  QC_STATUS_CHANGE = this.PATH_FOR_API + '/checklist/setTypeFlag/';
  QC_EXPORT = this.PATH_FOR_API + '/checklist/excelExport'
  QC_DATA_BY_ID = this.PATH_FOR_API + '/checklist/getDataById'
  QC_DROPDOWN = this.PATH_FOR_API + '/checklist/requiredData'
  QC_ASSOCIATE_ITEM = this.PATH_FOR_API + '/item/associateItemsPage'
  QC_ITEM_DROPDOWN = this.PATH_FOR_API + '/item/requiredDataForQcCheckList'

  //LEVEL MASTER
  LEVEL_LISTING = this.PATH_FOR_API + '/level/page';
  LEVEL_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/level/';
  LEVEL_STATUS_CHANGE = this.PATH_FOR_API + '/level/updateStatus/';
  LEVEL_EXPORT = this.PATH_FOR_API + '/level/excelExport'
  LEVEL_DATA_BY_ID = this.PATH_FOR_API + '/level/requiredData'
  LEVEL_SORT_ORDER = this.PATH_FOR_API + '/level/updateSortOrder';

  //BRANCH
  BRANCH_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/branch/';
  BRANCH_MARK_AS_PRIM = this.PATH_FOR_API + '/branch/markPrimary/'
  BRANCH_STATUS_CHANGE = this.PATH_FOR_API + '/branch/updateStatus/'
  BRANCH_DATA_BY_ID = this.PATH_FOR_API + '/branch/requiredData'
  BRANCH_ORDER = this.PATH_FOR_API + '/branch/updateSortOrder'

  //USERS
  USER_LISTING = this.PATH_FOR_API + '/user/page';
  USER_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/user/';
  USER_MARK_AS_ABSENT = this.PATH_FOR_API + '/user/markAbsent/';
  USER_STATUS_CHANGE = this.PATH_FOR_API + '/user/updateStatus/';
  USER_DATA_BY_ID = this.PATH_FOR_API + '/user/requiredData';
  USER_EXPORT = this.PATH_FOR_API + '/user/excelExport';
  USER_EXTEND_TIME = this.PATH_FOR_API + '/user/extendTime';
  USER_REQ_TIME_LIST = this.PATH_FOR_API + '/user/approveRequestListing';
  USER_EXTEND_TIME_APPROVE = this.PATH_FOR_API + '/user/approveRequest';
  GET_ALL_USER_DROPDOWN_DATA_BY_ACTIVE_STATUS = this.PATH_FOR_API + '/user/getAllUserDropDownDataByActiveStatus';

  //ROLES
  ROLES_LISTING = this.PATH_FOR_API + '/role/page';
  ROLES_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/role/';
  ROLES_CLONE = this.PATH_FOR_API + '/role/cloneUserRole/';
  ROLES_STATUS_CHANGE = this.PATH_FOR_API + '/role/updateStatus/';
  ROLES_DATA_BY_ID = this.PATH_FOR_API + '/role/requiredData';
  ROLES_EXPORT = this.PATH_FOR_API + '/role/excelExport';

  //CODE SUBCODE
  CODE_LISTING = this.PATH_FOR_API + '/code/page';
  CODE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/code/';
  CODE_STATUS_CHANGE = this.PATH_FOR_API + '/code/codeFlag/';
  CODE_EXPORT = this.PATH_FOR_API + '/code/excelExport';

  //SUB CODE
  CODESUB_LISTING = this.PATH_FOR_API + '/subCode/page';
  CODESUB_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/subCode/';
  CODESUB_STATUS_CHANGE = this.PATH_FOR_API + '/subCode/subCodeFlag/';
  CODESUB_EXPORT = this.PATH_FOR_API + '/subCode/excelExport';
  GET_CODE_DROPDOWN = this.PATH_FOR_API + '/subCode/requiredData';

  // INV - CATEGORY
  CATEGORY_LISTING = this.PATH_FOR_API + '/category/page';
  CATEGORY_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/category/';
  CATEGORY_DATA_BY_ID = this.PATH_FOR_API + '/category/requiredData';
  CATEGORY_DROPDOWN = this.PATH_FOR_API + '/category/requiredDataForCategory'
  CATEGORY_EXPORT = this.PATH_FOR_API + '/category/excelExport';
  CATEGORY_CHANGE = this.PATH_FOR_API + '/category/setTypeFlag/';

  // INV - ITEM-GROUPS
  IG_LISTING = this.PATH_FOR_API + '/itemGroup/page';
  IG_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/itemGroup/';
  IG_DROPDOWN = this.PATH_FOR_API + '/itemGroup/requiredDataForItemsGroup';
  IG_DATA_BY_ID = this.PATH_FOR_API + '/itemGroup/getDataById';
  IG_EXPORT = this.PATH_FOR_API + '/itemGroup/excelExport';
  IG_CHANGE_STATUS = this.PATH_FOR_API + '/itemGroup/updateStatus/';

  // WAREHOUSE 
  WAREHOUSE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/warehouse/';
  WAREHOUSE_STATUS = this.PATH_FOR_API + '/warehouse/updateStatus/';
  WAREHOUSE_MARK_AS_PRIMARY = this.PATH_FOR_API + '/warehouse/updateMainWarehouse/';
  WAREHOUSE_DETAILS = this.PATH_FOR_API + '/warehouse/getWareHouseDetails';
  WAREHOUSE_DROPDOWN_GET = this.PATH_FOR_API + '/warehouse/requiredData';
  WAREHOUSE_SORTORDER = this.PATH_FOR_API + '/warehouse/updateSortOrder';
  WAREHOUSE_EMP_DEL = this.PATH_FOR_API + '/warehouse/wareHouseUserMappingDelete'

  DROPOFF_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/dropLocation/';
  DROPOFF_STATUS = this.PATH_FOR_API + '/dropLocation/updateStatus/';
  DROPOFF_SORT_ORDER = this.PATH_FOR_API + '/dropLocation/updateSortOrder'
  DROPOFF_QR = this.PATH_FOR_API + '/dropLocation/getQrCode'

  AISLE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/aisle/';
  AISLE_SORT_ORDER = this.PATH_FOR_API + '/aisle/updateSortOrder'
  AISLE_GET_DATA_BY_ID = this.PATH_FOR_API + '/aisle/getDataById'
  AISLE_QR = this.PATH_FOR_API + '/aisle/getQrCode'

  RACKS_SORT_ORDER = this.PATH_FOR_API + '/rack/updateSortOrder';
  RACKS_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/rack/';
  RACK_REQ_DATA = this.PATH_FOR_API + '/rack/requiredData';
  RACK_QR = this.PATH_FOR_API + '/rack/getQrCode'
  
  // Account-type
  ACCOUNT_TYPE_LISTING = this.PATH_FOR_API + '/accountType/page';
  ACCOUNT_TYPE_STATUS_CHANGE = this.PATH_FOR_API + '/accountType/typeFlag/';
  ACCOUNT_TYPE_DEFAILT_CHANGE = this.PATH_FOR_API + '/accountType/typeDefault/';
  ACCOUNT_TYPE_REQUIRED_DATA = this.PATH_FOR_API + '/accountType/requiredData';
  ACCOUNT_TYPE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/accountType/';
  
  
  //Chart of accounts
  CHART_OF_ACCOUNTS_LISTING = this.PATH_FOR_API + '/accountChart/page';
  CHART_OF_ACCOUNT_STATUS_CHANGE = this.PATH_FOR_API + '/accountChart/accountFlag/';
  CHART_OF_ACCOUNT_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/accountChart/';
  CHART_OF_ACCOUNT_REQUIRED_DATA = this.PATH_FOR_API + '/accountChart/requiredData';

  //ITEMS
  ITEMS_LISTING = this.PATH_FOR_API + '/item/page';
  ITEM_LISTING_DROPDOWN = this.PATH_FOR_API + '/item/requiredDataForItemPage';
  ITEMS_TABLE_SALES_PRICE = this.PATH_FOR_API + '/item/updateSalesPrice';
  ITEMS_STATUS = this.PATH_FOR_API + '/item/typeFlag/';
  HEADER_GET_SAVE_API = this.PATH_FOR_API + '/header/'
  ITEM_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/item/';
  ITEMS_EXPORT = this.PATH_FOR_API + '/item/excelExport'
  ITEM_REQ_DATA = this.PATH_FOR_API + '/item/requiredDataForItems';
  ITEM_BY_ID = this.PATH_FOR_API + '/item/getDataById'
  ITEM_DETAILS = this.PATH_FOR_API + '/item/getItemDetailsById'
  ITEM_IMAGE_BY_ID = this.PATH_FOR_API + '/item/getItemDocById'
  ITEM_IMPORT = this.PATH_FOR_API + '/item/import'
  GET_PURCAHSE_HISTORY = this.PATH_FOR_API + '/poHistory/purchaseHistory'
  GET_SALES_HISTORY = this.PATH_FOR_API + '/sales/salesHistory'
  EXPORT_SALES_HISTORY = this.PATH_FOR_API + '/sales/exportSalesHistory'
  EXPORT_PURCHASE_HISTORY = this.PATH_FOR_API + '/poHistory/exportPurchaseHistory'

  //MANUAL JOURNAL ENTRY
  MANUAL_JOURNAL_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/manualJournal/';
  MANUAL_JOURNAL_REQ_DATA = this.PATH_FOR_API + '/manualJournal/requiredData';
  MANUAL_JOURNAL_GET_DATA_BY_ID = this.PATH_FOR_API + '/manualJournal/getDataById';
  MANUAL_JOURNAL_LISTING = this.PATH_FOR_API + '/manualJournal/page'

  //SIDEBAR BRANCH APIs
  USER_BRANCHES = this.PATH_FOR_API + '/user/branches'
  SAVE_USER_BRANCHES = this.PATH_FOR_API + '/user/saveUserBranch'

  //REGISTRATION
  REG_LISTING = this.PATH_FOR_API + '/registration/page';
  REG_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/registration/';
  REG_EXPORT = this.PATH_FOR_API + '/registration/excelExport';
  REG_REQUIRED_DATA = this.PATH_FOR_API + '/registration/requiredData';
  REG_DELETE_DOC = this.PATH_FOR_API + '/registration/deleteRegistrationDoc';
  REG_LIST_DROPDOWN = this.PATH_FOR_API + '/registration/requiredDataForPage';
  REG_DETAILS = this.PATH_FOR_API + '/registration/getRegistrationDetail'
  REG_STATUS_CHANGE = this.PATH_FOR_API + '/registration/registrationFlag';
  REG_SA_STATUS_CHANGE = this.PATH_FOR_API + '/registration/shippingAddressFlag';
  REG_GST_STATUS_CHANGE = this.PATH_FOR_API + '/registration/gstFlag';
  REG_ITEM_STATUS_CHANGE = this.PATH_FOR_API + '/registration/associateItemFlag';

  //ITEM DROPDOWN
  GET_ALL_ITEMS_DROPDOWN = this.PATH_FOR_API + '/item/getAllItem'

  //SETTINGS
  SETTINGS_GET_SAVE = this.PATH_FOR_API + '/setting/'
  SETTINGS_GENERAL_SAVE = this.PATH_FOR_API + '/setting/generalSetting'
  SETTINGS_GENERAL_GET = this.PATH_FOR_API + '/setting/getGeneralSettingData'
  SETTINGS_REQ_DATA = this.PATH_FOR_API + '/setting/generalSettingReqData'

  //PURCHASE
  REQ_DATA_PO_IMPORT = this.PATH_FOR_API + '/poImport/requiredDataForPoImport';
  PAGE_REQ_DATA = this.PATH_FOR_API + '/poTempo/PageRequiredData'
  PO_IMPORT_SAVE = this.PATH_FOR_API + '/poImport/'
  PO_IMPORT_DEL = this.PATH_FOR_API + '/poImport/'
  PO_CREATED_DEL = this.PATH_FOR_API + '/poImportCreate/'
  PO_REC_CHINA_DEL = this.PATH_FOR_API + '/poImportReceivedCh/'
  PO_REC_CHINA_GET_DATA = this.PATH_FOR_API + '/poImportReceivedCh/getDataById'
  PO_REC_CHINA_LOADED_REQ_DATA = this.PATH_FOR_API + '/poLoaded/requiredDataForLoaded'
  PO_LOADING_CONTAINER_EDIT_ADD_NEW = this.PATH_FOR_API + '/poLoaded/getReceivedItemForLoaded'
  PO_IMPORT_LISTING = this.PATH_FOR_API + '/poImport/page'
  PO_IMPORT_GET_DATA = this.PATH_FOR_API + '/poImport/getDataById'
  PO_CREATED_LISTING = this.PATH_FOR_API + '/poImportCreate/pagePoCreate'
  PO_CREATED_GET_DATA = this.PATH_FOR_API + '/poImportCreate/getDataById'
  PO_CREATED_TO_RC_CHINA = this.PATH_FOR_API + '/poImportCreate/poCreateTORecCH'
  PO_IMPORT_DRAFT_POIMPORT = this.PATH_FOR_API + '/poImport/changeStatusDraftToPoCreate'
  PO_BACK_TO_DRAFT = this.PATH_FOR_API + '/poImport/backToDraft'
  PO_RECEIVE_CHINA_LISTING = this.PATH_FOR_API + '/poImportReceivedCh/page'
  PO_REC_CHINA_LOADED_SAVE = this.PATH_FOR_API + '/poImportReceivedCh/receivedChToLoaded'
  PO_EDIT_CONTAINER = this.PATH_FOR_API + '/poLoaded/updatePoContainer'
  PO_GET_ITEM_IMAGES = this.PATH_FOR_API + '/poImport/getAssociatedItemDocById'

  PO_LOADED_LISTING = this.PATH_FOR_API + '/poLoaded/page'
  PO_RELEASE_LISTING = this.PATH_FOR_API + '/released/page'
  LOADING_CONTAINER_PAGE = this.PATH_FOR_API + '/poContainer/page'
  LOADING_EXPORT = this.PATH_FOR_API +'/poContainer/excelExport'
  LOADED_TO_RELEASE = this.PATH_FOR_API + '/poLoaded/loadedToReleased'

  PO_ITEM_DEL = this.PATH_FOR_API + '/poImport/deletePoImportItem'
  PO_RC_ITEM_DEL = this.PATH_FOR_API + '/poImportReceivedCh/deletePoImportItem'

  PO_TEMPO_PAGE = this.PATH_FOR_API + '/poTempo/page'
  PO_TEMPO_SAVE_DEL = this.PATH_FOR_API + '/poTempo/'
  PO_TEMPO_MARK_AS_COMPLETED = this.PATH_FOR_API + '/poTempo/markIsCompleted'

  PO_RC_CHINA_CHANGE_STATUS = this.PATH_FOR_API + '/poImportReceivedCh/changedStatus'
  PO_LOADED_CHANGE_STATUS = this.PATH_FOR_API + '/poLoaded/changedStatus'
  PO_CREATE_CHANGE_STATUS = this.PATH_FOR_API + '/poImportCreate/changedStatus'

  PO_RELEASE_LOADED_PACKING_DOWNLOAD = this.PATH_FOR_API + '/poLoaded/pdf'
  PO_CREATED_PDF = this.PATH_FOR_API + '/poImportCreate/pdf'

  PO_CARTON_MAPPING_BRANCH_REQ = this.PATH_FOR_API + '/cartonMapping/requiredDataForBranch';
  PO_CARTON_MAPPING_BRANCH_SAVE = this.PATH_FOR_API + '/cartonMapping/saveBranchCartonMapping';
  PO_CARTON_MAPPING_WAREHOUSE_REQ = this.PATH_FOR_API + '/cartonMapping/requiredDataForWarehouse';
  PO_CARTON_MAPPING_WAREHOUSE_SAVE = this.PATH_FOR_API + '/cartonMapping/saveWarehouseCartonMapping';
  PO_CARTON_MAPPING_AR_REQ = this.PATH_FOR_API + '/cartonMapping/requiredDataForAisleRackMapping';
  PO_CARTON_MAPPING_AR_SAVE = this.PATH_FOR_API + '/cartonMapping/saveAisleRackCartonMapping';

  PO_QR_DOWNLOAD = this.PATH_FOR_API + '/QR/print'
  PRINT_QR_CODE = this.PATH_FOR_API + '/QR/printQrCode'

  PO_START_GRN = this.PATH_FOR_API + '/grn/startGRN'
  PO_GRN_MANUAL_SAVE = this.PATH_FOR_API + '/grn/saveGrnItem'
  PO_GRN_REQ_DATA = this.PATH_FOR_API + '/grn/requiredDataForGrn'
  PO_GRN_LISTING = this.PATH_FOR_API + '/grn/page'
  GET_ITEM_IMAGE_FROMGRN = this.PATH_FOR_API + '/grn/getImageDataForLink'
  SAVE_ITEM_LINK_IMG = this.PATH_FOR_API + '/grn/saveGroupLink'
  PREVIEW_MARKA_IMG = this.PATH_FOR_API + '/grn/downloadLink'
  STOCK_DETAILS_EXPORT = this.PATH_FOR_API + '/item/stockDetailsExcelExport'

  PO_ASSIGN_TO_CHA = this.PATH_FOR_API + '/released/assignToCHA'
  PO_REQ_ASSIGN_TO_CHA = this.PATH_FOR_API + '/released/assignToCHARequiredDta'
  PO_GET_DATA_MARK_AS_COMPLETED = this.PATH_FOR_API + '/released/completeRequiredData'
  PO_MOVE_TO_COMPLETED = this.PATH_FOR_API + '/released/moveToCompleted'

  PO_COMPLETED_LISTING = this.PATH_FOR_API + '/released/completedPage'

  // EXPENSES
  REQ_TEMPO_EXP =  this.PATH_FOR_API + '/expense/requiredDataForTempoExpense'
  TEMPO_EXP_SAVE_DEL = this.PATH_FOR_API + '/expense/'
  TEMPO_EXP_LISTING = this.PATH_FOR_API + '/expense/page';
  TEMPO_EXP_DOC_DELETE = this.PATH_FOR_API + '/expense/document';
  TEMPO_EXP_EXPORT = this.PATH_FOR_API + '/expense/excelExport';

  REQ_CONTAINER_EXP = this.PATH_FOR_API + '/container/expense/requiredDataForContainerExpense'
  CONTAINER_EXP_SAVE_DEL = this.PATH_FOR_API + '/container/expense/'
  CONTAINER_EXP_LISTING = this.PATH_FOR_API + '/container/expense/page';
  CONTAINER_EXP_EXPORT = this.PATH_FOR_API + '/container/expense/excelExport';
  CONTAINER_EXP_APPROVE = this.PATH_FOR_API + '/container/expense/approve';
  CONTAINER_EXP_DECLINE = this.PATH_FOR_API + '/container/expense/cancel';

  // GROUP_CODE
  GROUP_CODE_LISTING = this.PATH_FOR_API + '/groupCode/page';
  GROUP_CODE_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/groupCode/';
  GROUP_CODE_DATA_BY_ID = this.PATH_FOR_API + '/groupCode/requiredData';
  GROUP_CODE_EXPORT = this.PATH_FOR_API + '/groupCode/excelExport';
  GROUP_CODE_CHANGE_STATUS = this.PATH_FOR_API + '/groupCode/updateStatus/';
  GROUP_CODE_DROPDOWN = this.PATH_FOR_API + '/category/getAll'

  // AVERAGE PRICE
  AVERAGE_PRICE_REQ_DATA = this.PATH_FOR_API + '/avgPrice/requiredData'
  AVERAGE_PRICE_LISTING = this.PATH_FOR_API + '/avgPrice/'
  AVG_PRICE_EXPORT = this.PATH_FOR_API + '/avgPrice/downloadExcel'
  AVG_PRICE_STAFF_SAVE = this.PATH_FOR_API + '/avgPrice/saveStaffSp'
  AVG_PRICE_APPROVE = this.PATH_FOR_API + '/avgPrice/approveNewSp'

  // PAYMENTS
  PAYMENTS_LISTING = this.PATH_FOR_API + '/payment/page'
  PAYMENTS_EXPORT = this.PATH_FOR_API + '/payment/excelExport'
  PAYMENTS_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/payment/'
  PAYMENTS_REQ_DATA = this.PATH_FOR_API + '/payment/requiredData'
  PAYMENT_PAGE_REQ = this.PATH_FOR_API + '/payment/requiredDataForPage'
  PAYMENTS_APPROVE = this.PATH_FOR_API + '/payment/approve';
  PAYMENTS_DECLINE = this.PATH_FOR_API + '/payment/cancel';

  // CUSTOMER LEAD
  CUSTOMER_LEAD_LISTING = this.PATH_FOR_API + '/customerLead/page'
  CUSTOMER_LEAD_EXPORT = this.PATH_FOR_API + '/customerLead/excelExport'
  CUSTOMER_LEAD_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/customerLead/'
  CUSTOMER_LEAD_REQ_DATA = this.PATH_FOR_API + '/customerLead/getRequiredDataForSaveCustomerLead'

  // INQUIRY
  INQUIRY_LISTING = this.PATH_FOR_API + '/inquiry/page'
  INQUIRY_BY_CUSTOMER_ID = this.PATH_FOR_API + '/inquiry/getInquiryItemByCustomerId'
  INQUIRY_EXPORT = this.PATH_FOR_API + '/inquiry/excelExport'
  INQUIRY_SAVE_EDIT_DELETE = this.PATH_FOR_API + '/inquiry/'
  INQUIRY_REQ_DATA = this.PATH_FOR_API + '/inquiry/getRequiredData'
  INQUIRY_UPLOAD_CSV = this.PATH_FOR_API + '/inquiry/getCsvData'
  INQUIRY_GROUP_CODE_ASSOCIATED_ITEMS = this.PATH_FOR_API + '/inquiry/getGroupCodeAssociatedItems'
  INQUIRY_UPDATE_SALES_MANAGER_PRICE = this.PATH_FOR_API + '/inquiry/updateSalesManagerPrice'
  INQUIRY_PAGE_REQ_DATA = this.PATH_FOR_API + '/inquiry/pageRequiredData'
  INQUIRY_MARKA_STOCK_QUANTITY = this.PATH_FOR_API + '/inquiry/getMarkaStockQuantity'
  INQUIRY_PAGE_MAPPED_IMAGE = this.PATH_FOR_API + '/inquiry/pageMappedImage'
  INQUIRY_SALES_DELETE = this.PATH_FOR_API + '/inquiry/deleteCustomerInquiry'
  INQUIRY_UPLOAD_ITEM_IMAGE = this.PATH_FOR_API + '/inquiry/uploadItemImage'
  INQUIRY_DELETE_MAPPED_IMAGE_BY_ITEM_ID = this.PATH_FOR_API + '/inquiry/deleteMappedImageByItemId'
  INQUIRY_GET_IMG_INQ_TAB = this.PATH_FOR_API + '/inquiry/getAllInquiryItemDoc'
  INQUIRY_MAPPED_IMAGE_REQ_DATA = this.PATH_FOR_API + '/inquiry/mappedImageRequiredData'
  INQUIRY_SAVE_MAPPED_IMAGE_WITH_ITEM = this.PATH_FOR_API + '/inquiry/saveMappedImageWithItem'
  INQUIRY_CLEAR_MAPPED_IMAGE = this.PATH_FOR_API + '/inquiry/clearMappedImage'

  // SO CREATE
  SO_REQUIRED_DATA = this.PATH_FOR_API + '/sales/requiredData'
  SO_GET_ITEM_STOCK = this.PATH_FOR_API + '/sales/getItemStock'
  SO_SALES_ORDER_SAVE = this.PATH_FOR_API + '/sales/'
  SO_DRAFT_PAGE = this.PATH_FOR_API + '/sales/draftPage'
  SO_DRAFT_EXPORT = this.PATH_FOR_API + '/sales/draftExport'
  SO_HOLD_RELEASE = this.PATH_FOR_API + '/sales/releaseHold'
  SO_EXTEND_HOLD = this.PATH_FOR_API + '/sales/extendHold'
  SO_DELETE_ITEM_LIST = this.PATH_FOR_API + '/sales/deleteItem/'
  SO_CANCEL_SO = this.PATH_FOR_API + '/sales/cancelSalesOrder/'
  SO_BACK_TO_DRAFT = this.PATH_FOR_API + '/sales/backToDraft/'

  // STOCK SHIFTING
  STOCK_SHIFTING_SAVE = this.PATH_FOR_API + '/stockShifting/saveStockShifting'
  REQ_STOCK_SHIFTING_DATA = this.PATH_FOR_API + '/stockShifting/requiredDataForSaveStockShifting'
  AVAILABLE_QTY_OF_ITEM_AT_LOCATION = this.PATH_FOR_API + '/stockShifting/availableQtyOfItemAtLocation'
  AVAILABLE_QTY_OF_ITEM_WISE = this.PATH_FOR_API + '/auditTicket/availableQtyByItem'
  GET_ALL_AISLE_DROPDOWN_DATA_BY_WAREHOUSE = this.PATH_FOR_API + '/aisle/getAllAisleDropDownDataByWarehouse'
  GET_ALL_RACK_DROPDOWN_DATA_BY_AISLE = this.PATH_FOR_API + '/rack/getAllRackDropDownDataByAisle'
  STOCK_SHIFTING_PAGE = this.PATH_FOR_API + '/stockShifting/getStockShiftingPage'
  STOCK_SHIFING_ASSOCIATED_ITEM = this.PATH_FOR_API + '/stockShifting/getStockShiftingAssociatedItem'
  GET_STOCK_SHIFING_ASSOCIATED_ITEM_QTY = this.PATH_FOR_API + '/stockShifting/getStockShiftingAssociatedItemQty'
  GET_TICKETS_STATUS_BY_TICKET_TYPE_AND_ASSOCIATED_ITEM = this.PATH_FOR_API + '/tickets/getTicketsStatusByTicketTypeAndAssociatedItem'
  DELETE_STOCK_SHIFING = this.PATH_FOR_API + '/stockShifting/deleteStockShiftingById'
  GET_ALISE_RACK_OF_ITEM_IN_WAREHOUSE = this.PATH_FOR_API + '/stockShifting/getAliseRackOfItemInWarehouse'

  //AUDIT TICKETS
  REQUIRED_DATA_AUDIT_TICKET = this.PATH_FOR_API + '/auditTicket/requiredData'
  SAVE_AUDIT_TICKET = this.PATH_FOR_API + '/auditTicket/'
  FETCH_TICKETS = this.PATH_FOR_API + '/auditTicket/fetchTickets'
  AUDIT_TICKET_DROPDOWN = this.PATH_FOR_API + '/auditTicket/getFilterData'
  AUDIT_TICKET_DELETE = this.PATH_FOR_API + '/auditTicket/deleteTickets'
  AUDIT_TICKET_APPROVE = this.PATH_FOR_API + '/auditTicket/markAsResolved'
  AUDIT_TICKET_EXPORT = this.PATH_FOR_API + '/auditTicket/exportAuditTickets'
  GET_AUDIT_TICKET_DATA = this.PATH_FOR_API + '/auditTicket/getAuditTicketForEdit'
  RE_ASSIGN_AUDIT_TICKET = this.PATH_FOR_API + '/tickets/reassignTicket'
  ASSIGN_TICKET_DETAILS = this.PATH_FOR_API + '/auditTicket/TicketsByStatus'
  TICKET_DETAILS_BY_ID = this.PATH_FOR_API + '/auditTicket/getTicketInfo'

  // STOCK WAREHOUSE TRANSFER
  SWT_REQUIRED_DATA = this.PATH_FOR_API + '/warehouseTransfer/requiredDataForSaveWarehouseTransfer'
  SWT_SAVE = this.PATH_FOR_API + '/warehouseTransfer/saveWarehouseTransfer'
  SWT_ITEM_OTHER_DETAILS = this.PATH_FOR_API + '/warehouseTransfer/getItemOtherDetail'
  SWT_WAREHOUSE_LISTING_DROPDOWN = this.PATH_FOR_API + '/warehouse/getLoginUserWarehouseForDropdown'
  SWT_LISTING = this.PATH_FOR_API + '/warehouseTransfer/getWarehouseTransferPage'
  SWT_ASSOCIATED_ITEM_LISTING = this.PATH_FOR_API + '/warehouseTransfer/getWarehouseTransferAssociatedItem'
  SWT_GET_MARKA_DATA = this.PATH_FOR_API + '/warehouseTransfer/getMarkaPopupForWarehouseTransfer'
  SWT_APPROVE_GET_DATA = this.PATH_FOR_API + '/warehouseTransfer/getByIdWarehouseTransfer'
  SWT_DROPDOWN_WAREHOUSE_LIST = this.PATH_FOR_API + '/dropLocation/getAllDropLocationDropDownDataByWarehouse'
  SWT_APPROVE_WAREHOUSE_TRANSFER = this.PATH_FOR_API + '/warehouseTransfer/approveWarehouseTransfer'
  SWT_REJECT_WAREHOUSE_TRANSFER = this.PATH_FOR_API + '/warehouseTransfer/rejectWarehouseTransfer'
  SWT_DELETE_WAREHOUSE_TRANSFER = this.PATH_FOR_API + '/warehouseTransfer/deleteWarehouseTransferById'

  //STOCK BRANCH TRANSFER
  SBT_REQUIRED_DATA = this.PATH_FOR_API + '/stockBranchTransfer/requiredData'
  SBT_SAVE_DELETE = this.PATH_FOR_API + '/stockBranchTransfer/'
  SBT_GET_DATA_BY_BRANCH_ITEM = this.PATH_FOR_API + '/stockBranchTransfer/fetchData'
  SBT_DATA_FOR_SELF = this.PATH_FOR_API + '/stockBranchTransfer/allDataForSelf'

  // BRANCH STOCK
  BRANCH_STOCK_LISTING = this.PATH_FOR_API + '/branchTransfer/branchStock'
  BRANCH_STOCK_EXCEL = this.PATH_FOR_API + '/branchTransfer/exportExcel'
}

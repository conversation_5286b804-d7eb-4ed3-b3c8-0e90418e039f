import { inject } from "@angular/core";
import { CanActivateFn, ActivatedRouteSnapshot } from "@angular/router";
import { UtilsService } from "@service/utils.service";

export const accessGuard: CanActivateFn = (route: ActivatedRouteSnapshot) => {
    const utilsService = inject(UtilsService);

    const pages = route.data['pages'] as string[];
    const hasAccess = utilsService.checkPageAccess(pages);

    if (!hasAccess) {
        utilsService.goToAccessDenied();
        return false;
    }

    return true;
};
import { Component, OnInit, Input, Output, EventEmitter, forwardRef, ElementRef, ViewChild, HostListener } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment'

export type PickerMode = 'month' | 'day-month';
export type OutputFormat = 'string' | 'moment';
export type DisplayFormat = 'DD/MM' | 'DD-MM' | 'Month DD' | 'DD Month';

@Component({
  selector: 'app-month-day-picker',
  templateUrl: './date-range-selection.component.html',
  styleUrls: ['./date-range-selection.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DateRangeSelectionComponent),
      multi: true
    }
  ]
})
export class DateRangeSelectionComponent implements OnInit, ControlValueAccessor {

  @Input() mode: PickerMode = 'day-month';
  @Input() outputFormat: OutputFormat = 'string';
  @Input() displayFormat: DisplayFormat = 'DD-MM'; // Format for input field display
  @Input() disabledDates: string[] = []; // Format: 'DD-MM'
  @Input() disabled: boolean = false;
  @Input() placeholder: string = 'Select date'; // Placeholder text for the component
  @Input() minDate: string = ''; // Format: 'DD-MM' or 'MM'
  @Input() maxDate: string = ''; // Format: 'DD-MM' or 'MM'
  @Input() closeOnOutsideClick: boolean = true; // Whether to close popup when clicking outside
  @Input() range: boolean = false; // Enable range selection for month mode

  // Output events
  @Output() valueChange = new EventEmitter<string | moment.Moment>();
  @Output() onOpen = new EventEmitter<void>();
  @Output() onClose = new EventEmitter<void>();
  @Output() onMonthChange = new EventEmitter<number>();
  @Output() onDateSelect = new EventEmitter<{month: number, day?: number}>();

  @ViewChild('inputField', { static: true }) inputField!: ElementRef<HTMLInputElement>;

  // Internal state
  selectedMonth: number | null = null;
  selectedDay: number | null = null;
  currentDate = moment();
  inputValue: string = '';

  // Range selection state
  rangeStartMonth: number | null = null;
  rangeEndMonth: number | null = null;
  isSelectingRange: boolean = false;
  hoverMonth: number | null = null; // For hover preview during range selection

  // Navigation state
  viewingMonth: number = moment().month() + 1; // 1-based month (1-12)
  showMonthSelection: boolean = false; // For day-month picker month selection view

  // Months array
  months = [
    { value: 1, name: 'Jan', fullName: 'January' },
    { value: 2, name: 'Feb', fullName: 'February' },
    { value: 3, name: 'Mar', fullName: 'March' },
    { value: 4, name: 'Apr', fullName: 'April' },
    { value: 5, name: 'May', fullName: 'May' },
    { value: 6, name: 'Jun', fullName: 'June' },
    { value: 7, name: 'Jul', fullName: 'July' },
    { value: 8, name: 'Aug', fullName: 'August' },
    { value: 9, name: 'Sep', fullName: 'September' },
    { value: 10, name: 'Oct', fullName: 'October' },
    { value: 11, name: 'Nov', fullName: 'November' },
    { value: 12, name: 'Dec', fullName: 'December' }
  ];



  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor() { }

  ngOnInit() {
    // Highlight today's month/day
    this.highlightToday();
    this.updateInputValue();
  }



  // Keyboard navigation
  @HostListener('keydown.escape')
  onEscapeKey(): void {
    // NgBootstrap dropdown handles escape key automatically
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    if (value) {
      if (typeof value === 'string') {
        this.parseStringValue(value);
      } else if (moment.isMoment(value)) {
        this.selectedMonth = value.month() + 1; // moment months are 0-indexed
        if (this.mode === 'day-month') {
          this.selectedDay = value.date();
        }
      }
    } else {
      this.selectedMonth = null;
      this.selectedDay = null;
    }
    this.updateInputValue();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Helper methods
  private parseStringValue(value: string): void {
    if (this.mode === 'month') {
      if (value.match(/^\d{2}$/)) {
        // Numeric month format (01, 02, etc.)
        this.selectedMonth = parseInt(value, 10);
      } else if (value.match(/^[A-Z]{3}$/)) {
        // 3-letter month abbreviation (JAN, FEB, etc.)
        const month = this.months.find(m => m.name.toUpperCase() === value.toUpperCase());
        if (month) {
          this.selectedMonth = month.value;
        }
      } else if (value.includes('_')) {
        // Range format (JAN_MAR, etc.)
        const [startMonth, endMonth] = value.split('_');
        const start = this.months.find(m => m.name.toUpperCase() === startMonth.toUpperCase());
        const end = this.months.find(m => m.name.toUpperCase() === endMonth.toUpperCase());
        if (start && end) {
          this.rangeStartMonth = start.value;
          this.rangeEndMonth = end.value;
        }
      }
    } else if (this.mode === 'day-month' && value.match(/^\d{2}-\d{2}$/)) {
      const [day, month] = value.split('-').map(v => parseInt(v, 10));
      this.selectedMonth = month;
      this.selectedDay = day;
    }
  }

  private highlightToday(): void {
    // This will be used in the template to highlight today's month/day
  }

  // Event handlers
  onDropdownOpenChange(isOpen: boolean): void {
    if (isOpen) {
      // When dropdown opens, always reset to date view and set proper viewing month
      this.onTouched();
      this.showMonthSelection = false;

      if (this.mode === 'day-month') {
        if (this.selectedMonth) {
          // If date is selected, show that month
          this.viewingMonth = this.selectedMonth;
        } else {
          // If no date selected, show current month
          this.viewingMonth = moment().month() + 1;
        }
      }
      this.onOpen.emit();
    } else {
      // When dropdown closes, check for incomplete range selection
      if (this.mode === 'month' && this.range && this.isSelectingRange) {
        // Reset incomplete range selection
        this.rangeStartMonth = null;
        this.rangeEndMonth = null;
        this.isSelectingRange = false;
        this.hoverMonth = null;

        // Reset form control value
        this.onChange(null);
        this.valueChange.emit(null);
        this.updateInputValue();
      }
      this.onClose.emit();
    }
  }



  onMonthSelect(month: number, dropdown?: NgbDropdown): void {
    if (this.disabled || this.isMonthDisabled(month)) return;

    if (this.mode === 'month' && this.range) {
      this.handleRangeSelection(month, dropdown);
    } else if (this.mode === 'month') {
      // Single month selection
      this.selectedMonth = month;
      this.onTouched();
      this.onMonthChange.emit(month);
      this.onDateSelect.emit({ month });
      this.emitValue();
      dropdown?.close();
    } else {
      // For day-month mode, set viewing month and reset day selection
      this.selectedMonth = month;
      this.viewingMonth = month;
      this.selectedDay = null;
      this.onTouched();
      this.onMonthChange.emit(month);
    }
  }

  onDaySelect(day: number, dropdown?: NgbDropdown): void {
    if (this.disabled || this.isDayDisabled(day)) return;

    // Set the selected month to the current viewing month
    this.selectedMonth = this.viewingMonth;
    this.selectedDay = day;
    this.onTouched();
    this.onDateSelect.emit({ month: this.selectedMonth, day });
    this.emitValue();
    dropdown?.close();
  }

  private emitValue(): void {
    let value: string | moment.Moment | null = null;

    if (this.mode === 'month' && this.selectedMonth) {
      if (this.outputFormat === 'string') {
        const month = this.months.find(m => m.value === this.selectedMonth);
        value = month ? month.name.toUpperCase() : '';
      } else {
        value = moment().month(this.selectedMonth - 1).startOf('month');
      }
    } else if (this.mode === 'day-month' && this.selectedMonth && this.selectedDay) {
      const dayStr = this.selectedDay.toString().padStart(2, '0');
      const monthStr = this.selectedMonth.toString().padStart(2, '0');
      value = this.outputFormat === 'string' ? `${dayStr}-${monthStr}` :
              moment().year(2000).month(this.selectedMonth - 1).date(this.selectedDay);
    }

    if (value) {
      this.onChange(value);
      this.valueChange.emit(value);
    }
    this.updateInputValue();
  }

  private emitRangeValue(): void {
    if (!this.rangeStartMonth || !this.rangeEndMonth) return;

    const startMonth = this.months.find(m => m.value === this.rangeStartMonth);
    const endMonth = this.months.find(m => m.value === this.rangeEndMonth);

    let value: string | moment.Moment;

    if (this.outputFormat === 'string') {
      if (this.rangeStartMonth === this.rangeEndMonth) {
        // Single month: "JAN"
        value = startMonth ? startMonth.name.toUpperCase() : '';
      } else {
        // Range: "JUN_MAY"
        value = startMonth && endMonth ? `${startMonth.name.toUpperCase()}_${endMonth.name.toUpperCase()}` : '';
      }
    } else {
      // For moment output, return an object with start and end moments
      value = {
        start: moment().month(this.rangeStartMonth - 1).startOf('month'),
        end: moment().month(this.rangeEndMonth - 1).endOf('month')
      } as any;
    }

    this.onChange(value);
    this.valueChange.emit(value);
    this.updateInputValue();
  }

  // Update input field display value
  private updateInputValue(): void {
    this.inputValue = this.getSelectedDisplayValue();
  }

  // Utility methods for template
  getDaysInMonth(): number[] {
    // Always use viewingMonth to show days for the currently viewed month
    const monthToShow = this.viewingMonth;
    if (!monthToShow) return [];

    // Use current year for calculations (since we only deal with months)
    let daysInMonth = moment().year(moment().year()).month(monthToShow - 1).daysInMonth();

    // Special case for February: always show 29 days to allow leap year selection
    if (monthToShow === 2 && daysInMonth === 28) {
      daysInMonth = 29;
    }

    // Create simple array of days 1 to daysInMonth
    return Array.from({ length: daysInMonth }, (_, i) => i + 1);
  }

  isDayDisabled(day: number): boolean {
    const dayStr = day.toString().padStart(2, '0');
    const monthStr = this.viewingMonth.toString().padStart(2, '0');
    const dateStr = `${dayStr}-${monthStr}`;

    // Special handling for February 29th - always allow it to be selectable
    if (this.viewingMonth === 2 && day === 29) {
      // Only check explicit disabled dates and min/max constraints for Feb 29
      if (this.disabledDates.includes(dateStr)) return true;
      if (this.minDate && this.isDateBefore(dateStr, this.minDate)) return true;
      if (this.maxDate && this.isDateAfter(dateStr, this.maxDate)) return true;
      return false; // Allow Feb 29 to be selected
    }

    // Check disabled dates
    if (this.disabledDates.includes(dateStr)) return true;

    // Check min/max date constraints
    if (this.minDate && this.isDateBefore(dateStr, this.minDate)) return true;
    if (this.maxDate && this.isDateAfter(dateStr, this.maxDate)) return true;

    return false;
  }

  isMonthDisabled(month: number): boolean {
    const monthStr = month.toString().padStart(2, '0');

    // For month-only mode, check min/max constraints
    if (this.mode === 'month') {
      if (this.minDate && this.minDate.length === 2 && monthStr < this.minDate) return true;
      if (this.maxDate && this.maxDate.length === 2 && monthStr > this.maxDate) return true;
    }

    // For day-month mode, check if ALL dates in the month are disabled
    if (this.mode === 'day-month') {
      return this.isAllDatesInMonthDisabled(month);
    }

    return false;
  }

  isAllDatesInMonthDisabled(month: number): boolean {
    // Get number of days in the month
    const daysInMonth = moment().year(moment().year()).month(month - 1).daysInMonth();

    // Special case for February - always include 29 days
    const totalDays = month === 2 ? 29 : daysInMonth;

    // Check if all days are disabled
    for (let day = 1; day <= totalDays; day++) {
      const dayStr = day.toString().padStart(2, '0');
      const monthStr = month.toString().padStart(2, '0');
      const dateStr = `${dayStr}-${monthStr}`;

      // If any day is NOT disabled, the month is not disabled
      if (!this.isDateDisabled(dateStr, month, day)) {
        return false;
      }
    }

    return true; // All days are disabled
  }

  private isDateDisabled(dateStr: string, month: number, day: number): boolean {
    // Special handling for February 29th - always allow it to be selectable
    if (month === 2 && day === 29) {
      if (this.disabledDates.includes(dateStr)) return true;
      if (this.minDate && this.isDateBefore(dateStr, this.minDate)) return true;
      if (this.maxDate && this.isDateAfter(dateStr, this.maxDate)) return true;
      return false;
    }

    // Check disabled dates
    if (this.disabledDates.includes(dateStr)) return true;

    // Check min/max date constraints
    if (this.minDate && this.isDateBefore(dateStr, this.minDate)) return true;
    if (this.maxDate && this.isDateAfter(dateStr, this.maxDate)) return true;

    return false;
  }

  private isDateBefore(date: string, compareDate: string): boolean {
    if (compareDate.length === 2) { // MM format
      const dateMonth = parseInt(date.substring(3, 5), 10); // Extract month from DD-MM
      const compareMonth = parseInt(compareDate, 10);
      return dateMonth < compareMonth;
    } else if (compareDate.length === 5) { // DD-MM format
      const [dateDay, dateMonth] = date.split('-').map(num => parseInt(num, 10));
      const [compareDay, compareMonth] = compareDate.split('-').map(num => parseInt(num, 10));

      if (dateMonth !== compareMonth) {
        return dateMonth < compareMonth;
      }
      return dateDay < compareDay;
    }
    return false;
  }

  private isDateAfter(date: string, compareDate: string): boolean {
    if (compareDate.length === 2) { // MM format
      const dateMonth = parseInt(date.substring(3, 5), 10); // Extract month from DD-MM
      const compareMonth = parseInt(compareDate, 10);
      return dateMonth > compareMonth;
    } else if (compareDate.length === 5) { // DD-MM format
      const [dateDay, dateMonth] = date.split('-').map(num => parseInt(num, 10));
      const [compareDay, compareMonth] = compareDate.split('-').map(num => parseInt(num, 10));

      if (dateMonth !== compareMonth) {
        return dateMonth > compareMonth;
      }
      return dateDay > compareDay;
    }
    return false;
  }

  isToday(month?: number, day?: number): boolean {
    const today = moment();
    if (month && !day) {
      return today.month() + 1 === month;
    } else if (month && day) {
      return today.month() + 1 === month && today.date() === day;
    }
    return false;
  }

  isSelected(month?: number, day?: number): boolean {
    if (month && !day) {
      // For range mode, check if month is in range
      if (this.mode === 'month' && this.range && this.rangeStartMonth && this.rangeEndMonth) {
        return this.isMonthInRange(month);
      }
      return this.selectedMonth === month;
    } else if (month && day) {
      return this.selectedMonth === month && this.selectedDay === day;
    }
    return false;
  }

  isMonthInRange(month: number): boolean {
    if (!this.rangeStartMonth || !this.rangeEndMonth) return false;

    const start = this.rangeStartMonth;
    const end = this.rangeEndMonth;

    if (start <= end) {
      // Normal range (e.g., Mar to Oct)
      return month >= start && month <= end;
    } else {
      // Wrap-around range (e.g., Oct to Mar)
      return month >= start || month <= end;
    }
  }

  isRangeStart(month: number): boolean {
    return this.range && this.rangeStartMonth === month;
  }

  isRangeEnd(month: number): boolean {
    return this.range && this.rangeEndMonth === month;
  }

  // Hover preview methods
  onMonthHover(month: number): void {
    if (this.isSelectingRange && this.rangeStartMonth) {
      this.hoverMonth = month;
    }
  }

  onMonthLeave(): void {
    this.hoverMonth = null;
  }

  isInHoverRange(month: number): boolean {
    if (!this.isSelectingRange || !this.rangeStartMonth || !this.hoverMonth) {
      return false;
    }

    const start = this.rangeStartMonth;
    const end = this.hoverMonth;

    if (start <= end) {
      // Normal range (e.g., Mar to Oct)
      return month >= start && month <= end;
    } else {
      // Wrap-around range (e.g., Oct to Mar)
      return month >= start || month <= end;
    }
  }

  isHoverRangeStart(month: number): boolean {
    return this.isSelectingRange && this.rangeStartMonth === month && this.hoverMonth !== null;
  }

  isHoverRangeEnd(month: number): boolean {
    return this.isSelectingRange && this.hoverMonth === month && this.rangeStartMonth !== null;
  }

  // TrackBy functions for performance
  trackByMonth(index: number, month: any): number {
    return month.value;
  }

  trackByDay(index: number, day: number): number {
    return day;
  }

  // Range selection handler
  handleRangeSelection(month: number, dropdown?: NgbDropdown): void {
    if (!this.isSelectingRange) {
      // First click - start range selection
      this.rangeStartMonth = month;
      this.rangeEndMonth = null;
      this.isSelectingRange = true;
      this.onMonthChange.emit(month);
    } else {
      // Second click - complete range selection
      if (this.rangeStartMonth === month) {
        // Double click on same month - select single month range
        this.rangeEndMonth = month;
      } else {
        // Different month - set end of range
        this.rangeEndMonth = month;
      }

      this.isSelectingRange = false;
      this.hoverMonth = null; // Reset hover state
      this.onTouched();
      this.onDateSelect.emit({
        month: this.rangeStartMonth!,
        day: this.rangeEndMonth
      });
      this.emitRangeValue();
      dropdown?.close();
    }
  }

  // Month navigation methods
  navigateToPreviousMonth(): void {
    if (this.viewingMonth > 1) {
      this.viewingMonth--;
    } else {
      this.viewingMonth = 12;
    }
  }

  navigateToNextMonth(): void {
    if (this.viewingMonth < 12) {
      this.viewingMonth++;
    } else {
      this.viewingMonth = 1;
    }
  }



  getCurrentMonthName(): string {
    const month = this.months.find(m => m.value === this.viewingMonth);
    return month ? month.fullName : '';
  }

  // Month selection view methods for day-month picker
  showMonthSelectionView(): void {
    if (this.mode === 'day-month') {
      this.showMonthSelection = true;
    }
  }

  hideMonthSelectionView(): void {
    this.showMonthSelection = false;
  }

  onMonthSelectFromGrid(month: number): void {
    if (this.disabled || this.isMonthDisabled(month)) return;

    this.viewingMonth = month;
    this.showMonthSelection = false;
    this.onMonthChange.emit(month);
  }





  // Display value for selected date
  getSelectedDisplayValue(): string {
    if (this.mode === 'month' && this.range && this.rangeStartMonth && this.rangeEndMonth) {
      // Range display: "June - May" or "January" for single month
      const startMonth = this.months.find(m => m.value === this.rangeStartMonth);
      const endMonth = this.months.find(m => m.value === this.rangeEndMonth);

      if (this.rangeStartMonth === this.rangeEndMonth) {
        // Single month: "January"
        return startMonth ? startMonth.fullName : '';
      }

      // Range: "June - May"
      return startMonth && endMonth ? `${startMonth.fullName} - ${endMonth.fullName}` : '';
    } else if (this.mode === 'month' && this.selectedMonth) {
      // Single month: "January"
      const month = this.months.find(m => m.value === this.selectedMonth);
      return month ? month.fullName : '';
    } else if (this.mode === 'day-month' && this.selectedMonth && this.selectedDay) {
      const month = this.months.find(m => m.value === this.selectedMonth);
      const dayStr = this.selectedDay.toString().padStart(2, '0');
      const monthStr = this.selectedMonth.toString().padStart(2, '0');

      switch (this.displayFormat) {
        case 'DD/MM':
          return `${dayStr}/${monthStr}`;
        case 'DD-MM':
          return `${dayStr}-${monthStr}`;
        case 'Month DD':
          return month ? `${month.name} ${dayStr}` : '';
        case 'DD Month':
          return month ? `${dayStr} ${month.name}` : '';
        default:
          return `${dayStr}-${monthStr}`;
      }
    }
    return '';
  }
}

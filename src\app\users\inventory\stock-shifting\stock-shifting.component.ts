import { Component, computed, effect, inject, OnInit, ViewChild } from '@angular/core';
import { EnumForShiftingStatus } from '@enums/EnumForShiftingStatus';
import { EnumForTicketStatus } from '@enums/EnumForTicketStatus';
import { createArraySignal, createObjectSignal } from '@libs';
import { StockShiftingPagination } from '@modal/request/StockShiftingPagination';
import { StockShiftingList, StockShiftingListItems, StockShiftingListItemsQty, StockShiftingTicketStatusData } from '@modal/StockShiftingList';
import { UtilsService } from '@service/utils.service';
import dayjs from 'dayjs';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { Subject, takeUntil, tap, map } from 'rxjs';
declare var window: any;

@Component({
  selector: 'app-stock-shifting',
  templateUrl: './stock-shifting.component.html',
  styleUrls: ['./stock-shifting.component.css']
})
export class StockShiftingComponent implements OnInit {

  @ViewChild(DaterangepickerDirective) pickerDirective: DaterangepickerDirective;

  utilsService = inject(UtilsService);

  enumForTicketStatus = EnumForTicketStatus;
  enumForShiftingStatus = EnumForShiftingStatus;

  paginationRequest = createObjectSignal({} as StockShiftingPagination);
  stockShiftingList = createArraySignal([] as StockShiftingList[]);
  stockShiftingObj = createObjectSignal({} as StockShiftingList);

  isStockShiftingListEmpty = computed(() => (this.stockShiftingList.get() || []).length === 0);

  private destroy$ = new Subject<void>();
  private hasDateInit = false;

  isExpandedIDs: { parentIds: number[] } = { parentIds: [] };
  warehouseDropdown: any[] = []
  deleteStockShiftingModal: any;

  constructor() {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: "100" }));
    effect(() => {
      this.expandCollapseIdsSave()
    });
  }

  ngOnInit() {
    this.getAllStockShifting();
    this.getRequiredData();

    this.deleteStockShiftingModal = new window.bootstrap.Modal(
      document.getElementById('deleteStockShiftingModal')
    );
  }

  getRequiredData = () => {

    const API = this.utilsService.serverVariableService.REQ_STOCK_SHIFTING_DATA;

    this.utilsService.get(API).pipe(
      map((res) => res.data),
      tap((data: any) => {
        this.warehouseDropdown = data.warehouse
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getAllStockShifting = () => {
    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])

    this.utilsService.post(this.utilsService.serverVariableService.STOCK_SHIFTING_PAGE, param).pipe(
      map((res) => res.data),
      tap((res) => {
        this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'], pagination: res.pagination }));
      }),
      map((res) => res?.['content']),
      tap((data: StockShiftingList[]) => {
        if (!this.utilsService.isNullUndefinedOrBlank(data)) {
          this.stockShiftingList.set(data);
          this.expandOnFilter(data);
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getStockShiftingAssociatedItem = (id: number, index: number) => {

    const current = this.stockShiftingList.get()[index];
    if (current?.loadedChild) return;

    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])
    param.stickShiftingId = id;

    this.utilsService.post(this.utilsService.serverVariableService.STOCK_SHIFING_ASSOCIATED_ITEM, param).pipe(
      map((res) => res.data),
      tap((data: StockShiftingListItems[]) => {
        this.stockShiftingList.updateAt(index, a => ({ ...a, items: data, loadedChild: true }))
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onExpand = (index: number, item: StockShiftingList) => {
    this.stockShiftingList.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
    if (this.stockShiftingList.get()[index].isExpand) {
      this.getStockShiftingAssociatedItem(item.id, index)
    }
  }

  onExpandToggle = () => {
    this.stockShiftingList.update(a => a.map(item => ({ ...item, isExpand: !item.isExpand })))
  }

  expandCollapseIdsSave = () => {
    if (!this.stockShiftingList.get()?.length) {
      return;
    }

    const parentIds: number[] = [];
    for (const item of this.stockShiftingList.get()) {
      if (item.isExpand && !parentIds.includes(item.id)) {
        parentIds.push(item.id);
      }
    }
    this.isExpandedIDs = { parentIds };
  }

  addPageSizeData(event: any) {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
    this.getAllStockShifting();
  }

  pageNumber(event: any) {
    this.paginationRequest.update(a => ({ ...a, pageNo: event }));
    this.getAllStockShifting();
  }

  getStockShiftingAssociatedItemQty = (id: number, associatedItemId: number, index: number, subIndex: number) => {

    // const current = this.stockShiftingList.get()[index].items[subIndex];
    // if (current?.loadedChild) return;

    const param = {
      stickShiftingId: id,
      associatedItemId: associatedItemId
    }

    this.utilsService.post(this.utilsService.serverVariableService.GET_STOCK_SHIFING_ASSOCIATED_ITEM_QTY, param).pipe(
      map((res) => res.data),
      tap((data: StockShiftingListItemsQty[]) => {
        this.stockShiftingList.updateAt(index, (item) => {
          const updatedItems = [...item.items];
          updatedItems[subIndex] = { ...updatedItems[subIndex], data, loadedChild: true };
          return { ...item, items: updatedItems };
        });
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getTicketsStatusByTicketTypeAndAssociatedItem = (id: number, associatedItemId: number, index: number, subIndex: number) => {

    // const current = this.stockShiftingList.get()[index].items[subIndex];
    // if (current?.loadedChild) return;

    const param = {
      ticketType: "ST",
      ticketTypeId: id,
      associateItemId: associatedItemId
    }

    this.utilsService.post(this.utilsService.serverVariableService.GET_TICKETS_STATUS_BY_TICKET_TYPE_AND_ASSOCIATED_ITEM, param).pipe(
      map((res) => res.data),
      tap((data: StockShiftingTicketStatusData[]) => {
        this.stockShiftingList.updateAt(index, (item) => {
          const updatedItems = [...item.items];
          updatedItems[subIndex] = { ...updatedItems[subIndex], ticketData: data, loadedChild: true };
          return { ...item, items: updatedItems };
        });
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // Filter
  onChangeFilter = (event: any, type: 'date' | 'search' | 'warehouseId', isDateClear?: boolean) => {
    switch (type) {
      case 'date':
        if (!this.hasDateInit) {
          this.hasDateInit = true;
          return;
        }
        if (event?.start === null && event?.end === null && !isDateClear) {
          return;
        }
        const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
        const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
        this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
        break;
      case 'search':
        this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
        break;
      case 'warehouseId':
        this.paginationRequest.update(a => ({ ...a, warehouseId: event }));
        break;
    }
    this.destroy$.complete()
    this.getAllStockShifting();

  }

  onClearDateOnly = () => {
    this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null }));
    this.onChangeFilter(null, 'date', true);
}

  // Date picker functions
  open = () => {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    }
  }

  // Filters Clear
  onClear = () => {
    this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null, searchText: null, warehouseId: null }));
    this.onChangeFilter(null, 'date', true);
  }

  expandOnFilter = (list: StockShiftingList[] = this.stockShiftingList.get(), expandCount: number = 5) => {
    const { searchText, fromDate, toDate, warehouseId } = this.paginationRequest.get();
    const hasFilters = searchText || fromDate || toDate || warehouseId;

    if (hasFilters && list.length > 0) {
      this.stockShiftingList.update(items =>
        items.map((item, index) => ({ ...item, isExpand: index < expandCount }))
      );

      const limitedList = list.slice(0, expandCount);
      for (const item of limitedList) {
        const index = this.stockShiftingList.get().findIndex(i => i.id === item.id);
        if (index !== -1) {
          this.getStockShiftingAssociatedItem(item.id, index);
        }
      }
    }
  };

  onRefresh = () => {
    this.getAllStockShifting();
  }

  openDeleteStockShiftingModal = (item: StockShiftingList) => {
    this.stockShiftingObj.set(item);
    this.deleteStockShiftingModal.show();
  }

  onDeleteStockShifting = () => {

    const API = `${this.utilsService.serverVariableService.DELETE_STOCK_SHIFING}?id=${this.stockShiftingObj.get().id}`;

    this.utilsService.delete(API, { toast: true }).pipe(
      map((res) => res.data),
      tap(() => {
        this.getAllStockShifting();
        this.deleteStockShiftingModal.hide();
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }
}

import { Moment } from "moment";

export interface SalesInquiry {
    customerId: number;
    customerNote: string;
    countryId: number;
    phone: string;
    inquiryItems: SalesInquiryItem[];
}

export interface SalesInquiryItem {
    itemId: number;
    inquiryType: number;
    reasonId: number;
    enumsItemsBelong: number;
    enumForOrderUnit: number;
    inquiryQuantity: number;
    piecePerCarton: number;
    totalPiece: number;
    rate: number;
    totalAmount: number;
    salesExecutiveOfferedPrice: number;
    customerRequestedPrice: number;
    hsnCodeId: number;
    gstAmount: number;
    totalAmountWithGst: number;
    note: string;
    hsnDropdown: any[];
    taxName: string;
    inquiryItemName: string;
    //webcam
    firstImage: string;
    images: Attachment[];
    itemName: string;
    isOnlyImage: boolean;
    //inquiry group code
    itemGroupId: number;
    lastSalesInfo: LastSalesInfo[];
}

export interface InquiryDropdowns {
    Country: {id: number, countryExtension: string, isActive: boolean}[]
    NITReason: {value: number, label: string, isActive: boolean}[]
    customerLead: {id: number, displayName: string, isActive: boolean, phone: string, countryId: number, registrationTypeName : string}[]
    hsnCodeMaster: {id: number, hsnCode: string, taxName: string, isActive: boolean, description: string, rate: number}[]
    itemsBelong: {value: number, label: string}[]
    orderUnit: {value: number, label: string}[]
    item: InquiryItemDropdown[]
    InquiryIdealTimer: string,
    inquiryItems: SalesInquiryItem[];
}

export interface InquiryItemDropdown {
    displayName: string,
    hsnCodeMasterId: number,
    id: number,
    itemName: string,
    skuId: string,
    formattedName: string,
    hsnCodes: {id: number, hsnCode: string, taxName: string, isActive: boolean, description: string, rate: number}[]
    orderUnits: {label: string, value: string}[]
    itemPrice: number
    cartonPrice: number
    itemGroupId: number
    branchMaster: any;
    cartonOutOfStock: number;
    looseOutOfStock: number;
    outOfStock: boolean
    isUpcomingAvailable: boolean
    isSellCarton: boolean
    lastSalesInfo: LastSalesInfo[],
    itemId : number
}

export interface Attachment {
    id: number;
    file: File;
    originalName: string;
    formattedName: string;
}

export interface InquiryListing {
    // id: number,
    customerId: number,
    phone: string,
    displayName: string,
    countryExtension: string
    isExpand: boolean
    items: InquiryListingItem[]
    loadedChild: boolean,
    isCustomerLead : boolean
}

export interface InquiryListingItem {
    id: number,
    itemId: number,
    inquiryType: {label: string, value: string},
    enumsItemsBelong: string
    inquiryQuantity: number
    totalPiece: number
    salesExecutiveOfferedPrice: number
    customerRequestedPrice: number
    piecePerCarton: number
    note: string
    salesManagerPrice: number
    followUpDateTime: string | Moment
    itemInquiryDate: string
    reasonName: string
    enumForOrderUnit: {label: string, value: string}
    inquiryItemName: string
    lastSalesInfo: LastSalesInfo[]
    item: {
        id: number,
        itemName: string,
        displayName: string,
        skuId: string,
        isActive: boolean,
        itemPrice: number,
        originalName: string,
        formattedName: string,
        itemGroupName: string,
        qcCheckListId: number,
        qcCheckListName: string,
        groupCodeName: string
        averagePriceWithoutGST: number,
        isSelected : boolean
    },
}

export interface InquiryAssociatedItemModal {
    id: number,
    displayName: string,
    skuId: string,
    image: string,
    hsnCode: string,
    stock: {
        countOfCarton: number,
        pricePerCarton: number,
        sumOfCartonQty: number,
        availableCarton: number,
        totalCartonQty: number,
        looseQty: number,
        totalAvailableCartonQty: number,
    }
}

export interface InquiryBranchMarkaModal {
    availableCartonsQty: number,
    availableLooseQty: number,
    totalAvailableQty: number,
    branchId: number
    branchName: string
    markaStocks: MarkaStocks[]
    totalHoldLooseQty: number
    totalHoldCartonQty: number
    finalQty?: number
    totalWPCarton?: number
    totalWPLoose?: number
}

export interface MarkaStocks {
    marka: string,
    warehouseStocks: WarehouseStocks[],
    grnGroupLinkId: number,
    countOfImage: number
    expiresInDays: number
    sumTotalCartonQty?: number;
    sumTotalLooseQty?: number;
    sumTotalQty?: number;
    sumCartonPcs?: number
    orderQty?: number
    qty?: number
    branchId?: number
    age: number
    tag: string
}

export interface WarehouseStocks {
    warehouseName: string,
    warehouseId: number
    totalCartons: number,
    pcsPerCarton: number,
    totalCartonQty: number,
    totalLooseQty: number,
    totalQty: number,
    aisleStocks: AisleStocks[]
}

export interface AisleStocks {
    id: number,
    cartonPcs: number,
    cartonQty: number
    location: string
    totalCarton: number
    looseQty: number
}

export interface BranchMaster {
    branchName: string
    id: number
    totalAvailableQty: number
}

export interface UpcomingQty {
    cartonPic: number
    cartonQty: number
    containerName: string
    expectedDeliveryDate: string
    marka: string
    stage: string
    totalQty: number
    stageDate: string
    id: number
    qty: number
    orderQty: number
    poImportItemId: number
    upcomingQty?: number
}

// Inq listing TABs
export interface ImageMappedInquiry {
    id: number,
    displayName: string,
    tag: string,
    lastModifiedDate: string,
    itemDocsCount: number,
    customerCount: number,
    itemDocs: AttachmentTick[]
    itemCustomerMaps: ItemCustomerMap[]
    isExpand: boolean
    skuId: string
    dummySKUId: string
    isDummyItem: boolean
    sumOfInquiry: number
    markAsDefaultImage: string
}

export interface ItemCustomerMap {
    id: number,
    itemId: number,
    customerId: number,
    customerRequestedPrice: number,
    inquiryQuantity: number,
    inquiryCount: number,
    lastModifiedDate: string
    phone: string
    displayName: string
    inquiryItemDocs: AttachmentTick[]
}

export interface LastSalesInfo {
    totalQty: number,
    rate: number,
    dateTime: string
}

export interface AttachmentTick extends Attachment {
    isMarkDefault: boolean
}

export interface ImageInquiryTab {
    id: number,
    otherData: {
        customerName: string;
        phone: string;
        inquiryItemId: number;
        inquiryId: number;
        customerId: number;
    };
    images: {
        id: number;
        originalName: string;
        formattedName: string;
        isSelectedChild: boolean
    }[];
    isSelected: boolean
}

export interface ImageMergeSave {
    itemId: number;
    itemName: string;
    tag: string;
    inquiryItemDocIds: number[];
    info: {
        customerId: number;
        inquiryItemId: number;
    }[];
}

export interface MarkaBranchData {
    activeTab: number,
    markaDropDown: InquiryBranchMarkaModal,
    upcomingQty: UpcomingQty[],
    warehouse: any[],
    upcomingCartonQty: number,
    totalUpcomingQty: number,
    allBranchSumTotalInput?: {
        branchId: number,
        sumTotalInput: number
    }[]
}
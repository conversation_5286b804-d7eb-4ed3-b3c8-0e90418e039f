import { Component, EventEmitter, inject, Input, OnInit, Output, signal, TemplateRef } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { Item } from '@modal/Item';
import { ItemSupplier } from '@modal/ItemSupplier';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgbModalService } from '@service/ngb-modal.service';
import { UtilsService } from '@service/utils.service';
import { Serialize } from 'cerialize';

@Component({
  selector: 'app-item-supplier',
  templateUrl: './item-supplier.component.html',
  styleUrls: ['./item-supplier.component.scss']
})
export class ItemSupplierComponent implements OnInit {

  utilsService = inject(UtilsService);
  private modalService = inject(NgbModalService)

  @Input({ alias: 'regForm', required: true }) regForm: FormGroup;
  @Input({ alias: 'associatedSupplier', required: true }) associatedSupplier: ItemSupplier[];
  @Input({ alias: 'isEdit', required: true }) isEdit: boolean;
  @Input({ alias: 'itemObj', required: true }) itemObj: Item;

  @Output() addSupplier = new EventEmitter<void>();
  @Output() generateMeasurementCode = new EventEmitter<any>();
  @Output() onAddImageToItem: EventEmitter<any> = new EventEmitter<any>();

  supplierObj = new ItemSupplier();
  selectedIndex = signal<number>(null);

  selectedItemIndex = signal<number>(null);
  selecteditemImgIndex = signal<number>(null);

  constructor() { }

  ngOnInit() {
  }

  get supplierList() {
    return this.regForm.get('supplierList') as FormArray;
  }

  customSearchFn(term: string, item: any) {
    term = term.toLowerCase();
    return item.displayName.toLowerCase().indexOf(term) > -1;
  }

  calculateCBM(associateItem: ItemSupplier) {
    if (associateItem.cartonLength && associateItem.cartonHeight && associateItem.cartonWidth && associateItem.unitId) {
      let unit = associateItem.unitDropdown.find(u => u.id == associateItem.unitId);
      let conversion = Math.pow(unit.conversionToMeter, 3);
      associateItem.cbm = (associateItem.cartonLength * associateItem.cartonHeight * associateItem.cartonWidth) / conversion;
      associateItem.cbm = Number(associateItem.cbm.toFixed(5));
    }
    else {
      associateItem.cbm = 0
    }
  }

  onChangeTagQty(item: ItemSupplier, index: number) {
    this.generateMeasurementCode.emit({ item: item, index: index })
  }

  preventRemovalColor(event, item: ItemSupplier) {
    return false
  }

  onSelectImages(event, index: number) {
    this.onAddImageToItem.emit({ event: event, index: index })
  }

  openDeleteModal(item: ItemSupplier, content: TemplateRef<any>, index: number) {
    this.supplierObj = Serialize(item);
    this.selectedIndex.set(index);
    this.modalService.open(content);
  }

  removeSupplier(modal: NgbModalRef) {
    if (this.associatedSupplier.at(this.selectedIndex())?.id) {
      this.itemObj.deleteAssociateIds.push(this.associatedSupplier.at(this.selectedIndex()).id)
    }
    this.supplierList.removeAt(this.selectedIndex());
    this.associatedSupplier.splice(this.selectedIndex(), 1);
    this.modalService.close(modal);
  }

  openRemoveImageSuppModal(index: number, childIndex: number, content: TemplateRef<any>) {
    this.selectedItemIndex.set(index);
    this.selecteditemImgIndex.set(childIndex);
    this.modalService.open(content);
  }

  removeImageItem(modal: NgbModalRef) {
    if (this.associatedSupplier[this.selectedItemIndex()].images.at(this.selecteditemImgIndex())?.id) {
      this.itemObj.deleteRegistrationDocIds.push(this.associatedSupplier[this.selectedItemIndex()].images.at(this.selecteditemImgIndex())?.id)
    }
    this.associatedSupplier[this.selectedItemIndex()].images.splice(this.selecteditemImgIndex(), 1);
    this.modalService.close(modal);
  }
}

<div class="card card-theme card-settings">
  <div class="card-body pt-0" [formGroup]="settingsForm">
    <div class="settings-row-wrapper">
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Sales Hold By</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter days" formControlName="salesHoldBy"
                [maxlength]="4" mask="separator.0" thousandSeparator="" [(ngModel)]="obj.salesHoldBy ">
              <span class="input-group-text">Mins</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['salesHoldBy'].hasError('required') && !settingsForm.controls['salesHoldBy'].valid && settingsForm.controls['salesHoldBy'].touched">
              {{utilsService.validationService.SALES_HOLD_BY_MINS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <p class="settings-info"><b>*Orders that aren't billed by this time will be automatically removed.</b></p>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Import Shipping Days</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter days" aria-label="Import Shipping Days"
                aria-describedby="button-addon1" formControlName="importShippingDay" [(ngModel)]="obj.importShippingDay"
                [maxlength]="3" mask="separator.0" thousandSeparator="">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['importShippingDay'].hasError('required') && !settingsForm.controls['importShippingDay'].valid && settingsForm.controls['importShippingDay'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Local Shipping Days</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter days" aria-label="Local Shipping Days"
                aria-describedby="button-addon1" formControlName="localShippingMonth"
                [(ngModel)]="obj.localShippingMonth" [maxlength]="3" mask="separator.0" thousandSeparator="">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['localShippingMonth'].hasError('required') && !settingsForm.controls['localShippingMonth'].valid && settingsForm.controls['localShippingMonth'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Consider 'Ordered Qty' in Breach qty calculation</p>
        </div>
        <div class="setting-col-right">
          <div class="checkbox checkbox-primary">
            <input type="checkbox" id="orderedqty" formControlName="orderedQtyCalculation"
              [(ngModel)]="obj.orderedQtyCalculation" class="material-inputs filled-in" />
            <label for="orderedqty"></label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Allow to raise branch transfer request</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="branchrequest">
              <input type="checkbox" id='branchrequest' formControlName="allowToRaiseBranchTransfReq"
                [(ngModel)]="obj.allowToRaiseBranchTransfReq" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Branch request to main branch auto approval</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="mainbranchapproval">
              <input type="checkbox" id='mainbranchapproval' formControlName="branchReqToMainBranchApproval"
                [(ngModel)]="obj.branchReqToMainBranchApproval" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Auto Approve Enquiry Offer price</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="autoapprove">
              <input type="checkbox" id='autoapprove' formControlName="autoEnquiryOfferPrice"
                [(ngModel)]="obj.autoEnquiryOfferPrice" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <p class="settings-info"><b>PO Setting</b></p>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>CBM Price</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input [maxlength]="utilsService.validationService.MAX_20" type="text" class="form-control"
                placeholder="Enter" aria-label="CBM Price" aria-describedby="button-addon1" formControlName="cbmPrice"
                [(ngModel)]="obj.cbmPrice" mask="separator.5" thousandSeparator="">
              <span class="input-group-text">Rs</span>
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Show Enquiry No. of Days</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input [maxlength]="3" mask="separator.0" thousandSeparator="" type="text" class="form-control"
              placeholder="Enter" formControlName="enquiryNoOfDays" [(ngModel)]="obj.enquiryNoOfDays">
            <div class="message error-message"
              *ngIf="!settingsForm.controls['enquiryNoOfDays'].hasError('required') && !settingsForm.controls['enquiryNoOfDays'].valid && settingsForm.controls['enquiryNoOfDays'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Need QR Code to Scan</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
              placeholder="Enter" formControlName="needToQrCodeScan" [(ngModel)]="obj.needToQrCodeScan"
              mask="separator.0" thousandSeparator="">
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Auto Ticket Raise {{'(Carton Qty <)'}} </p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
              placeholder="Enter" formControlName="autoTicketRaiseCartonQty" [(ngModel)]="obj.autoTicketRaiseCartonQty"
              mask="separator.0" thousandSeparator="">
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Auto Ticket Raise {{'(Breach <)'}} </p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
              placeholder="Enter" formControlName="autoTicketRaiseBranch" [(ngModel)]="obj.autoTicketRaiseBranch"
              mask="separator.0" thousandSeparator="">
          </div>
        </div>
      </div>
      <div class="settings-row">
        <p class="settings-info"><b>Warehouse Transfer settings</b></p>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Godown to Rack Transfer Qty</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
              placeholder="Enter" formControlName="godownToRackTransfer" [(ngModel)]="obj.godownToRackTransfer"
              mask="separator.0" thousandSeparator="">
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Variation percentage</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
                placeholder="Enter" aria-label="Variation percentage" aria-describedby="button-addon1"
                formControlName="variationPrg" [(ngModel)]="obj.variationPrg" mask="separator.3" thousandSeparator="">
              <span class="input-group-text">%</span>
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <p class="settings-info"><b>Catalogue</b></p>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>New Arrival Days After GRN</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter" [maxlength]="3" mask="separator.0" thousandSeparator=""
                formControlName="newArrivalAfterGrn" [(ngModel)]="obj.newArrivalAfterGrn">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['newArrivalAfterGrn'].hasError('required') && !settingsForm.controls['newArrivalAfterGrn'].valid && settingsForm.controls['newArrivalAfterGrn'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class='bottombar-wrapper bottom-fixed'>
  <div class='bottombar-container'>
    <div class='bottombar-left'>
      <button (click)="onSaveSettings.emit()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
          class="th th-outline-tick-circle"></i>
        Save</button>
      <button (click)="utilsService.redirectTo('/users/dashboard/')" type="button"
        class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
    </div>
    <div class='bottombar-right'>

    </div>
  </div>
</div>
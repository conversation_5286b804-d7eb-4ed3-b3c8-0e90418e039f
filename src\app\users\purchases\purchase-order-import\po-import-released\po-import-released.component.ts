import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import moment from 'moment';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { POImportItem } from 'src/app/models/POImportItem';
import { POImportList, POImportsSub } from 'src/app/models/POImportList';
import { POImportPagination } from 'src/app/models/request/POImportPagination';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-po-import-released',
  templateUrl: './po-import-released.component.html',
  styleUrls: ['./po-import-released.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PoImportReleasedComponent implements OnInit {

  Option = [
    { id: 1, name: 'Active' },
    { id: 2, name: 'Active 2' },
    { id: 3, name: 'Active 3' },
  ];

  demo = [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' },
    { id: 3, name: 'Option 3' },
  ];


  selectedOption: number = 1;
  selectedDemo1: number = 1;

  @ViewChild(DaterangepickerDirective) pickerDirective: DaterangepickerDirective;

  enumForContainerStatus = this.utilsService.poImportStatus

  @Input({ alias: 'selectedTab', required: true }) selectedTab: string;
  @Input({ alias: 'headerObj', required: true }) headerObj: any;
  @Input({ alias: 'columnArr', required: true }) columnArr: any;
  @Input({ alias: 'allHeaderArr', required: true }) allHeaderArr: any;

  @Input({ alias: 'poImportList', required: true }) poImportList: POImportList[];
  @Input({ alias: 'dropdown', required: true }) dropdown: any;
  @Input({ alias: 'paginationRequest', required: true }) paginationRequest: POImportPagination;

  @Output() getAllPOImports: EventEmitter<any> = new EventEmitter<any>();
  @Output() checkIfAllSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() saveCol: EventEmitter<any> = new EventEmitter<any>();
  @Output() onCollapse: EventEmitter<any> = new EventEmitter<any>();
  @Output() openLoadedContainerEdit: EventEmitter<any> = new EventEmitter<any>();
  @Output() openStatusChangeModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() redirectToContainerExpense: EventEmitter<any> = new EventEmitter<any>();
  
  @Output() redirectToDetails: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClear: EventEmitter<any> = new EventEmitter<any>();
  @Output() downloadPacking: EventEmitter<any> = new EventEmitter<any>();
  @Output() downloadQR: EventEmitter<any> = new EventEmitter<any>();

  @Output() openAssignToCHA: EventEmitter<any> = new EventEmitter<any>();
  @Output() openMoveToCompleted: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedTab'] && changes['selectedTab'].currentValue) {
      setTimeout(() => {
        this.pickerDirective?.clear();
        this.pickerDirective?.open(); 
        this.pickerDirective?.clear(); 
        this.pickerDirective?.hide();
      }, 0);
    }
  }

  onChangeImporter() {
    this.getAllPOImports.emit();
  }

  //track By/ Toggle Expand
  trackBy(index: number, name: POImportList): number {
    return name.id;
  }

  trackByChild(index: number, name: POImportItem): number {
    return name.id;
  }

  //Pagination
  addPageSizeData(event) {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllPOImports.emit();
  }

  pageNumber(event) {
    this.paginationRequest.pageNo = event
    this.getAllPOImports.emit();
  }

  //Date Ranges
  open(): void {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  //collapse
  toggleExpand(index: number): void {
    this.onCollapse.emit(index)
  }

  //Search
  onSearch = (event: any, marka: boolean) => {
    if (marka) {
      this.paginationRequest.searchText = (event.target.value ? event.target.value : null);
    } else {
      this.paginationRequest.searchByPoContainer = (event.target.value ? event.target.value : null);
    }
    this.getAllPOImports.emit();
  }


  //Date picker
  onChangeDate = (type: string) => {
    switch (type) {
      case 'delivery':
        let date = moment(`${this.paginationRequest.temp_expectedDeliveryDate.year}-${this.paginationRequest.temp_expectedDeliveryDate.month}-
          ${this.paginationRequest.temp_expectedDeliveryDate.day}`
          , 'YYYY-MM-DD');
        this.paginationRequest.expectedDeliveryDate = date.format('YYYY-MM-DD');
        this.getAllPOImports.emit()
        break;
      case 'released':

        break;
      default:
        break;
    }
  }

  // LOADED EDIT CONTAINER
  openLoadedEditCon(item: POImportList) {
    this.openLoadedContainerEdit.emit(item)
  }

  /// Date range clear
  onClearDate() {
    this.pickerDirective?.clear()
  }

  onPacking = (id: number) => {
    this.downloadPacking.emit(id)
  }

  qrDownload = (id: number) => {
    this.downloadQR.emit(id)
  }

  // Status Change
  onStatusOpen(item: POImportItem, poImportObj: POImportList, selectedPOSub: POImportsSub) {
    this.openStatusChangeModal.emit({item: item, poImportObj: poImportObj, selectedPOSub: selectedPOSub})
  }

  redirectToExp = (id: number) => {
    this.redirectToDetails.emit()
    this.redirectToContainerExpense.emit(id);
  }

  redirectoToCartonMapping = (id: number) => {
    this.redirectToDetails.emit()
    this.utilsService.redirectTo(`/users/purchases/carton-mapping/container/${id}`);
  }

  // Assign to CHA
  onAssignToCHA = (item: POImportItem, poImportObj: POImportList) => {
    this.openAssignToCHA.emit({item: item, poImportObj: poImportObj})
  }

  // Move to Completed
  onMoveToCompleted = (poImportObj: POImportList) => {
    this.openMoveToCompleted.emit(poImportObj)
  }
}

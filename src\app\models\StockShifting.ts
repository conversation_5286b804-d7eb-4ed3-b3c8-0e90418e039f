export const LocationType = {
    "AISLE": "A",
    "RACK": "R"
} as const

export interface StockShiftingReq {
    items: StockShiftingItemDropdown[]
    reason: { label: string, value: number, isActive: boolean }[]
    user: { label: string, value: number, isActive: boolean }[]
    warehouse: { label: string, value: number, isActive: boolean }[]
    aisle: { label: string, value: number, isActive: boolean }[]
    rack: { label: string, value: number, isActive: boolean }[]
}

export interface StockShiftingItemDropdown {
    displayName: string;
    formattedName: string;
    hsnCode: string;
    id: number
    itemName: string
    originalName: string
    skuId: string
}

export interface StockShiftingSaveDTO {
    shiftingDate: string,
    reasonMasterId: number,
    note: string,
    assignTo: number,
    reportTo: number,
    warehouseId: number,
    associateItemReqList: StockShiftingAssociateItemReqList[]
}

export interface StockShiftingAssociateItemReqList {
    qty: number,
    fromLocationId: number,
    fromLocationType: string,
    toLocationId: number,
    toLocationType: string,
    itemId: number,
    aisleId: number,
    rackId: number,
    aisleIdTo: number,
    rackIdTo: number,
    cLrackList: any,
    tLrackList: any,
}

export interface ItemMovementTicket {          
    qtyCarton: number,
    qtyLoose: number,
    marka: string,
    piecesPerCarton: number,
    transferTo: number,
    transferToType: string
}

export interface AvailableQtyRes {
    marka: string,
    cartonQty: number,
    piecesPerCarton: number,
    itemIndex: number;
    cartonQtySum: number;
    qtyLoose: number;
    qtyCarton: number;
    looseQty: number;
    looseQtySum: number;
    isLoose: boolean;
}

export interface AttachmentStockShifting {
    id: number;
    file: File;
    originalName: string;
    formattedName: string;
}
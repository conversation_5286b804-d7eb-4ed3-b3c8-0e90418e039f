<div class="modal-content">
  <div class="modal-header">
    <h5 class="modal-title" id="exampleModalLabel">{{isAdd() ? 'Add New' : 'Edit'}} Customer Lead</h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
        class='th th-close'></i></button>
  </div>
  <div class="modal-body" [formGroup]="formGroup()">
    <div class="row">
      <div class="col-4">
        <div class="form-group required">
          <label class="form-label">First Name</label>
          <input type="text" class="form-control" formControlName="firstName" placeholder="Enter First Name"
            [maxLength]="utilsService.validationService.MAX_30">
          <div class="message error-message"
            *ngIf="formGroup().controls['firstName'].hasError('required') &&  formGroup().controls['firstName'].touched">
            {{utilsService.validationService.F_NAME_REQ}}
          </div>
          <div class="message error-message"
            *ngIf="!formGroup().controls['firstName'].hasError('required') && !formGroup().controls['firstName'].valid && formGroup().controls['firstName'].touched">
            {{utilsService.validationService.F_NAME_INVALID}}
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="form-group">
          <label class="form-label">Middle Name</label>
          <input type="text" class="form-control" formControlName="middleName" placeholder="Enter Middle Name"
            [maxLength]="utilsService.validationService.MAX_30">
          <div class="message error-message"
            *ngIf="!formGroup().controls['middleName'].hasError('required') && !formGroup().controls['middleName'].valid && formGroup().controls['middleName'].touched">
            {{utilsService.validationService.M_NAME_INVALID}}
          </div>
          <div class="message error-message"
            *ngIf="formGroup().controls['middleName'].hasError('required') &&  formGroup().controls['middleName'].touched">
            {{utilsService.validationService.M_NAME_REQ}}
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="form-group">
          <label class="form-label">Last Name</label>
          <input type="text" class="form-control" formControlName="lastName" placeholder="Enter Last Name"
            [maxLength]="utilsService.validationService.MAX_30">
          <div class="message error-message"
            *ngIf="formGroup().controls['lastName'].hasError('required') &&  formGroup().controls['lastName'].touched">
            {{utilsService.validationService.L_NAME_REQ}}
          </div>
          <div class="message error-message"
            *ngIf="!formGroup().controls['lastName'].hasError('required') && !formGroup().controls['lastName'].valid && formGroup().controls['lastName'].touched">
            {{utilsService.validationService.L_NAME_INVALID}}
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="form-group theme-ngselect required">
          <label class="form-label">Phone No</label>
          <div class="form-control-wrapper">
            <div class="input-group input-group-select">
              <ng-select placeholder="Ph." [multiple]="false" [clearable]="false" [items]="countryCodeDropdown()"
                bindLabel="countryExtension" bindValue="id" formControlName="countryId">
              </ng-select>
              <input formControlName="phone" type="text" placeholder="Enter mobile number" class="form-control"
                [maxLength]="utilsService.validationService.MAX_30">
            </div>
            <div class="message error-message"
              *ngIf="formGroup().controls['countryId'].hasError('required') && formGroup().controls['countryId'].touched">
              {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
            </div>
            <div class="message error-message"
              *ngIf="formGroup().controls['phone'].hasError('required') && formGroup().controls['phone'].touched && formGroup().controls['countryId'].valid">
              {{utilsService.validationService.PHONE_NUMBER_REQUIRED}}
            </div>
            <div class="message error-message"
              *ngIf="!formGroup().controls['phone'].hasError('required') && !formGroup().controls['phone'].valid && formGroup().controls['phone'].touched && formGroup().controls['countryId'].valid">
              {{utilsService.validationService.PHONE_NUMBER_INVALID}}
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="form-group">
          <label class="form-label">Email Address</label>
          <input type="text" class="form-control" formControlName="email" placeholder="Enter Email">
          <div class="message error-message"
            *ngIf="!formGroup().controls['email'].hasError('required') && !formGroup().controls['email'].valid && formGroup().controls['email'].touched">
            {{utilsService.validationService.EMAIL_INVALID}}
          </div>
        </div>
      </div>
      <div class="col-12" *ngIf="!isFromInquiry">
        <div class="form-group d-flex justify-content-between required ">
          <label class="form-label">Status</label>

          <div class="switch-box">
            <label class="switch" htmlFor="switch">
              <input type="checkbox" id='switch' formControlName="isActive" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal-footer">
    <div class="modal-footer-group full-width-btn">
      <button (click)="onSaveCustomerLead()" type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>{{isAdd() ?
        'Save' : 'Update'}}</button>
      <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
    </div>
  </div>
</div>
<div class="page-content page-content-with-tabs">
    <div class="page-title-wrapper page-title-with-tab">
        <div class="page-title-left">
            <h4>Purchase Orders - Import</h4>
        </div>
        <div class="page-title-right">

            <button
                [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.VIEW_LOADING_CONTAINER}"
                *ngIf="selectedTab == enumForStatus.RECEIVED_CHINA" class="btn btn-sm btn-icon btn-primary"
                (click)="redirectToDetails()" [routerLink]="['/users/purchases/po-import/loading-container']"
                ngbTooltip="Loading Container" placement="left" container="body" triggers="hover">
                <i class="th th-outline-ship"></i>
            </button>
            <button [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.ADD_PO}"
                *ngIf="(selectedTab == enumForStatus.DRAFT || selectedTab == enumForStatus.PO_CREATED)"
                (click)="redirectToDetails()" class="btn btn-sm btn-icon btn-primary"
                [routerLink]="['/users/purchases/po-import/new-po-import']" ngbTooltip="Add New PO" placement="left"
                container="body" triggers="hover">
                <i class="th th-outline-add-circle"></i>
            </button>
            <button *ngIf="(selectedTab == enumForStatus.RELEASED && 
                            utilsService.checkPageAccess([utilsService.enumForPage.CARTON_MAPPING_L1,
                            utilsService.enumForPage.CARTON_MAPPING_L2,
                            utilsService.enumForPage.CARTON_MAPPING_L3]))"
                (click)="redirectToDetails()" class="btn btn-sm btn-icon btn-primary"
                [routerLink]="['/users/purchases/carton-mapping/']" ngbTooltip="Carton Mapping" placement="left" container="body"
                triggers="hover">
                <i class="th th-outline-box"></i>
            </button>
            <button *ngIf="selectedTab == enumForStatus.BREACH" class="btn btn-sm btn-icon btn-primary"
                data-bs-toggle="modal" data-bs-target="#auditTicketModal" ngbTooltip="Audit Ticket" placement="bottom"
                container="body" triggers="hover">
                <i class="th th-outline-ticket-2"></i>
            </button>

            <button [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.ADD_TEMPO}"
                *ngIf="(selectedTab == enumForStatus.TEMPO)" (click)="openTempoModal(null, 'Add', addEditTempoModal)"
                class="btn btn-sm btn-icon btn-primary" ngbTooltip="Add Tempo" placement="left" container="body" triggers="hover">
                <i class="th th-outline-truck"></i>
            </button>
            <button class="btn btn-sm btn-icon btn-outline-white" (click)="onRefresh()" ngbTooltip="Refresh" placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class="nav-tabs-outer nav-new nav-tabs-style2">
            <ul ngbNav #nav="ngbNav" class="nav-tabs" [destroyOnHide]="true" [(activeId)]="selectedTab" (activeIdChange)="onChangeTab($event)">
                <!-- Purchase Breach -->
                <li [ngbNavItem]="enumForStatus.BREACH">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-box-time"></i>Pur-Breach
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-breach />
                    </ng-template>
                </li>
                <!-- All -->
                <li [ngbNavItem]="enumForStatus.ALL">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="bi bi-list-ul"></i> All
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-all />
                    </ng-template>
                </li>
                <!-- Draft -->
                <li [ngbNavItem]="enumForStatus.DRAFT" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_PO}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-document-text"></i> Draft
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-draft [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()" [poImportList]="poImportList"
                            (onCollapse)="onCollapse($event)" (onCollapseChild)="onCollapseChild($event.parent_index, $event.i)"
                            (getAllPOImports)="getAllPOImports()" (onDelete)="openDeletePOModal($event, deletePOModal)"
                            [selectedTab]="selectedTab"
                            (openItemDeleteModal)="openItemDeleteFrom3rdTab($event.item, $event.subItem, deleteItemInPO)"
                            (redirectToDetails)="redirectToDetails()" (onClear)="onClear()" />
                    </ng-template>
                </li>
                <!-- PO Created -->
                <li [ngbNavItem]="enumForStatus.PO_CREATED" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_PO}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-box-tick"></i> PO Created
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-created [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" (onCollapse)="onCollapse($event)"
                            (onCollapseChild)="onCollapseChild($event.parent_index, $event.i)"
                            (getAllPOImports)="getAllPOImports()" (onDelete)="openDeletePOModal($event, deletePOModal)"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()" (onSearchPO)="onSearchPO($event)"
                            [selectedTab]="selectedTab" (openPOtoMoveToDraftModal)="openPOtoMoveToDraftModal($event)"
                            [associatedItems]="associatedItems"
                            (openPoToRecieveChina)="openPoToRecieveChina($event.item, $event.subItem, $event.isEdit, null, poToRecieveChina)"
                            (openItemDeleteModal)="openItemDeleteFrom3rdTab($event.item, $event.subItem, deleteItemInPO)"
                            (redirectToDetails)="redirectToDetails()" (onClear)="onClear()" (poPdfDownload)="downloadPOPDF($event.id, $event.supplier)"/>
                    </ng-template>
                </li>
                <!-- Received China -->
                <li [ngbNavItem]="enumForStatus.RECEIVED_CHINA" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_RC_CHINA}">
                    <button ngbNavLink class="nav-link" type="button">
                        <i class="th th-outline-house"></i> Received-China
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-received-china [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" [selectedTab]="selectedTab" (onCollapse)="onCollapse($event)"
                            (onCollapseChild)="onCollapseChild($event.parent_index, $event.i)"
                            (getAllPOImports)="getAllPOImports()" (checkIfAllSelected)="checkIfAllSelected()"
                            (saveCol)="saveCol()" (onDelete)="openDeletePOModal($event, deletePOModal)"
                            (openPoToRecieveChina)="openPoToRecieveChina($event.item, $event.subItem, $event.isEdit, $event.child, poToRecieveChina)"
                            (openRecToLoaded)="openRecToLoaded($event.item, $event.subItem, $event.isEdit, ImportDetailsModalRecChina)"
                            (openStatusChangeModal)="openStatusChangeModal($event.item, $event.poImportObj, $event.selectedPOSub, changeStatusItemModal)"
                            (onClear)="onClear()" (openItemDeleteModal)="openItemDeleteFrom3rdTab($event.item, $event.subItem, deleteItemInPO)"
                            (downloadPacking)="downloadPacking($event, enumForStatus.RECEIVED_CHINA)" />
                    </ng-template>
                </li>
                <!-- Loaded -->
                <li [ngbNavItem]="enumForStatus.LOADED" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_LOADED}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-ship"></i> Loaded
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-loaded [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" [selectedTab]="selectedTab"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()" (onCollapse)="onCollapse($event)"
                            (openConfirmToReleased)="openConfirmToReleased($event, loadedconfirmationModal)" (getAllPOImports)="getAllPOImports()"
                            (openLoadedContainerEdit)="openLoadedContainerEdit($event, loadedEditContainerModal)" (onClear)="onClear()"
                            (openItemDeleteModal)="openItemDeleteFrom3rdTab($event.item, $event.subItem, deleteItemInPO)"
                            (downloadPacking)="downloadPacking($event, enumForStatus.LOADED)"
                            (openStatusChangeModal)="openStatusChangeModal($event.item, $event.poImportObj, $event.selectedPOSub, changeStatusItemModal)"
                            (redirectToContainerExpense)="redirectToContainerExpense($event)" 
                            (redirectToDetails)="redirectToDetails()"/>
                    </ng-template>
                </li>
                <!-- Released -->
                <li [ngbNavItem]="enumForStatus.RELEASED" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_RELEASED}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-shield-tick"></i> Released
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-released [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" [selectedTab]="selectedTab"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()" (onCollapse)="onCollapse($event)"
                            (getAllPOImports)="getAllPOImports()" (openLoadedContainerEdit)="openLoadedContainerEdit($event, loadedEditContainerModal)"
                            (onClear)="onClear()" (downloadPacking)="downloadPacking($event, enumForStatus.RELEASED)"
                            (downloadQR)="downloadQR($event)"
                            (openStatusChangeModal)="openStatusChangeModal($event.item, $event.poImportObj, $event.selectedPOSub, changeStatusItemModal)"
                            (redirectToContainerExpense)="redirectToContainerExpense($event)"
                            (redirectToDetails)="redirectToDetails()"
                            (openAssignToCHA)="openAssignToCHA($event.item, $event.poImportObj, CHAassignModal)"
                            (openMoveToCompleted)="openMoveToCompleted($event)" />
                    </ng-template>
                </li>
                <!-- Tempo -->
                <li [ngbNavItem]="enumForStatus.TEMPO" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_TEMPO}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-truck-fast"></i> Tempo
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-tempo [poImportList]="poImportList" [selectedTab]="selectedTab"
                            [paginationRequest]="paginationRequest" [dropdown]="dropdown" (getAllPOImports)="getAllPOImports()"
                            (openTempoModal)="openTempoModal($event.obj, $event.status, addEditTempoModal)"
                            (openTempoToCompleted)="openTempoToCompleted($event, tempoToCompleted)"
                            (openDeleteTempoModal)="openDeleteTempoModal($event, deleteTempoModal)" (onClear)="onClear()"
                            (redirectToDetails)="redirectToDetails()" (warningGRNStart)="warningGRNStart($event)"
                            (redirectToTempoExpense)="redirectToTempoExpense($event)" />
                    </ng-template>
                </li>
                <!-- GRN -->
                <li [ngbNavItem]="enumForStatus.GRN" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_GRN}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-box-tick"></i> GRN
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-grn [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" [selectedTab]="selectedTab" (onClear)="onClear()"
                            (getAllPOImports)="getAllPOImports()" (onCollapse)="onCollapse($event)"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()"
                            (redirectToDetails)="redirectToDetails()" />
                    </ng-template>
                </li>
                <!-- Completed -->
                <li [ngbNavItem]="enumForStatus.COMPLETED" [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_COMPLETED}">
                    <button ngbNavLink class="nav-link" type="button"> <i
                            class="th th-outline-tick-circle"></i> Completed
                    </button>
                    <ng-template ngbNavContent>
                        <app-po-import-completed [headerObj]="headerObj" [allHeaderArr]="headerObj?.allHeaderArr"
                            [dropdown]="dropdown" [paginationRequest]="paginationRequest" [columnArr]="headerObj?.columnArr"
                            [poImportList]="poImportList" [selectedTab]="selectedTab" (onClear)="onClear()"
                            (getAllPOImports)="getAllPOImports()" (onCollapse)="onCollapse($event)"
                            (checkIfAllSelected)="checkIfAllSelected()" (saveCol)="saveCol()"
                            (redirectToDetails)="redirectToDetails()" (redirectToContainerExpense)="redirectToContainerExpense($event)"/>
                    </ng-template>
                </li>
            </ul>
            <div [ngbNavOutlet]="nav" class="tab-content pt-0"></div>
        </div>
    </div>
</div>

<!-- ---------------------------- Delete PO Modal ----------------------------- -->
<ng-template #deletePOModal let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete PO Import.
                            <ng-container *ngIf="selectedTab !== enumForStatus.DRAFT">
                                <ng-container
                                    *ngIf="selectedTab !== enumForStatus.RECEIVED_CHINA"><b>({{selectedPO?.purchaseOrder}})</b></ng-container>
                                <ng-container
                                    *ngIf="selectedTab == enumForStatus.RECEIVED_CHINA"><b>({{selectedPO?.rcId}})</b></ng-container>
                            </ng-container>
                        </p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="deletePO(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ---------------------------- Delete PO Modal ----------------------------- -->

<div *ngIf="false" class="modal modal-theme fade" id="auditTicketModal" tabindex="-1" aria-labelledby="auditTicketModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditTicketModalLabel">Create Audit Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Ticket Subject</label>
                            <ng-select placeholder="Ticket Subject" [multiple]="false" [clearable]="false"
                                [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect">
                            <label class="form-label">Assign to</label>
                            <ng-select placeholder="Assign to" [multiple]="false" [clearable]="false" [items]="demo"
                                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Date</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-icon-end">
                                    <i class="th th-outline-calendar"></i>
                                    <input type="text" class="form-control" placeholder="DD/MM/YYYY">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Note</label>
                            <textarea class="form-control" rows="3" placeholder="Enter Note"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- ------------------- Change Status Forms Modal Start  --------------------------- -->
<ng-template #changeStatusItemModal let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Change Status</h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
            
                <div class="product-details-container product-status">
                    <div class="product-details-left d-flex justify-content-start">
                        <a class="d-flex flex-row">
                            <div class="product-image">
                                <img *ngIf="itemObj?.item?.formattedName" loading="lazy"
                                    [src]="itemObj.item.formattedName ? (utilsService.imgPath + itemObj.item.formattedName) : ''"
                                    alt="valamji">
                                <ng-container *ngIf="!itemObj?.item?.formattedName">{{
                                    itemObj?.displayName?.charAt(0).toUpperCase()
                                    }}
                                </ng-container>
                            </div>
                            <div class="product-title ms-2">
                                <div class="tbl-user-text">
                                    <p class="text-black">{{ itemObj?.item?.skuId }}</p>
                                    <span class="grid-item">
                                        <div [title]="itemObj?.item?.displayName || ''">{{ itemObj?.item?.displayName }}</div>
                                    </span>
                                </div>
                            </div>
                        </a>
                        <div class="product-title ms-1">
                            <div class="tbl-user-text">
                                <span>Current Status</span>
                                <p class="text-black">{{(selectedTab) | underscore}}</p>
                            </div>
                        </div>
                        <div class="product-title ms-1">
                            <div class="tbl-user-text">
                                <span>Marka</span>
                                <p class="text-black">{{itemObj?.marka ? itemObj?.marka : '-'}}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" [formGroup]="changeStatusFG">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Status</label>
                            <ng-select (change)="onFilterContainer()" [(ngModel)]="changeStatusId" class="" formControlName="changeStatusId"
                                placeholder="Select New Status" [multiple]="false" [clearable]="false" [items]="statusDropdownCS"
                                bindLabel="label" bindValue="value">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="changeStatusFG.controls['changeStatusId'].hasError('required') &&  changeStatusFG.controls['changeStatusId'].touched">
                                {{utilsService.validationService.STATUS_REQ}}
                            </div>
                        </div>
                    </div>
                    <ng-container *ngIf="(changeStatusId == enumForStatus.LOADED || changeStatusId == enumForStatus.RELEASED)">
                        <div class="col-12">
                            <div class="form-group theme-ngselect required">
                                <label class="form-label">Container</label>
                                <ng-select formControlName="changeStatusContainer" [(ngModel)]="changeStatusContainer" class=""
                                    placeholder="Select Container" [multiple]="false" [clearable]="false" [items]="containerDropdown"
                                    bindLabel="containerName" bindValue="id">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="changeStatusFG.controls['changeStatusContainer'].hasError('required') &&  changeStatusFG.controls['changeStatusContainer'].touched">
                                    {{utilsService.validationService.CONTAINER_REQ}}
                                </div>
                            </div>
                        </div>
                        <ng-container *ngIf="changeStatusId == enumForStatus.LOADED">
                            <div class="col-12">
                                <div class="form-group required"
                                    [ngClass]="{'form-error': selectedTab !== enumForStatus.RELEASED ? isValidLoadedReq(itemObj) : isValidReleasedToLoaded(itemObj)}">
                                    <label class="form-label">Loaded Qty (Carton)</label>
                                    <input placeholder="Enter Loaded Qty" [(ngModel)]="itemObj.loadedQtyField" formControlName="loadedQtyField"
                                        [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control" mask="separator.0"
                                        thousandSeparator="">
                                    <div class="message error-message"
                                        *ngIf="changeStatusFG.controls['loadedQtyField'].hasError('required') &&  changeStatusFG.controls['loadedQtyField'].touched">
                                        {{utilsService.validationService.LOADED_QTY_REQ}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-12" *ngIf="selectedTab !== enumForStatus.RELEASED">
                                <span *ngIf="itemObj.calcReceiveKey" class="d-block w-100 fs-13"><b>Received Qty:
                                        {{itemObj.calcReceiveKey}}</b></span>
                                <span *ngIf="itemObj.calcLoadedQty" class="d-block w-100 fs-13"><b>Loaded Qty:
                                        {{itemObj.calcLoadedQty}}</b></span>
                            </div>
                            <div class="col-12" *ngIf="selectedTab == enumForStatus.RELEASED">
                                <span *ngIf="itemObj.releasedQty" class="d-block w-100 fs-13"><b>Released Qty:
                                        {{itemObj.releasedQty}}</b></span>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="changeStatusId == enumForStatus.RELEASED">
                            <div class="col-12">
                                <div class="form-group required" [ngClass]="{'form-error': isValidLoadedToRelease(itemObj)}">
                                    <label class="form-label">Release Qty (Carton)</label>
                                    <input placeholder="Enter Release Qty" [(ngModel)]="itemObj.releaseQtyField"
                                        formControlName="releaseQtyField" [maxlength]="utilsService.validationService.MAX_10" type="text"
                                        class="form-control" mask="separator.0" thousandSeparator="">
                                    <div class="message error-message"
                                        *ngIf="changeStatusFG.controls['releaseQtyField'].hasError('required') &&  changeStatusFG.controls['releaseQtyField'].touched">
                                        {{utilsService.validationService.REL_QTY_REQ}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <span *ngIf="itemObj?.loadedQty" class="d-block w-100 fs-13"><b>Loaded Qty:
                                        {{itemObj?.loadedQty}}</b></span>
                            </div>
                        </ng-container>
                    </ng-container>
                    
                    <ng-container *ngIf="(changeStatusId == enumForStatus.RECEIVED_CHINA)">
                        <div class="col-12">
                            <div class="form-group" [ngClass]="{'form-error': isValidLoadedtoRCReq(itemObj)}">
                                <label class="form-label">Received Qty (Carton)</label>
                                <input placeholder="Enter Received Qty" [(ngModel)]="itemObj.rcCartonField" formControlName="rcCartonField"
                                    [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control" mask="separator.0"
                                    thousandSeparator="">
                                <div class="message error-message"
                                    *ngIf="changeStatusFG.controls['rcCartonField'].hasError('required') &&  changeStatusFG.controls['rcCartonField'].touched">
                                    {{utilsService.validationService.REC_QTY_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <span *ngIf="itemObj?.loadedQty && selectedTab == enumForStatus.LOADED" class="d-block w-100 fs-13"><b>Loaded
                                    Qty:
                                    {{itemObj?.loadedQty}}</b>
                            </span>
                            <span *ngIf="itemObj?.loadedQty && selectedTab == enumForStatus.RELEASED" class="d-block w-100 fs-13"><b>Released
                                    Qty:
                                    {{itemObj?.loadedQty}}</b>
                            </span>
                        </div>
                    </ng-container>
                </div>
            
            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onStatusChange(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Save</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ------------------- Change Status Forms Modal End  --------------------------- -->


<!-- PO TO DRAFT -->
<div *ngIf="false" class="modal modal-theme modal-confirmation modal-warning-two fade" id="fromPoToDraft" tabindex="-1"
    aria-labelledby="fromPoToDraft" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to move this Purchase Order ({{selectedPO?.purchaseOrder}}) from <br />
                            <b>{{utilsService.poImportStatus.PO_CREATED}}</b> to
                            <b>{{utilsService.poImportStatus.DRAFT}}</b>
                        </p>
                        <!-- <p><b>Note:</b> Existing main branch will not be treated as main branch</p> -->
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="onMovetoDraft()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>Confirm</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ------------------- PO TO CHINA Modal Start  ------------------------- -->
<ng-template #poToRecieveChina let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">
                    @if (isEditPotoRec) {
                        Edit
                    }
                    Received Cartons (Shop No #{{ poImportObj?.supplierShortCode }})
                    @if (itemObj?.rcId) {
                        (RC Id: {{ itemObj.rcId }})
                    }
                </h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="row">
                    <div class="col-12">
                        <div class="form-group w-100">
                            <div class="form-group-icon-start">
                                <i class='th th-outline-search-normal-1 icon-broder '></i>
                                <input type="text" placeholder="Search Marka" class="form-control"
                                    [formControl]="searchControlPOCreatedToRC">
                            </div>
                        </div>
                        <span class="inner-title">Total Received: {{totalCount.overallRCQty ? totalCount.overallRCQty : 0}}</span>
                    </div>
                </div>

                <div class="modal-table">
                    <div class="card card-theme  ">
                        <div class="card-body p-0">
                            <div class="table-responsive po-rc-china">
                                <table class="table-theme table-hover table table-bordered table-sticky table-footer-sticky">
                                    <thead class="border-less">
                                        <tr>
                                            <th class="d-flex align-items-center gap-2">
                                                Item Details
                                            </th>
                                            <th>QTY/Cartons Ordered</th>
                                            <th>Received Qty (Carton)</th>
                                            <th *ngIf="isEditPotoRec">Price (RMB) / PCS</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of associatedItems; index as i">
                                            <td class="tbl-user w-25">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="tbl-user-wrapper">
                                                        <div class="tbl-user-image" *ngIf="item?.item">
                                                            <img *ngIf="item.item?.formattedName" loading="lazy"
                                                                [src]="item.item.formattedName ? (utilsService.imgPath + item.item.formattedName) : ''"
                                                                alt="valamji">
                                                            <ng-container *ngIf="!item.item?.formattedName">{{
                                                                item.displayName?.charAt(0).toUpperCase()
                                                                }}
                                                            </ng-container>
                                                        </div>
                                                        <div class="tbl-user-text-action">
                                                            <div class="tbl-user-text">
                                                                <p>{{item?.item?.displayName}}</p>
                                                                <span>{{item?.item?.skuId}}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="w-25" *ngIf="!isEditPotoRec">
                                                <span class="d-block w-100">{{item.marka}}</span>
                                                <span class="d-block w-100">{{item.poCarton}} Carton, Total Qty:
                                                    {{item.totalPcsQty}}</span>
                                                <span class="d-block w-100"><b>Total CTN:</b> {{item.poCarton}}</span>
                                                <span *ngIf="item.receivedQty" class="d-block w-100"><b>Received Qty:</b> {{item.receivedQty}}</span>
                                                <span class="d-block w-100"><b>Pending:</b> {{(item.poCarton - (item.receivedQty ? item.receivedQty : 0))}}</span>
                                            </td>

                                            <td class="w-25" *ngIf="isEditPotoRec">
                                                <span class="d-block w-100">{{item.marka}}</span>
                                                <span class="d-block w-100">{{item.poCarton}} Carton, Total Qty:
                                                    {{item.totalPcsQty}}</span>
                                                <!-- <span *ngIf="item.rcCarton" class="d-block w-100"><b>Received Qty:
                                                        {{item.rcCarton}}</b></span> -->
                                            </td>

                                            <td class="w-25" *ngIf="!isEditPotoRec">
                                                <div class="form-group form-group-sm form-group-100"
                                                    [ngClass]="{'form-error': (item.poCarton < ((item.receivedQty ? item.receivedQty : 0) + item.rcCartonField) || item.rcCartonField === 0)}">
                                                    <input id="rc-{{i}}" (input)="onChangeRecQty(item)" [(ngModel)]="item.rcCartonField"
                                                        [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control" mask="separator.0"
                                                        thousandSeparator="">
                                                </div>
                                            </td>
                                            <td class="w-25" *ngIf="isEditPotoRec">
                                                @let validation = isInvalidCartonField(item.rcCartonField);
                                                <div class="form-group form-group-sm form-group-100" [ngClass]="{'form-error': validation}">
                                                    <input id="rc-{{i}}" (input)="onChangeRecQty()" [(ngModel)]="item.rcCartonField"
                                                        [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control" mask="separator.0"
                                                        thousandSeparator="">
                                                </div>
                                            </td>
                                            <td class="w-25" *ngIf="isEditPotoRec">
                                                <div class="form-group form-group-sm form-group-100"
                                                    [ngClass]="{'form-error': item.rcCartonField && (!item.pricePerItem || item.pricePerItem === 0)}">
                                                    <input [disabled]="!item.isPricePerItemEdit" id="pricePerItem-{{i}}" (input)="onChangeRecQty()" [(ngModel)]="item.pricePerItem"
                                                        [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control" mask="separator.2"
                                                        thousandSeparator="">
                                                </div>
                                            </td>
                                        </tr>
                                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(associatedItems)">
                                            <td colspan="20" class="text-center">
                                                <span
                                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot>
                                        <tr class="tbl-total" [ngClass]="{'tbl-bg-success': totalCount?.receivedQtyTotal == totalCount?.totalPendingCarton}">
                                            <td>Total</td>
                                            <td>{{totalCount?.totalPendingCarton ? totalCount?.totalPendingCarton : 0}} Carton,
                                                Total Qty: {{totalCount?.totalPendingQty ?
                                                totalCount?.totalPendingQty : 0}}</td>
                                            <td>{{totalCount?.receivedQtyTotal ? totalCount?.receivedQtyTotal : 0}}</td>
                                            <!-- <td *ngIf="isEditPotoRec">{{totalCount?.pricePerItemTotal ? totalCount?.pricePerItemTotal : 0}}</td> -->
                                            <td *ngIf="isEditPotoRec">-</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button [disabled]="disableIfNoItemOrEmpty(true)" (click)="onSaveUpdatePoToRC(modal)" type="button"
                        class="btn btn-primary btn-icon-text">
                        <i class="th th-outline-tick-circle"></i>Save </button>
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ------------------- PO TO CHINA Modal End  ------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                     RC CHINA TO LOADED Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<ng-template #ImportDetailsModalRecChina let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Import Details
                    <span *ngIf="selectedPO?.rcId">(Receive ID : #{{selectedPO?.rcId}} | {{selectedPO?.rcDate | date:
                        'dd/MM/YYYY'}})</span>
                </h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [ngClass]="{'available-qty-scroll': stageStep === enumForRcToLoadedStage.SHIPMENT_DETAILS}">
                <div class="product-details-container product-import">
                    <div class="import-items-wrapper">
                        <div class="import-items-nav">
                            <div class="import-items-list">
                                <ul class="list-unstyled">
                                    <li [ngClass]="{'done': stageStep === enumForRcToLoadedStage.SELECT_CARTONS, 
                                                    'active': stageStep === enumForRcToLoadedStage.SHIPMENT_DETAILS}">
                                        <div class="step-boxs">
                                            <div class="step-number">1</div>
                                            <div class="step-done">
                                                <img src="assets/images/step-done.svg" alt="valamji">
                                            </div>
                                            <div>
                                                <h6>Shipment Details</h6>
                                                <p>Enter shipment details</p>
                                            </div>
                                        </div>
                                    </li>
                                    <li [ngClass]="{'active': stageStep === enumForRcToLoadedStage.SELECT_CARTONS}">
                                        <div class="step-boxs">
                                            <div class="step-number">2</div>
                                            <div class="step-done">
                                                <img src="assets/images/step-done.svg" alt="valamji">
                                            </div>
                                            <div>
                                                <h6>Select Cartons</h6>
                                                <p>Enter CTN To Load</p>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>

                <ng-container *ngIf="stageStep === enumForRcToLoadedStage.SHIPMENT_DETAILS">
                    <div class="row" [formGroup]="importDetailsForm">
                        <div class="col-12">
                            <div class="form-group theme-ngselect  required">
                                <label class="form-label">Select importer</label>
                                <ng-select formControlName="importer" class="" placeholder="Select Importer"
                                    [multiple]="false" [(ngModel)]="importCHtoLoadedObj.importerId" [clearable]="false"
                                    [items]="importDropdown?.importer" bindLabel="label" bindValue="value">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="importDetailsForm.controls['importer'].hasError('required') &&  importDetailsForm.controls['importer'].touched">
                                    {{utilsService.validationService.IMPORTER_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group theme-ngselect required">
                                <label class="form-label">Container Name</label>
                                <!-- <ng-select formControlName="containerId" class=""
                                    placeholder="Type to add new or select" [hideSelected]="false"
                                    [(ngModel)]="importCHtoLoadedObj.selectedContainer" [clearable]="false"
                                    addTagText="Add New Container" [addTag]="true" [items]="importDropdown?.container"
                                    bindLabel="containerName" [clearSearchOnAdd]="true">
                                </ng-select> -->
                                <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="importCHtoLoadedObj.selectedContainer"
                                    formControlName="containerId" type="text" class="form-control" placeholder="Enter Container Name">
                                <div class="message error-message"
                                    *ngIf="importDetailsForm.controls['containerId'].hasError('required') &&  importDetailsForm.controls['containerId'].touched">
                                    {{utilsService.validationService.CONTAINER_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!importDetailsForm.controls['containerId'].hasError('required') && !importDetailsForm.controls['containerId'].valid && importDetailsForm.controls['containerId'].touched">
                                    {{utilsService.validationService.CONTAINER_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group  required">
                                <label class="form-label">Expected Delivery Date</label>
                                <div class="form-group-icon-end">
                                    <i (click)="e.toggle()" class="th th-outline-calendar-1"></i>
                                    <input [minDate]="minDateNGB" (click)="e.toggle()" (keydown.space)="e.toggle()"
                                        formControlName="date" readonly type="text"
                                        [(ngModel)]="importCHtoLoadedObj.t_expectedDeliveryDate" class="form-control"
                                        placeholder="dd/mm/yyyy" ngbDatepicker #e="ngbDatepicker">
                                </div>
                                <div class="message error-message"
                                    *ngIf="importDetailsForm.controls['date'].hasError('required') &&  importDetailsForm.controls['date'].touched">
                                    {{utilsService.validationService.DATE_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group ">
                                <label class="form-label">Expected Delivery Days</label>
                                <input mask="separator.0" thousandSeparator=""
                                    [maxlength]="utilsService.validationService.MAX_5" [(ngModel)]="importCHtoLoadedObj.expectedDeliveryDay"
                                    formControlName="exp_days" type="text" class="form-control" placeholder="Enter Days">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group theme-ngselect  required">
                                <label class="form-label">Shipment Type</label>
                                <ng-select (change)="changeShipmentType()" formControlName="shipmentType" class="" placeholder="Select Shipment Type"
                                    [multiple]="false" [(ngModel)]="importCHtoLoadedObj.shippingTypes"
                                    [clearable]="false" [items]="importDropdown?.shippingType" bindLabel="label"
                                    bindValue="value">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="importDetailsForm.controls['shipmentType'].hasError('required') &&  importDetailsForm.controls['shipmentType'].touched">
                                    {{utilsService.validationService.SHIPPING_TYPE_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6" *ngIf="shippingType">
                            <div class="form-group required" [ngSwitch]="shippingType">
                                <label class="form-label">
                                    <ng-container *ngSwitchCase="enumForShippingType.CBM">CBM/Carton</ng-container>
                                    <ng-container *ngSwitchCase="enumForShippingType.WEIGHT">Cost / kg (RS)</ng-container>
                                    <ng-container *ngSwitchCase="enumForShippingType.PERCENTAGE">Total Expense / Piece (₹) [%]</ng-container>
                                    <ng-container *ngSwitchCase="enumForShippingType.PIECE">Expense/PCS</ng-container>
                                    <ng-container *ngSwitchCase="enumForShippingType.DONE">Fixed Price</ng-container>
                                </label>
                                <input [maxlength]="utilsService.validationService.MAX_10"
                                    [mask]="shippingType === enumForShippingType.CBM ? 'separator.5' : 'separator.2'" thousandSeparator=""
                                    [(ngModel)]="importCHtoLoadedObj.cbmPrice" formControlName="cbmPrice" type="text" class="form-control"
                                    placeholder="Enter Price">
                                <div class="message error-message"
                                    *ngIf="importDetailsForm.controls['cbmPrice'].hasError('required') &&  importDetailsForm.controls['cbmPrice'].touched">
                                    {{utilsService.validationService.PRICE_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!importDetailsForm.controls['cbmPrice'].hasError('required') && !importDetailsForm.controls['cbmPrice'].valid && importDetailsForm.controls['cbmPrice'].touched">
                                    {{utilsService.validationService.PRICE_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group ">
                                <label class="form-label">Tracking Link</label>
                                <input [maxlength]="utilsService.validationService.MAX_500"
                                    formControlName="trackingLink" type="text" class="form-control"
                                    placeholder="Enter tracking link" [(ngModel)]="importCHtoLoadedObj.trackingLink">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label">Note</label>
                                <textarea [maxlength]="utilsService.validationService.MAX_500" formControlName="note"
                                    [(ngModel)]="importCHtoLoadedObj.note" class="form-control" rows="2"
                                    placeholder="Enter Note"></textarea>
                            </div>
                        </div>
                    </div>
                </ng-container>

                <ng-container *ngIf="stageStep === enumForRcToLoadedStage.SELECT_CARTONS">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group w-100">
                                <div class="form-group-icon-start">
                                    <i class='th th-outline-search-normal-1 icon-broder '></i>
                                    <input type="text" placeholder="Search Marka" class="form-control"
                                        [formControl]="searchControl">
                                </div>
                            </div>
                            <span class="inner-title">Total Loaded CTN: {{totalCount.overallLoadedQty ? totalCount.overallLoadedQty : 0}}</span>
                        </div>
                    </div>

                    <div class="modal-table">
                        <div class="card card-theme  ">
                            <div class="card-body p-0">
                                <div class="table-responsive po-rc-china-two">
                                    <table
                                        class="table-theme table-hover table-hover table table-bordered table-sticky table-footer-sticky">
                                        <thead class="border-less">
                                            <tr>
                                                <th class="d-flex align-items-center gap-2">
                                                    Item Details
                                                </th>
                                                <th>Received In China</th>
                                                <th>Loaded CTN</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let item of associatedItems; index as i; trackBy: trackBy">
                                                <td class="tbl-user w-25">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <div class="tbl-user-wrapper">
                                                            <div class="tbl-user-image" *ngIf="item?.item">
                                                                <img *ngIf="item.item?.formattedName" loading="lazy"
                                                                    [src]="item.item.formattedName ? (utilsService.imgPath + item.item.formattedName) : ''"
                                                                    alt="valamji">
                                                                <ng-container *ngIf="!item.item?.formattedName">{{
                                                                    item.displayName?.charAt(0).toUpperCase()
                                                                    }}
                                                                </ng-container>
                                                            </div>
                                                            <div class="tbl-user-text-action">
                                                                <div class="tbl-user-text">
                                                                    <p>{{item?.item?.displayName}}</p>
                                                                    <span>{{item?.item?.skuId}}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="w-25">
                                                    <span class="d-block w-100">{{item.marka}}</span>
                                                    <span class="d-block w-100">{{item.calcReceiveKey}} CTN, Total
                                                        Qty:
                                                        {{item.totalPcsQty}}</span>
                                                    <span *ngIf="item.calcReceiveKey" class="d-block w-100"><b>Received
                                                            CTN: {{item.calcReceiveKey}}</b></span>
                                                    <span *ngIf="item.calcLoadedQty" class="d-block w-100"><b>Loaded
                                                            CTN: {{item.calcLoadedQty}}</b></span>

                                                    <span class="d-block w-100"><b>Pending Loaded CTN:
                                                            {{item.calcReceiveKey - item.calcLoadedQty}}</b></span>
                                                </td>
                                                <td class="w-25">
                                                    <div class="form-group form-group-sm form-group-100"
                                                        [ngClass]="{'form-error': isValidLoadedReq(item)}">
                                                        <input (input)="onChangeLoadQty()"
                                                            [(ngModel)]="item.loadedQtyField"
                                                            [maxlength]="utilsService.validationService.MAX_10"
                                                            type="text" class="form-control" mask="separator.0"
                                                            thousandSeparator="">
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr class="tbl-total" [ngClass]="{'tbl-bg-success': totalCount?.loadedQty == totalCount?.totalPendingCarton}">
                                                <td>Total</td>
                                                <td>{{totalCount?.totalPendingCarton ? totalCount?.totalPendingCarton : 0}}
                                                    CTN, Total Qty:
                                                    {{totalCount?.totalPendingQty ?
                                                    totalCount?.totalPendingQty : 0}}</td>
                                                <td>{{totalCount?.loadedQty ? totalCount?.loadedQty : 0}}</td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>

            <div class="modal-footer">

                <div class="modal-footer-group full-width-btn justify-content-between"
                    *ngIf="stageStep === enumForRcToLoadedStage.SHIPMENT_DETAILS">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onNextStep(enumForRcToLoadedStage.SELECT_CARTONS)" type="button"
                        class="btn btn-primary btn-icon-text">
                        Continue <i class="th th-outline-arrow-right-1"></i>
                    </button>
                </div>

                <div class="modal-footer-group full-width-btn justify-content-between"
                    *ngIf="stageStep === enumForRcToLoadedStage.SELECT_CARTONS">
                    <div class="">
                        <button type="button" class="btn btn-outline-white"
                            (click)="onNextStep(enumForRcToLoadedStage.SHIPMENT_DETAILS)">Back</button>
                    </div>
                    <div class="d-flex gap-2">
                        <button [disabled]="disableIfNoItemOrEmpty(false)" (click)="onSaveCHtoLoaded(modal)" type="button"
                            class="btn btn-primary btn-icon-text">
                            <i class="th th-outline-tick-circle"></i>Save </button>
                        <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ----------------------------------------------------------------------- -->
<!--                  RC CHINA TO LOADED Modal End              -->
<!-- ----------------------------------------------------------------------- -->


<!-- ---------------------------- Loaded to Release Modal ----------------------------- -->
<ng-template #loadedconfirmationModal let-modal>
    <div class="modal-theme modal-confirmation modal-approve">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-tick-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>
                            You want to convert container <b>{{poImportObj?.containerName}}</b> in to
                            <b>{{enumForStatus.RELEASED | titlecase}}</b>
                        </p>
                    </div>
                </div>



                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class=" btn btn-sm btn-outline-white btn-icon-text "
                        (click)="modal.close()">No, Change my mind</button>
                    <button (click)="onConfirmToRelease(modal)" type="button" class="btn btn-sm btn-primary btn-icon-text">
                        <i class="th th-outline-tick-circle"></i> Yes, Change</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ----------------------------  Loaded to Release Modal ----------------------------- -->

<!-- ------------------------ LOADED - EDIT CONTAINER  START -------------------->
<ng-template #loadedEditContainerModal let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Edit Container No</h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="importDetailsForm">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Container Name</label>
                            <!-- <ng-select (change)="onChangeEditContainer()" formControlName="containerId" class="" placeholder="Type to add new or select"
                                addTagText="Add New Container" [hideSelected]="false"
                                [(ngModel)]="poImportObj.selectedContainer" [clearable]="false" [addTag]="true"
                                [items]="importDropdown?.container" bindLabel="containerName" [clearSearchOnAdd]="true">
                            </ng-select> -->
                            <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="poImportObj.selectedContainer"
                                    formControlName="containerId" type="text" class="form-control" placeholder="Enter Container Name">
                            <div class="message error-message"
                                *ngIf="importDetailsForm.controls['containerId'].hasError('required') &&  importDetailsForm.controls['containerId'].touched">
                                {{utilsService.validationService.CONTAINER_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importDetailsForm.controls['containerId'].hasError('required') && !importDetailsForm.controls['containerId'].valid && importDetailsForm.controls['containerId'].touched">
                                {{utilsService.validationService.CONTAINER_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group  ">
                            <label class="form-label">Note</label>
                            <textarea [maxlength]="utilsService.validationService.MAX_500" formControlName="note"
                                [(ngModel)]="poImportObj.note" class="form-control" rows="2"
                                placeholder="Enter Note"></textarea>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button (click)="modal.close()" type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="onEditLoadedContainer(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Update</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ------------------------- LOADED - EDIT CONTAINER  End  --------------------->

<!-- ---------------------------- Delete PO Item  Modal ----------------------------- -->
<ng-template #deleteItemInPO let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete Item <b>{{itemObj?.item?.displayName}}</b> from <b>{{selectedTab | underscore}}</b></p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onDeletePOItem(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ---------------------------- Delete PO Item Modal ----------------------------- -->

<!-- -------------------------Tempo Add/Edit Modal Start    --------------------------- -->
<ng-template #addEditTempoModal let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal == 'Add' ? 'Add' : 'Edit'}} Vehicle</h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="row" [formGroup]="tempFormGroup">
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label"> Vehicle No </label>
                            <input type="text" class="form-control" placeholder="Enter Vehicle No"
                                [maxlength]="utilsService.validationService.MAX_30" formControlName="vehicleNo"
                                [(ngModel)]="tempoObj.vehicleNo">
                            <div class="message error-message"
                                *ngIf="tempFormGroup.controls['vehicleNo'].hasError('required') &&  tempFormGroup.controls['vehicleNo'].touched">
                                {{utilsService.validationService.VEH_NO_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!tempFormGroup.controls['vehicleNo'].hasError('required') && !tempFormGroup.controls['vehicleNo'].valid && tempFormGroup.controls['vehicleNo'].touched">
                                {{utilsService.validationService.VEH_NO_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group single-date required">
                            <label class="form-label"> Tempo Loading Date & Time</label>
                            <div class="form-group-icon-end date-tempo-clear">
                                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                                <input class="form-control" type="text" ngxDaterangepickerMd readonly
                                    [(ngModel)]="tempoObj.tempDate" placeholder="DD/MM/YYYY, HH:MM" [autoApply]="true"
                                    [singleDatePicker]="true" [timePicker24Hour]="false" [timePicker]="true"
                                    startKey="start" formControlName="date" [closeOnAutoApply]="false" [maxDate]="tempoMaxDate"
                                    [locale]="{format: 'DD/MM/YYYY h:mm A', displayFormat: 'DD/MM/YYYY h:mm A'}">
                            </div>
                            <div class="message error-message"
                                *ngIf="tempFormGroup.controls['date'].touched && !tempoObj.tempDate">
                                {{utilsService.validationService.DATE_TIME_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <div class="form-label">Container</div>
                            <ng-select formControlName="slab" placeholder="Select Container" [multiple]="true" [clearable]="false"
                                [items]="tempoContainerDropdown" bindLabel="containerName" bindValue="id" [(ngModel)]="tempoObj.containerIDs"
                                formControlName="containerIDs">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="tempFormGroup.controls['containerIDs'].hasError('required') &&  tempFormGroup.controls['containerIDs'].touched">
                                {{utilsService.validationService.CONTAINER_REQ}}
                            </div>
                        </div>
                    </div>
                    <!-- <div class="col-12">
                        <div class="form-group ">
                            <div class="carton-card-wrapper">
                                <div class="carton-card card">
                                    <div class="card-body">
                                        <div class="card-details">
                                            <p>Released Cartons</p>
                                            <h6>{{tempoObj.releasedCarton ? tempoObj.releasedCarton : 0}}</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="carton-card card">
                                    <div class="card-body">
                                        <div class="card-details">
                                            <p>Available Cartons</p>
                                            <h6>{{tempoObj.availableCarton ? tempoObj.availableCarton : 0}}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> -->
                    <div class="col-12">
                        <div class="form-group required"
                            [ngClass]="{'form-error': tempFormGroup.controls['cartonQtyVehicle'].invalid && tempFormGroup.controls['cartonQtyVehicle'].touched}">
                            <label class="form-label"> Carton Qty (Loading In Vehicle) </label>
                            <input type="text" class="form-control" placeholder="Enter Carton Qty" formControlName="cartonQtyVehicle"
                                [(ngModel)]="tempoObj.cartonQtyVehicle" mask="separator.0" thousandSeparator=""
                                [maxlength]="utilsService.validationService.MAX_10">
                            <!-- <div class="message error-message"
                                *ngIf="tempFormGroup.controls['cartonQtyVehicle'].hasError('required') &&  tempFormGroup.controls['cartonQtyVehicle'].touched">
                                {{utilsService.validationService.CARTON_QTY_REQ}}
                            </div> -->
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onSaveTempo(modal)" type="button" class="btn btn-primary btn-icon-text">
                        <i class="th th-outline-tick-circle"></i> {{statusForModal == 'Add' ? 'Save' :
                        'Update'}}</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- -------------------------Tempo Add/Edit Modal End    --------------------------- -->

<!------------------------------ TEMPO MOVE TO COMPLETED ------------------------------->
<ng-template #tempoToCompleted let-modal>
    <div class="modal-theme modal-confirmation modal-warning">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to move this Tempo <b>({{poImportObj?.vehicleNo}})</b> status to
                            <b>{{enumForTempoStatus.COMPLETED | titlecase}}</b>
                        </p>
                        <p><b>Note:</b> Ensure all expenses are recorded before marking this tempo as completed.</p>
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onTempoMarkAsCompleted(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>Confirm</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!------------------------------ TEMPO MOVE TO COMPLETED ------------------------------->

<!-- ---------------------------- Delete Tempo Modal ----------------------------- -->
<ng-template #deleteTempoModal let-modal>
    <div class="modal-theme modal-confirmation modal-reject">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete Tempo <b>({{poImportObj?.vehicleNo}})</b>.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onDelTempo(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- ---------------------------- Delete Tempo Modal ----------------------------- -->

<!-- ---------------------------- GRN Carton Mapping Pending Modal ----------------------------- -->
<div class="modal modal-theme modal-confirmation modal-warning fade" id="GRNStartConfirmModal" tabindex="-1"
    aria-labelledby="GRNStartConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-info-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <p class="mb-3">Please ensure that all cartons in the linked container are mapped and that the expenses have
                            been added before starting the <strong>Goods Receipt Note (GRN)</strong>. <br/>Thank you!</p>
                        <p>Containers: <b>{{poImportObj?.containersName}}</b></p>
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button (click)="redirectToCartonMapping()" type="button" class="btn btn-sm btn-primary btn-icon-text">
                        Go to Carton Mapping
                    </button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ---------------------------- GRN Carton Mapping Pending Modal ----------------------------- -->


<!-- --------------------CHA Assign Modal Start ---------------------- -->
<ng-template #CHAassignModal let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Assign Carton to CHA</h5>
                <button type="button" class="btn-close" (click)="modal.close()" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="assignCHAFormGroup">

                <div class="product-details-container product-status">
                    <div class="product-details-left d-flex justify-content-start">
                        <a class="d-flex flex-row">
                            <div class="product-image">
                                <img *ngIf="itemObj?.item?.formattedName" loading="lazy"
                                    [src]="itemObj.item.formattedName ? (utilsService.imgPath + itemObj.item.formattedName) : ''"
                                    alt="valamji">
                                <ng-container *ngIf="!itemObj?.item?.formattedName">{{
                                    itemObj?.displayName?.charAt(0).toUpperCase()
                                    }}
                                </ng-container>
                            </div>
                            <div class="product-title ms-2">
                                <div class="tbl-user-text">
                                    <p class="text-black">{{itemObj?.item?.skuId}}</p>
                                    <span>
                                        <div>{{itemObj?.item?.displayName}}</div>
                                    </span>
                                </div>
                            </div>
                        </a>
                        <div class="product-title ms-1">
                            <div class="tbl-user-text">
                                <span>Marka</span>
                                <p class="text-black">{{itemObj?.marka ? itemObj?.marka : '-'}}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">CHA</label>
                            <ng-select formControlName="importerId" class="" placeholder="Select CHA" [multiple]="false"
                                [clearable]="false" [items]="CHAdropdown" bindLabel="label" bindValue="value">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="assignCHAFormGroup.controls['importerId'].hasError('required') &&  assignCHAFormGroup.controls['importerId'].touched">
                                {{utilsService.validationService.IMPORTER_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect required"
                            [ngClass]="{'form-error': assignCHAFormGroup.controls['cartonQty'].invalid && assignCHAFormGroup.controls['cartonQty'].touched}">
                            <label class="form-label">No. of Cartons</label>
                            <input [maxLength]="utilsService.validationService.MAX_10" mask="separator.0" thousandSeparator=""
                                formControlName="cartonQty" type="text" class="form-control" placeholder="Enter Carton Qty" />
                        </div>
                    </div>
                    <div class="col-12">
                        <span *ngIf="itemObj.PendingGrnQty != null" class="d-block w-100 fs-13">
                            <b>Pending GRN Qty: {{
                                (itemObj.PendingGrnQty || 0) + (itemObj.assignToCHAQty || 0)
                            }}</b>
                        </span>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button type="button" class="btn btn-outline-white" (click)="modal.close()">Cancel</button>
                    <button (click)="onAssignToCHA(modal)" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Assign</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
<!-- --------------------CHA Assign Modal END ---------------------- -->

<!-- ------------------ Move to Forms Modal Start   ------------------------------- -->
<div class="modal modal-theme fade" id="movetoCompletedModal" tabindex="-1" aria-labelledby="movetoCompletedModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered  modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Move to Completed ({{poImportObj?.containerName ? poImportObj?.containerName : ''}})</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" *ngIf="showMoveToCompletedModal">
                <div class="modal-table">
                    <div class="card card-theme  ">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table-theme table table-bordered table-sticky">
                                    <thead class="border-less">
                                        <tr>
                                            <th class="d-flex align-items-center gap-2">
                                                Item Details
                                            </th>
                                            <th>Carton Qty</th>
                                            <th>Qty/Carton</th>
                                            <th>Total Qty</th>
                                            <th>Weight</th>
                                            <th>Dimension</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of markAsCompletedList; index as i; trackBy: trackByMarkAsCompleted">
                                            <td class="tbl-user">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="tbl-user-wrapper">
                                                        <div class="tbl-user-image">
                                                            <img *ngIf="item.item?.formattedName" loading="lazy"
                                                                [src]="item.item.formattedName ? (utilsService.imgPath + item.item.formattedName) : ''" alt="valamji">
                                                        </div>
                                                        <div class="tbl-user-text-action">
                                                            <div class="tbl-user-text">
                                                                <p>{{item.item?.skuId}}</p>
                                                                <span>{{item.item?.displayName}}</span>
                                                                <span>{{item.marka}}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{item.grnQty}}</td>
                                            <td>{{item.pricePerCarton}}</td>
                                            <td>{{item.totalGrnQty}}</td>
                                            <td>{{item.cartonWeight}} {{item.cartonWeightDimName}}</td>
                                            <td>{{item.cartonLength}} x {{item.cartonWidth}} x {{item.cartonHeight}} {{item.cartonDimensionUnitName}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="onMoveToConfirmation()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Save & Continue</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ------------------Move to Forms Modal End   --------------------------- -->

<!-- ----------------------------   Move to Confirmation Modal ----------------------------- -->
<div class="modal modal-theme modal-confirmation modal-approve fade" id="completedConfirmationModal" tabindex="-1"
    aria-labelledby="completedConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-tick-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Do you want to mark this <b>{{poImportObj?.containerName}}</b> as <b> “{{utilsService.poImportStatus.COMPLETED | titlecase}}” </b></p>
                        <!-- <p><b>Note:</b> Ensure all expenses are recorded before marking this container as completed.</p> -->
                    </div>
                </div>

                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">No</button>
                    <button (click)="moveToCompletedConfirm()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Yes, Mark</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ---------------------------- Move to Confirmation Modal ----------------------------- -->
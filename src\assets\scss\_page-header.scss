.page-title-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    padding: 0;
    background-color: $white_color;
    border-bottom: 1px solid $stock_light;
    padding: 10px;

    h4 {
        font-size: 16px;
        font-weight: 600;
        line-height: 20.8px;
        color: #13172E;
        margin: 0;
    }

    .page-title-left {
        .page-description {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            margin: 0;

            li {
                font-size: 12px;
                font-weight: 400;
                line-height: 20.8px;
                color: $text_color;
                padding-right: 7px;
                margin-right: 7px;
                position: relative;

                &:nth-last-child(1) {
                    padding-right: 0;
                    margin-right: 0;

                    &::after {
                        content: none;
                    }
                }

                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1px;
                    height: 11px;
                    background-color: #636D83;
                }
            }
        }
        .item-header {
            word-break: break-word;
        }
    }

    .page-title-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 7px;
    }

    &.page-title-with-tab {
        border-bottom: 0;
        padding-bottom: 0;
    }

    .page-title-details {
        display: inline-flex;
        flex-direction: column;

        p {
            font-size: 12px;
            margin-bottom: 0;
            color: $text_black_color;
        }

        span {
            font-size: 10px;
        }
    }
}

.page-title-wrapper-sub {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    padding: 0;
    background-color: $white_color;
    border-bottom: 1px solid $stock_light;
    padding: 10px;

    .page-title-sub-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 7px;
    }

    h4 {
        font-size: 16px;
        font-weight: 600;
        line-height: 20.8px;
        color: #13172E;
        margin: 0;
    }

    .page-title-sub-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 7px;
    }

    .form-group {
        margin-bottom: 0 !important;
    }
}
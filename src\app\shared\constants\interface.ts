import { Level } from "src/app/models/Level";

export interface ItemDropdown {
    itemGroup: any[];
    sizeMaster: any[];
    colorMaster: any[];
    packingMaster: any[];
    hsnCodeMaster: any[];
    qcChecklist: any[];
    itemsBelong: any[];
    marketType: any[];
    unitMaster: any[];
    itemDimUnit: any[];
    itemWeightDimUnit: any[];
    boxUnit: any[];
    boxWeightUnit: any[];
    seasonMaster: any[];
    batteryType: any[];
    mountingType: any[];
    packOf: any[];
    itemSeasonTypes: any[];
    category: any[];
    groupCodes: any[];
    tag: any[];
    levels: Level[];
    suppliers: any[];
}

export interface BranchSideBar {
    branchEmail: string;
    branchName: string;
    id: number;
    isLoggedInBranch: number | boolean;
    isMainBranch: boolean;
}

export interface RegDropdown {
    AssignSalesPerson: any[];
    City: any[];
    Color: any[];
    Country: any[];
    Currency: any[];
    HsnCode: any[];
    PaymentTerms: any[];
    RegistrationType: any[];
    State: any[];
    SupplierType: any[];
    Unit: any[];
    phone: any[];
    phoneWhatsApp: any[];
    phoneTelegram: any[];
    phoneRef: any[];
    phoneLedger: any[];
    packingTypes: any[];
}

export interface PoImportDropdown {
    orderType: any[]
    item: any[]
    bankGroup: any[]
    checkList: any[]
    shippingType: any[]
    Country: any[]
    registration: any[]
    currency: any[]
    currencyAdvCus: any[]
    currencyCBM: any[]
    warehouse: any[]
    paymentTerms: any[]
    status: any[];
    customerDropdown: any[];
    supplierDropdown: any[];
    poImportResponse: any;
    unitMaster: any[];
    packingMaster: any[];
}

export interface SBTDropdown {
    orderUnit: any[]
    pickupPerson: any[]
    reason: any[]
    requestStatus: any[]
    requestTo: any[]
    Country: any[]
}
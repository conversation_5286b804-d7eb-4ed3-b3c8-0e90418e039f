import { deserializeAs, serializeAs } from 'cerialize';

export class Settings {

    @serializeAs('salesHoldBy')
    @deserializeAs('salesHoldBy')
    private _salesHoldBy: number;

    @serializeAs('payDueByCustomer')
    @deserializeAs('payDueByCustomer')
    private _payDueByCustomer: number;

    @serializeAs('payDueBySupplier')
    @deserializeAs('payDueBySupplier')
    private _payDueBySupplier: number;

    @serializeAs('repeatAfterEvery')
    @deserializeAs('repeatAfterEvery')
    private _repeatAfterEvery: number;

    @serializeAs('importShippingDay')
    @deserializeAs('importShippingDay')
    private _importShippingDay: number;

    @serializeAs('localShippingMonth')
    @deserializeAs('localShippingMonth')
    private _localShippingMonth: number;

    @serializeAs('creditLimitCustomer')
    @deserializeAs('creditLimitCustomer')
    private _creditLimitCustomer: number;

    @serializeAs('inquiryIdealTimer')
    @deserializeAs('inquiryIdealTimer')
    private _inquiryIdealTimer: any;

    @serializeAs('extendTime')
    @deserializeAs('extendTime')
    private _extendTime: any;

    @deserializeAs('inquiryIdealTimerTemp')
    private _inquiryIdealTimerTemp: any;

    @serializeAs('orderedQtyCalculation')
    @deserializeAs('orderedQtyCalculation')
    private _orderedQtyCalculation: boolean;

    @serializeAs('allowToRaiseBranchTransfReq')
    @deserializeAs('allowToRaiseBranchTransfReq')
    private _allowToRaiseBranchTransfReq: boolean;

    @serializeAs('branchReqToMainBranchApproval')
    @deserializeAs('branchReqToMainBranchApproval')
    private _branchReqToMainBranchApproval: boolean;

    @serializeAs('autoEnquiryOfferPrice')
    @deserializeAs('autoEnquiryOfferPrice')
    private _autoEnquiryOfferPrice: boolean;

    @serializeAs('cbmPrice')
    @deserializeAs('cbmPrice')
    private _cbmPrice: number;

    @serializeAs('enquiryNoOfDays')
    @deserializeAs('enquiryNoOfDays')
    private _enquiryNoOfDays: number;

    @serializeAs('needToQrCodeScan')
    @deserializeAs('needToQrCodeScan')
    private _needToQrCodeScan: number;

    @serializeAs('autoTicketRaiseCartonQty')
    @deserializeAs('autoTicketRaiseCartonQty')
    private _autoTicketRaiseCartonQty: number;

    @serializeAs('autoTicketRaiseBranch')
    @deserializeAs('autoTicketRaiseBranch')
    private _autoTicketRaiseBranch: number;

    @serializeAs('godownToRackTransfer')
    @deserializeAs('godownToRackTransfer')
    private _godownToRackTransfer: number;

    @serializeAs('variationPrg')
    @deserializeAs('variationPrg')
    private _variationPrg: number;

    @serializeAs('newArrivalAfterGrn')
    @deserializeAs('newArrivalAfterGrn')
    private _newArrivalAfterGrn: number;

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('sendMsgSalesPerson')
    @deserializeAs('sendMsgSalesPerson')
    private _sendMsgSalesPerson: boolean;

    @serializeAs('sendMsgCustomer')
    @deserializeAs('sendMsgCustomer')
    private _sendMsgCustomer: boolean;

    @serializeAs('sendPDFTelegram')
    @deserializeAs('sendPDFTelegram')
    private _sendPDFTelegram: boolean;
    
    @serializeAs('isShowItemPropertyDuringQC')
    @deserializeAs('isShowItemPropertyDuringQC')
    private _isShowItemPropertyDuringQC: boolean;

    @serializeAs('purchasePriceVariation')
    @deserializeAs('purchasePriceVariation')
    private _purchasePriceVariation: number;

    @serializeAs('poAlertRation')
    @deserializeAs('poAlertRation')
    private _poAlertRation: number;

    @serializeAs('fiscalYearType')
    @deserializeAs('fiscalYearType')
    private _fiscalYearType: string;

    @serializeAs('raiseSCAfterNoOfDays')
    @deserializeAs('raiseSCAfterNoOfDays')
    private _raiseSCAfterNoOfDays: number;

    @serializeAs('showExistingLocationForCartonMapping')
    @deserializeAs('showExistingLocationForCartonMapping')
    private _showExistingLocationForCartonMapping: boolean;

    @serializeAs('chinaCommentForPOPdf')
    @deserializeAs('chinaCommentForPOPdf')
    private _chinaCommentForPOPdf: string;

    constructor() {
        this.showExistingLocationForCartonMapping = false;
    }


    /**
     * Getter chinaCommentForPOPdf
     * @return {string}
     */
	public get chinaCommentForPOPdf(): string {
		return this._chinaCommentForPOPdf;
	}

    /**
     * Setter chinaCommentForPOPdf
     * @param {string} value
     */
	public set chinaCommentForPOPdf(value: string) {
		this._chinaCommentForPOPdf = value;
	}


    /**
     * Getter showExistingLocationForCartonMapping
     * @return {boolean}
     */
	public get showExistingLocationForCartonMapping(): boolean {
		return this._showExistingLocationForCartonMapping;
	}

    /**
     * Setter showExistingLocationForCartonMapping
     * @param {boolean} value
     */
	public set showExistingLocationForCartonMapping(value: boolean) {
		this._showExistingLocationForCartonMapping = value;
	}

    /**
     * Getter raiseSCAfterNoOfDays
     * @return {number}
     */
	public get raiseSCAfterNoOfDays(): number {
		return this._raiseSCAfterNoOfDays;
	}

    /**
     * Setter raiseSCAfterNoOfDays
     * @param {number} value
     */
	public set raiseSCAfterNoOfDays(value: number) {
		this._raiseSCAfterNoOfDays = value;
	}


    /**
     * Getter extendTime
     * @return {any}
     */
	public get extendTime(): any {
		return this._extendTime;
	}

    /**
     * Setter extendTime
     * @param {any} value
     */
	public set extendTime(value: any) {
		this._extendTime = value;
	}



    /**
     * Getter fiscalYearType
     * @return {string}
     */
	public get fiscalYearType(): string {
		return this._fiscalYearType;
	}

    /**
     * Setter fiscalYearType
     * @param {string} value
     */
	public set fiscalYearType(value: string) {
		this._fiscalYearType = value;
	}


    /**
     * Getter sendMsgSalesPerson
     * @return {boolean}
     */
	public get sendMsgSalesPerson(): boolean {
		return this._sendMsgSalesPerson;
	}

    /**
     * Getter sendMsgCustomer
     * @return {boolean}
     */
	public get sendMsgCustomer(): boolean {
		return this._sendMsgCustomer;
	}

    /**
     * Getter sendPDFTelegram
     * @return {boolean}
     */
	public get sendPDFTelegram(): boolean {
		return this._sendPDFTelegram;
	}

    /**
     * Getter purchasePriceVariation
     * @return {number}
     */
	public get purchasePriceVariation(): number {
		return this._purchasePriceVariation;
	}


    /**
     * Getter isShowItemPropertyDuringQC
     * @return {boolean}
     */
	public get isShowItemPropertyDuringQC(): boolean {
		return this._isShowItemPropertyDuringQC;
	}

    /**
     * Setter isShowItemPropertyDuringQC
     * @param {boolean} value
     */
	public set isShowItemPropertyDuringQC(value: boolean) {
		this._isShowItemPropertyDuringQC = value;
	}

    /**
     * Getter poAlertRation
     * @return {number}
     */
	public get poAlertRation(): number {
		return this._poAlertRation;
	}

    /**
     * Setter sendMsgSalesPerson
     * @param {boolean} value
     */
	public set sendMsgSalesPerson(value: boolean) {
		this._sendMsgSalesPerson = value;
	}

    /**
     * Setter sendMsgCustomer
     * @param {boolean} value
     */
	public set sendMsgCustomer(value: boolean) {
		this._sendMsgCustomer = value;
	}

    /**
     * Setter sendPDFTelegram
     * @param {boolean} value
     */
	public set sendPDFTelegram(value: boolean) {
		this._sendPDFTelegram = value;
	}

    /**
     * Setter purchasePriceVariation
     * @param {number} value
     */
	public set purchasePriceVariation(value: number) {
		this._purchasePriceVariation = value;
	}

    /**
     * Setter poAlertRation
     * @param {number} value
     */
	public set poAlertRation(value: number) {
		this._poAlertRation = value;
	}


    /**
     * Getter inquiryIdealTimerTemp
     * @return {any}
     */
	public get inquiryIdealTimerTemp(): any {
		return this._inquiryIdealTimerTemp;
	}

    /**
     * Setter inquiryIdealTimerTemp
     * @param {any} value
     */
	public set inquiryIdealTimerTemp(value: any) {
		this._inquiryIdealTimerTemp = value;
	}


    /**
     * Getter salesHoldBy
     * @return {number}
     */
	public get salesHoldBy(): number {
		return this._salesHoldBy;
	}

    /**
     * Getter payDueByCustomer
     * @return {number}
     */
	public get payDueByCustomer(): number {
		return this._payDueByCustomer;
	}

    /**
     * Getter payDueBySupplier
     * @return {number}
     */
	public get payDueBySupplier(): number {
		return this._payDueBySupplier;
	}

    /**
     * Getter repeatAfterEvery
     * @return {number}
     */
	public get repeatAfterEvery(): number {
		return this._repeatAfterEvery;
	}

    /**
     * Getter importShippingDay
     * @return {number}
     */
	public get importShippingDay(): number {
		return this._importShippingDay;
	}

    /**
     * Getter localShippingMonth
     * @return {number}
     */
	public get localShippingMonth(): number {
		return this._localShippingMonth;
	}

    /**
     * Getter creditLimitCustomer
     * @return {number}
     */
	public get creditLimitCustomer(): number {
		return this._creditLimitCustomer;
	}

    /**
     * Getter inquiryIdealTimer
     * @return {any}
     */
	public get inquiryIdealTimer(): any {
		return this._inquiryIdealTimer;
	}

    /**
     * Getter orderedQtyCalculation
     * @return {boolean}
     */
	public get orderedQtyCalculation(): boolean {
		return this._orderedQtyCalculation;
	}

    /**
     * Getter allowToRaiseBranchTransfReq
     * @return {boolean}
     */
	public get allowToRaiseBranchTransfReq(): boolean {
		return this._allowToRaiseBranchTransfReq;
	}

    /**
     * Getter branchReqToMainBranchApproval
     * @return {boolean}
     */
	public get branchReqToMainBranchApproval(): boolean {
		return this._branchReqToMainBranchApproval;
	}

    /**
     * Getter autoEnquiryOfferPrice
     * @return {boolean}
     */
	public get autoEnquiryOfferPrice(): boolean {
		return this._autoEnquiryOfferPrice;
	}

    /**
     * Getter cbmPrice
     * @return {number}
     */
	public get cbmPrice(): number {
		return this._cbmPrice;
	}

    /**
     * Getter enquiryNoOfDays
     * @return {number}
     */
	public get enquiryNoOfDays(): number {
		return this._enquiryNoOfDays;
	}

    /**
     * Getter needToQrCodeScan
     * @return {number}
     */
	public get needToQrCodeScan(): number {
		return this._needToQrCodeScan;
	}

    /**
     * Getter autoTicketRaiseCartonQty
     * @return {number}
     */
	public get autoTicketRaiseCartonQty(): number {
		return this._autoTicketRaiseCartonQty;
	}

    /**
     * Getter autoTicketRaiseBranch
     * @return {number}
     */
	public get autoTicketRaiseBranch(): number {
		return this._autoTicketRaiseBranch;
	}

    /**
     * Getter godownToRackTransfer
     * @return {number}
     */
	public get godownToRackTransfer(): number {
		return this._godownToRackTransfer;
	}

    /**
     * Getter variationPrg
     * @return {number}
     */
	public get variationPrg(): number {
		return this._variationPrg;
	}

    /**
     * Getter newArrivalAfterGrn
     * @return {number}
     */
	public get newArrivalAfterGrn(): number {
		return this._newArrivalAfterGrn;
	}

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter salesHoldBy
     * @param {number} value
     */
	public set salesHoldBy(value: number) {
		this._salesHoldBy = value;
	}

    /**
     * Setter payDueByCustomer
     * @param {number} value
     */
	public set payDueByCustomer(value: number) {
		this._payDueByCustomer = value;
	}

    /**
     * Setter payDueBySupplier
     * @param {number} value
     */
	public set payDueBySupplier(value: number) {
		this._payDueBySupplier = value;
	}

    /**
     * Setter repeatAfterEvery
     * @param {number} value
     */
	public set repeatAfterEvery(value: number) {
		this._repeatAfterEvery = value;
	}

    /**
     * Setter importShippingDay
     * @param {number} value
     */
	public set importShippingDay(value: number) {
		this._importShippingDay = value;
	}

    /**
     * Setter localShippingMonth
     * @param {number} value
     */
	public set localShippingMonth(value: number) {
		this._localShippingMonth = value;
	}

    /**
     * Setter creditLimitCustomer
     * @param {number} value
     */
	public set creditLimitCustomer(value: number) {
		this._creditLimitCustomer = value;
	}

    /**
     * Setter inquiryIdealTimer
     * @param {any} value
     */
	public set inquiryIdealTimer(value: any) {
		this._inquiryIdealTimer = value;
	}

    /**
     * Setter orderedQtyCalculation
     * @param {boolean} value
     */
	public set orderedQtyCalculation(value: boolean) {
		this._orderedQtyCalculation = value;
	}

    /**
     * Setter allowToRaiseBranchTransfReq
     * @param {boolean} value
     */
	public set allowToRaiseBranchTransfReq(value: boolean) {
		this._allowToRaiseBranchTransfReq = value;
	}

    /**
     * Setter branchReqToMainBranchApproval
     * @param {boolean} value
     */
	public set branchReqToMainBranchApproval(value: boolean) {
		this._branchReqToMainBranchApproval = value;
	}

    /**
     * Setter autoEnquiryOfferPrice
     * @param {boolean} value
     */
	public set autoEnquiryOfferPrice(value: boolean) {
		this._autoEnquiryOfferPrice = value;
	}

    /**
     * Setter cbmPrice
     * @param {number} value
     */
	public set cbmPrice(value: number) {
		this._cbmPrice = value;
	}

    /**
     * Setter enquiryNoOfDays
     * @param {number} value
     */
	public set enquiryNoOfDays(value: number) {
		this._enquiryNoOfDays = value;
	}

    /**
     * Setter needToQrCodeScan
     * @param {number} value
     */
	public set needToQrCodeScan(value: number) {
		this._needToQrCodeScan = value;
	}

    /**
     * Setter autoTicketRaiseCartonQty
     * @param {number} value
     */
	public set autoTicketRaiseCartonQty(value: number) {
		this._autoTicketRaiseCartonQty = value;
	}

    /**
     * Setter autoTicketRaiseBranch
     * @param {number} value
     */
	public set autoTicketRaiseBranch(value: number) {
		this._autoTicketRaiseBranch = value;
	}

    /**
     * Setter godownToRackTransfer
     * @param {number} value
     */
	public set godownToRackTransfer(value: number) {
		this._godownToRackTransfer = value;
	}

    /**
     * Setter variationPrg
     * @param {number} value
     */
	public set variationPrg(value: number) {
		this._variationPrg = value;
	}

    /**
     * Setter newArrivalAfterGrn
     * @param {number} value
     */
	public set newArrivalAfterGrn(value: number) {
		this._newArrivalAfterGrn = value;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


   
}
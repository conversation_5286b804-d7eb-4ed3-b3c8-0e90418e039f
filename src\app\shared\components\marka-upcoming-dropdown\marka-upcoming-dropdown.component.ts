import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MarkaBranchData, MarkaStocks, UpcomingQty, BranchMaster } from '@modal/SalesInquiry';
import { UtilsService } from '@service/utils.service';
import { sumArr } from '@libs';

@Component({
  selector: 'app-marka-upcoming-dropdown',
  templateUrl: './marka-upcoming-dropdown.component.html',
  styleUrls: ['./marka-upcoming-dropdown.component.css']
})
export class MarkaUpcomingDropdownComponent implements OnInit {

  dragPosition = {x: 0, y: 0};

  @Input({alias: 'allMarkaBranchData', required: true}) allMarkaBranchData: MarkaBranchData;
  @Input({alias: 'branchMaster', required: true}) branchMaster: BranchMaster[];
  @Input({alias: 'isUpcomingTab', required: true}) isUpcomingTab: boolean;
  @Input({alias: 'filteredMarkaList', required: true}) filteredMarkaList: MarkaStocks[] = []
  @Input({alias: 'filteredUpcomingQtyList', required: true}) filteredUpcomingQtyList: UpcomingQty[] = []
  @Input({alias: 'searchText', required: true}) searchText: FormControl;
  @Input({alias: 'warehouseIdFilter', required: true}) warehouseIdFilter: number[] = []
  @Input({alias: 'i', required: true}) i: number;
  @Input({alias: 'isSO', required: true}) isSO: boolean;
  @Input({alias: 'isLoose', required: true}) isLoose: boolean;
  @Input({alias: 'isOnlyUpcoming', required: true}) isOnlyUpcoming: boolean;
  @Input({alias: 'markaDataCache', required: true}) markaDataCache: Map<string, any>;
  @Input({alias: 'allBranchSumTotalInputMap', required: false}) allBranchSumTotalInputMap: Map<string, number> = new Map<string, number>();
  
  @Output() openMarkaPopupEvent = new EventEmitter()
  @Output() filterUpcomingQtyEvent = new EventEmitter()
  @Output() filtersMarkaModalEvent = new EventEmitter()
  @Output() changeBranchTabEvent = new EventEmitter()
  @Output() onSaveMarkaQtyEvent = new EventEmitter()
  @Output() onSaveUpcomingQtyEvent = new EventEmitter()

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }
  
  changePosition() {
    this.dragPosition = {x: this.dragPosition.x, y: this.dragPosition.y};
  }
  

  getQtyTotal(markaIndex: number): number {

    const branchId = this.branchMaster[this.allMarkaBranchData.activeTab !== null ? this.allMarkaBranchData.activeTab : 0]?.id;
    if (!branchId) {
      return 0;
    };
    
    const markaName = this.filteredMarkaList[markaIndex]?.marka;
  
    // Create a unique key for branch + marka
    const key = `${branchId}|${markaName}|${this.i}`;
  
    // Sum all qty for this marka
    const sum = this.filteredMarkaList.filter(item => item.marka === markaName).reduce((total, item) => total + (Number(item.qty) || 0), 0);
  
    // Save in the map
    this.allBranchSumTotalInputMap.set(key, sum);
    return sum;
  }

  getTotalUpcomingQty = (): number => {
    return this.filteredUpcomingQtyList?.reduce((sum: number, m: any) => sum + (Number(m.upcomingQty) || 0), 0) || 0;
  }

  onSaveMarkaQty = () => {
    this.onSaveMarkaQtyEvent.emit()
  }

  onSaveUpcomingQty = () => {
    this.onSaveUpcomingQtyEvent.emit()
  }

  filterUpcomingQty = (searchType: 'marka' | 'warehouseId', searchValue: string | number[]) => {
    switch (searchType) {
      case 'marka':
        this.filterUpcomingQtyEvent.emit({ searchType, searchValue: this.searchText.value })
        break;
      case 'warehouseId':
        this.filterUpcomingQtyEvent.emit({ searchType, searchValue })
        break;
    }
  }

  filtersMarkaModal = (searchType: 'marka' | 'warehouseId', searchValue: string | number[]) => {
    switch (searchType) {
      case 'marka':
        this.filtersMarkaModalEvent.emit({ searchType, searchValue: this.searchText.value })
        break;
      case 'warehouseId':
        this.filtersMarkaModalEvent.emit({ searchType, searchValue })
        break;
    }
  }

  changeBranchTab = (index: number, branch: any, isUpcomingTab: boolean) => {
    this.changePosition();
    this.changeBranchTabEvent.emit({index, branch, isUpcomingTab})
  }

  changeToUpcomingTab = (index: number) => {
    this.openMarkaPopup(index, true);
  }

  openMarkaPopup = (index: number, isUpcomingTab: boolean) => {
    this.changePosition();
    this.openMarkaPopupEvent.emit({index, isUpcomingTab})
  }

  getTotalQty = (marka: MarkaStocks): number => {
    return marka.warehouseStocks?.reduce((sum: number, w: any) => sum + (w.totalQty || 0), 0) || 0;
  }
  
  getMaxCartons = (marka: MarkaStocks): number => {
    return marka.warehouseStocks?.reduce((sum: number, w: any) => sum + (w.totalCartons || 0), 0) || 0;
  }

  getMaxLoose = (marka: MarkaStocks): number => {
    return marka.warehouseStocks?.reduce((sum: number, w: any) => sum + (w.totalLooseQty || 0), 0) || 0;
  }

  getTotalQtyAllMarkas = (): number => {
    return this.filteredMarkaList?.reduce((sum: number, m: any) => sum + (Number(m.qty) || 0), 0) || 0;
  }

  getTotalUpcomingQtyAllMarkas = (): number => {
    return this.filteredUpcomingQtyList?.reduce((sum: number, m: UpcomingQty) => sum + (Number(m.upcomingQty) || 0), 0) || 0;
  }

  isQtyInvalid = (marka: MarkaStocks): boolean => {
    let flag = false;

    // const maxForThisMarkaLoose = this.getMaxLoose(marka);
    const totalAvailableCartons = this.allMarkaBranchData?.markaDropDown?.availableCartonsQty || 0;
    const totalAvailableQuantity = this.allMarkaBranchData?.markaDropDown?.totalAvailableQty || 0;
    const maxForThisMarka = this.getMaxCartons(marka);
    const totalQty = this.getTotalQty(marka)
    const totalEnteredQty = this.getTotalQtyAllMarkas();

    if (this.isLoose && (totalEnteredQty > totalAvailableQuantity)) {
      flag = true;
    }

    if (!this.isLoose && (totalEnteredQty > maxForThisMarka)) {
      flag = true;
    }

    if (!this.isLoose && (totalEnteredQty > totalAvailableCartons)) {
      flag = true;
    }

    if (this.isLoose && (totalEnteredQty > totalQty)) {
      flag = true;
    }
    return flag;
  }

  disabledUpcoming = () => {
    let flag = false;

    const totalEnteredQty = this.getTotalUpcomingQtyAllMarkas();
    if (this.filteredUpcomingQtyList.length === 0) {
      flag = true;
    }
    if (totalEnteredQty === 0) {
      flag = true;
    }
    for (let i = 0; i < this.filteredUpcomingQtyList.length; i++) {
      if (this.isUpcomingInvalid(i)) {
        flag = true;
        break;
      }
    }
    return flag;
  }

  isUpcomingInvalid = (index: number) => {
    let flag = false;

    const totalEnteredQty = this.getTotalUpcomingQtyAllMarkas();
    const totalAvailableQuantity = this.allMarkaBranchData.totalUpcomingQty || 0;
    const item = this.filteredUpcomingQtyList[index];

    if (totalEnteredQty > totalAvailableQuantity) {
      flag = true;
    }
    if (Number(item.upcomingQty) > Number(item.totalQty)) {
      flag = true;
    }
    return flag;
  }

  disabledIfInvalid = (): boolean => {
    const tempFilteredData = Array.from(this.markaDataCache.values()).flatMap(entry => {
      const branchId = entry?.markaDropDown?.branchId;
      return (entry?.markaDropDown?.markaStocks || []).filter((a: MarkaStocks) => a?.qty > 0).map((markaStock: MarkaStocks) => ({
        ...markaStock,
        branchId
      }));
    })

    const totalEnteredQty = sumArr(tempFilteredData || [], x => Number(x.qty) || 0);

    // Rule 1: atleast 1 qty field should be present before save
    if (totalEnteredQty === 0) {
      return true;
    }

    return this.filteredMarkaList.some(marka => this.isQtyInvalid(marka));
  }

  getGrnGroupLink(grnGroupLinkId: number): string {
    const origin = window.location.origin;
    const baseHref = document.querySelector('base')?.getAttribute('href') || '/';
  
    // Ensure base does not end with a slash
    const base = baseHref.endsWith('/') ? baseHref.slice(0, -1) : baseHref;
  
    return `${origin}${base}/#/marka-image/${grnGroupLinkId}`;
  }  

  onCopy(grnGroupLinkId: number) {
    if(grnGroupLinkId){
      this.utilsService.toasterService.success('Copied To Clipboard', '', { positionClass: 'toast-top-right', closeButton: true, timeOut: 2000 });
    }
  }
}

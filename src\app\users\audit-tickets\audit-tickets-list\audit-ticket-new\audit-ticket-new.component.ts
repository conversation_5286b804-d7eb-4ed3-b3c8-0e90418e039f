import { Component, OnInit, ViewChild, inject } from '@angular/core';
import { UtilsService } from '@service/utils.service';
import { AuditTicketService } from '../audit-ticket.service';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';

@Component({
  selector: 'app-audit-ticket-new',
  templateUrl: './audit-ticket-new.component.html',
  styleUrls: ['./audit-ticket-new.component.scss']
})
export class AuditTicketNewComponent implements OnInit {

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective;

  auditTicketService = inject(AuditTicketService);
  utilsService = inject(UtilsService);
  constructor() { }

  ngOnInit(): void {
  }

  open(): void {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }


}

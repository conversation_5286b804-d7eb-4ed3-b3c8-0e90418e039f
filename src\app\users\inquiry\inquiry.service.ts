import { computed, inject, Injectable, signal, TemplateRef } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { EnumForInquiryTabs } from "@enums/EnumForInquiryTabs";
import { createObjectSignal, createArraySignal, hasDuplicates } from "@libs";
import { ImagesInquiryPagination } from "@modal/request/ImagesInquiryPagination";
import { Attachment, AttachmentTick, ImageInquiryTab, ImageMappedInquiry, ImageMergeSave } from "@modal/SalesInquiry";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { NgbModalService } from "@service/ngb-modal.service";
import { UtilsService } from "@service/utils.service";
import { Serialize } from "cerialize";
import dayjs from "dayjs";
import { Subject, map, tap, takeUntil } from "rxjs";

const EnumForType = { NEW_ITEM: 'NEW_ITEM', EXISTING_ITEM: 'EXISTING_ITEM' } as const

interface DropdownTag {
    tag: { label: string, value: string }[],
    items: { displayName: string, id: number, formattedName: string }[]
}

@Injectable({
    providedIn: 'root'
})

export class InquiryService {

    private modalService = inject(NgbModalService);
    utilsService = inject(UtilsService);

    enumForTab = EnumForInquiryTabs;
    selectedTab = signal<string>(null);
    formGroupMerge: FormGroup;
    enumForType = EnumForType;

    paginationRequest = createObjectSignal({} as ImagesInquiryPagination);
    mappedImagesTabData = createArraySignal<ImageMappedInquiry>([]);
    mappedImageObj = createObjectSignal({} as ImageMappedInquiry);
    imageInquiryTabData = createArraySignal<ImageInquiryTab>([]);

    emptyData = computed(() => (this.mappedImagesTabData.get() || []).length === 0);
    flagForExpandAll = computed(() => (this.mappedImagesTabData.get() || []).every(item => item.isExpand))
    checkIfAnySelected = computed(() => this.imageInquiryTabData.get().every(item => !item.isSelected))
    emptyInquiryImageData = computed(() => (this.imageInquiryTabData.get() || []).length === 0);

    destroy$ = new Subject<void>();

    hasDateInit = false;
    
    itemDeleteDocIds = createArraySignal<number>([]);
    itemDocs: AttachmentTick[] = [];

    isExpandedIDs: { parentIds: number[]} = { parentIds: [] };

    dropdownData: DropdownTag = {
        items: [],
        tag: []
    };

    selectedAttachmentIndex: number;

    constructor(private fb: FormBuilder) {

    }

    initPagination = () => {
        this.paginationRequest.update(a => ({ ...a, isShowCustomer: true, pageNo: 1, pageSize: "100" }));
    }

    onChangeTab(tab: string) {
        this.selectedTab.set(tab);
        this.paginationRequest.update(a => ({ ...a, searchText: null, customerId: null, inquiryType: null, dateRange: null }));

        switch (this.selectedTab()) {
            case this.enumForTab.IMAGE_INQUIRES:
                this.initPagination();
                this.getAllImageInquiryTabData();
                this.getMappedImagesTabData();
                break;
            case this.enumForTab.MAPPED_IMAGES:
                this.initPagination();
                this.getMappedImagesTabData();
                break;
            default:
                break;
        }
    }

    getMappedImagesTabData() {

        let param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'pagination', 'totalData'])
        
        switch (this.selectedTab()) {
            case this.enumForTab.IMAGE_INQUIRES:
                param.isShowCustomer = false;
                break;
            case this.enumForTab.MAPPED_IMAGES:
                param.isShowCustomer = true;
                break;
            default:
                break;
        }

        this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_PAGE_MAPPED_IMAGE, param).pipe(
            map((res) => res.data),
            tap((res) => {
                this.paginationRequest.update(a => ({ ...a, pagination: res.pagination, totalData: res?.['totalElements'] }));
            }),
            map((res) => res?.['content']),
            map((data: ImageMappedInquiry[]) => {
                if (!this.utilsService.isNullUndefinedOrBlank(data)) {
                    switch (this.selectedTab()) {
                        case this.enumForTab.IMAGE_INQUIRES:
                            this.imageDefault(data);
                            break;
                        case this.enumForTab.MAPPED_IMAGES:
                            this.imageDefault(data);
                            if (this.isExpandedIDs?.parentIds?.length > 0) {
                                this.mappedImagesTabData.update(list =>
                                    list.map(parent => {
                                        const isExpand = this.isExpandedIDs.parentIds.includes(parent.id);
                                        return {
                                            ...parent,
                                            isExpand: isExpand,
                                        }
                                    })
                                );
                                this.expandOnFilter()
                            }
                            break;
                        default:
                            break;
                    }
                }
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // EXPAND
    onExpand = (index: number): void => {
        this.mappedImagesTabData.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
    }

    imageDefault = (data: ImageMappedInquiry[]) => {
        for (const item of data) {
            item.markAsDefaultImage = item.itemDocs.find(a => a.isMarkDefault)?.formattedName || null;
        }
        this.mappedImagesTabData.set(data);
    }

    expandCollapse = () => {

        if (!this.mappedImagesTabData.get()?.length) {
            return;
        }

        const parentIds: number[] = [];

        for (const item of this.mappedImagesTabData.get()) {
            if (item.isExpand) {
                parentIds.push(item.id);
            }
        }
        this.isExpandedIDs = { parentIds };
    }

    expandOnFilter = () => {

        const searchText = this.utilsService.isEmptyObjectOrNullUndefined(this.paginationRequest.get().searchText);
        const fromDate = this.utilsService.isEmptyObjectOrNullUndefined(this.paginationRequest.get().fromDate);
        const toDate = this.utilsService.isEmptyObjectOrNullUndefined(this.paginationRequest.get().toDate);

        if ((searchText || fromDate || toDate) && this.mappedImagesTabData.get().length > 0) {
            this.mappedImagesTabData.update(items => { return items.map(item => ({ ...item, isExpand: true })) })
        }
    }

    addPageSizeData(event: any) {
        this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
        this.getMappedImagesTabData();
    }

    pageNumber(event: any) {
        this.paginationRequest.update(a => ({ ...a, pageNo: event }));
        this.getMappedImagesTabData();
    }

    onChangeFilter = (event: any, type: 'date' | 'search', isDateClear?: boolean) => {
        switch (type) {
            case 'date':
                if (!this.hasDateInit) {
                    this.hasDateInit = true;
                    return;
                }
                if (event?.start === null && event?.end === null && !isDateClear) {
                    return;
                }
                const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
                const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
                this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
                break;
            case 'search':
                this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
                break;
        }
        this.destroy$.complete()
        this.getMappedImagesTabData()
    }

    onChangeFilterImageInq = (event: any, type: 'date' | 'search', isDateClear?: boolean) => {
        switch (type) {
            case 'date':
                if (!this.hasDateInit) {
                    this.hasDateInit = true;
                    return;
                }
                if (event?.start === null && event?.end === null && !isDateClear) {
                    return;
                }
                const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
                const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
                this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
                break;
            case 'search':
                this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
                break;
        }
        this.destroy$.complete()
        this.getAllImageInquiryTabData()
    }

    // Filters Clear
    onClear = () => {
        this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null, searchText: null }));
        if(this.selectedTab() === this.enumForTab.IMAGE_INQUIRES) {
            this.onChangeFilterImageInq(null, 'date', true);
        } else {
            this.onChangeFilter(null, 'date', true);
        }
    }

    //Refresh
    onRefresh = () => {
        switch (this.selectedTab()) {
            case EnumForInquiryTabs.IMAGE_INQUIRES:
                this.getAllImageInquiryTabData();
                this.getMappedImagesTabData();
                break;
            case EnumForInquiryTabs.MAPPED_IMAGES:
                this.getMappedImagesTabData();
                break;
        }
    }

    // Uploading Images
    openAttachmentModal = (index: number, content: TemplateRef<any>) => {
        this.itemDocs = [];
        this.itemDeleteDocIds.set([]);
        this.selectedAttachmentIndex = index;
        this.modalService.open(content, { windowClass: 'modal-lg' });
        this.itemDocs = Serialize((this.mappedImagesTabData.get() || [])[this.selectedAttachmentIndex]?.itemDocs || []);
    }

    onSelectAttachments = (event: any): void => {

        let selectedFiles: FileList | null = null;
        if (event.type === 'drop') {
            event.preventDefault();
            selectedFiles = event.dataTransfer?.files;
        }

        if (event.type === 'dragover') {
            event.preventDefault();
        }

        if (event.type === 'change') {
            selectedFiles = event.target.files;
        }

        if (event.type === 'paste') {
            const items = (event.clipboardData.items);
            const dataTransfer = new DataTransfer();

            for (const item of items) {
                if (item.type.indexOf('image') === 0) {
                    const blob = item.getAsFile();
                    const fileName = blob?.name;
                    if (blob !== null && fileName) {
                        const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
                        const fileFromBlob = new File([blob], fileName, { type: item.type });
                        dataTransfer.items.add(fileFromBlob);
                    }
                }
            }
            selectedFiles = dataTransfer.files;
        }

        const max_file_size = 5242880;

        if (selectedFiles) {
            Array.from(selectedFiles).forEach((file: File, i: number) => {
                const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

                const allowedExts = ['jpeg', 'png', 'jpg', 'jfif'];

                if (allowedExts.includes(ext)) {
                    if (file.size > max_file_size) {
                        this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE);
                    } else {
                        const fileUrl = URL.createObjectURL(file);
                        let fileData = null;
                        fileData = {
                            id: null,
                            file: file,
                            originalName: file.name,
                            formattedName: fileUrl,
                            isMarkDefault: this.itemDocs.every(doc => !doc.isMarkDefault) ? true : false,
                        };
                        if ((this.itemDocs || []).length >= 10) {
                            this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                                positionClass: 'toast-top-right',
                                closeButton: true,
                                timeOut: 10000
                            });
                            selectedFiles = null;
                            return;
                        }
                        this.itemDocs.push(fileData);
                        selectedFiles = null;
                    }
                } else {
                    this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION);
                }
            });
        }
    }

    removeAttachment(i: number, file: Attachment) {
        this.itemDocs.splice(i, 1);
        if (file.id) {
            this.itemDeleteDocIds.push(file.id)
        }
    }

    openLink = (link: string, newUpload: any) => {
        const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

        if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
            window.open(newUpload, "_blank");
            return;
        }

        if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
            window.open(filePreview, "_blank");
            return;
        }
    }

    onPrimaryChange = (currentIndex: number) => {
        const obj = this.itemDocs.map((request, index) => {
            request.isMarkDefault = (index === currentIndex);
        });
    }

    onUpload = (modal: NgbModal) => {

        const formData = new FormData();

        let fileRes = {}

        const noMarkAsPrimary = this.itemDocs.every(v => !v.isMarkDefault)
        if (noMarkAsPrimary) {
            this.utilsService.toasterService.error('Minimum one image with Marked As default is required', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
            });
            return;
        }

        const obj = this.itemDocs.filter(a => !a.id).map((v, i) => {
            if (v.file) {
                if (v.isMarkDefault) {
                    fileRes[i] = v.file.name;
                    formData.set('itemsReqs', JSON.stringify(fileRes));
                }
                formData.append('itemDocs', v.file);
            }
        })

        this.itemDocs.map((v) => {
            if (!v.file) {
                if (v.isMarkDefault) {
                    formData.set('defaultDocId', JSON.stringify(v.id));
                }
            }
        })

        formData.set('itemId', JSON.stringify(this.mappedImagesTabData.get()[this.selectedAttachmentIndex].id))

        if(this.itemDeleteDocIds.get().length > 0) {
            formData.set('itemDeleteDocIds', JSON.stringify(this.itemDeleteDocIds.get()))
        }

        this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_UPLOAD_ITEM_IMAGE, formData, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getMappedImagesTabData();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Delete Image Mapping
    openDeleteItemMappingModal = (obj: ImageMappedInquiry, content: TemplateRef<any>) => {
        this.mappedImageObj.set(obj)
        this.modalService.open(content);
    }

    onDeleteMapping = (modal: NgbModal) => {

        const API = `${this.utilsService.serverVariableService.INQUIRY_DELETE_MAPPED_IMAGE_BY_ITEM_ID}?itemId=${this.mappedImageObj.get().id}`;

        this.utilsService.delete(API, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getMappedImagesTabData();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Image Inquiry Tab API response change
    transformData(inputData: any[]): ImageInquiryTab[] {
        const flatData = inputData.flat();
        const groupedByCustomer = {};

        flatData.forEach(item => {
            const inquiryItemId = item.inquiryItemId;

            if (!groupedByCustomer[inquiryItemId]) {
                groupedByCustomer[inquiryItemId] = {
                    otherData: {
                        customerName: item.customerName,
                        phone: item.phone,
                        inquiryItemId: item.inquiryItemId,
                        inquiryId: item.inquiryId,
                        customerId: item.customerId,
                    },
                    images: [],
                    id: item.id
                };
            }

            groupedByCustomer[inquiryItemId].images.push({
                id: item.id,
                originalName: item.originalName,
                formattedName: item.formattedName
            });
        });

        return Object.values(groupedByCustomer);
    }
    
    getAllImageInquiryTabData = () => {
        
        const param = {
            searchText: this.paginationRequest.get().searchText,
            fromDate: this.paginationRequest.get().fromDate,
            toDate: this.paginationRequest.get().toDate,
        }

        this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_GET_IMG_INQ_TAB, param).pipe(
            map((res: any) => {
                this.imageInquiryTabData.set(this.transformData(res.data || []));
                this.imageInquiryTabData.update((val: ImageInquiryTab[]) => val.map(item => {
                    item.isSelected = false;
                    return item;
                }));
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    onSelectImage = (index: number, event: any) => {
        this.imageInquiryTabData.updateAt(index, (item: ImageInquiryTab) => {
            return {
                ...item,
                isSelected: event,
                images: item.images.map((image: any) => ({
                    ...image,
                    isSelectedChild: event
                }))
            };
        });
    };

    // MERGE IMAGES MODAL

    openMergeImagesModal = (content: TemplateRef<any>) => {

        const inquiryIds = this.imageInquiryTabData.get().filter(item => item.isSelected).map(a => a.otherData.inquiryId);
        if(hasDuplicates(inquiryIds)) {
            this.utilsService.toasterService.error('Multiple Items cannot be merged.', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
            });
            return;
        }

        this.formGroupMerge.reset()
        this.formGroupMerge.patchValue({ type: this.enumForType.NEW_ITEM })

        this.onChangeType(this.enumForType.NEW_ITEM)

        this.modalService.open(content, {windowClass: 'modal-lg'});
    }

    getImageTagReqiuredData = (tag: string) => {

        let API = null;
        if(tag) {
            API = `${this.utilsService.serverVariableService.INQUIRY_MAPPED_IMAGE_REQ_DATA}?tag=${tag}`;
        } else {
            API = this.utilsService.serverVariableService.INQUIRY_MAPPED_IMAGE_REQ_DATA;
        }

        this.dropdownData = {
            tag: [],
            items: []
        }

        this.utilsService.get(API).pipe(
            map((res: any) => (res.data)),
            tap((res: DropdownTag) => {
                this.dropdownData.items = res.items;
                this.dropdownData.tag = res.tag;
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    initForm = () => {
        this.formGroupMerge = this.fb.group({
            type: [null],
            itemId: [null, Validators.compose([Validators.required])],
            itemName: [null, Validators.compose([Validators.required])],
            tag: [null],
        })
    }

    onChangeType = (type?: string) => {
        const value = type ? type : this.formGroupMerge.get('type').value
        this.getImageTagReqiuredData(null)
        switch (value) {
            case this.enumForType.NEW_ITEM:
                this.formGroupMerge.patchValue({ itemId: null, tag: null })
                this.formGroupMerge.get('itemName').addValidators([Validators.required])
                this.formGroupMerge.get('tag').addValidators([Validators.required])
                this.formGroupMerge.get('tag').updateValueAndValidity()
                for (const c of ['itemId']) {
                    this.formGroupMerge.get(c).reset()
                    this.formGroupMerge.get(c).clearValidators()
                    this.formGroupMerge.get(c).updateValueAndValidity()
                }
                break;
            case this.enumForType.EXISTING_ITEM:
                this.formGroupMerge.patchValue({ itemName: null, itemId: null })
                this.formGroupMerge.get('itemId').addValidators([Validators.required])
                this.formGroupMerge.get('itemId').updateValueAndValidity()
                for (const c of ["itemName", "tag"]) {
                    this.formGroupMerge.get(c).reset()
                    this.formGroupMerge.get(c).clearValidators()
                    this.formGroupMerge.get(c).updateValueAndValidity()
                }
                break;
        }
    }

    onChangeTag = ($event: any) => {
        this.getImageTagReqiuredData($event?.value)
        this.formGroupMerge.get('itemId').reset()
    }

    get type(): string {
        return this.formGroupMerge.get('type').value;
    }

    get formValue() {
        return this.formGroupMerge.value;
    }

    onSaveImageMergeMapping = (modal: NgbModal) => {

        if (this.formGroupMerge.invalid) {
            this.formGroupMerge.markAllAsTouched();
            return;
        }

        const info = this.imageInquiryTabData.get().filter(item => item.isSelected).map(item => ({
            customerId: item.otherData.customerId,
            inquiryItemId: item.otherData.inquiryItemId
          }))

        const tag = this.formValue.tag?.label ? this.formValue.tag.label : this.formValue.tag;

        const param: ImageMergeSave = {
            itemId: this.formValue.type === this.enumForType.EXISTING_ITEM ? this.formValue.itemId : null,
            itemName: this.formValue.type === this.enumForType.NEW_ITEM ? this.formValue.itemName : null,
            tag: this.formValue.type === this.enumForType.NEW_ITEM ? tag : this.formValue.tag,
            inquiryItemDocIds: this.imageInquiryTabData.get().filter(item => item.isSelected).flatMap(item => item.images.map(image => image.id)),
            info: info
        }

        const API = this.utilsService.serverVariableService.INQUIRY_SAVE_MAPPED_IMAGE_WITH_ITEM;
        this.utilsService.post(API, param, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getMappedImagesTabData();
                this.getAllImageInquiryTabData();
            }),
            takeUntil(this.destroy$)
        ).subscribe()

    }

    // Clear Images
    openClearInquiryImagesModal = (content: TemplateRef<any>) => {
        this.modalService.open(content);
    }

    onClearInquiryImages = (modal: NgbModal) => {

        const param = {
            inquiryItemIds: this.imageInquiryTabData.get().filter(item => this.checkIfAnySelected() || item.isSelected).flatMap(item => item.otherData.inquiryItemId),
        }

        const API = this.utilsService.serverVariableService.INQUIRY_CLEAR_MAPPED_IMAGE;

        this.utilsService.post(API, param, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getMappedImagesTabData();
                this.getAllImageInquiryTabData();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    // Link Inquiry Btn
    openLinkInquiryModal = (item: ImageMappedInquiry, content: TemplateRef<any>) => {
        const inquiryIds = this.imageInquiryTabData.get().filter(item => item.isSelected).map(a => a.otherData.inquiryId);
        if(hasDuplicates(inquiryIds)) {
            this.utilsService.toasterService.error('Multiple Items cannot be merged for same inquiry.', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
            });
            return;
        }
        this.modalService.open(content);
        this.mappedImageObj.set(item);
    }

    onLinkInquiry = (modal: NgbModal) => {
        const info = this.imageInquiryTabData.get().filter(item => item.isSelected).map(item => ({
            customerId: item.otherData.customerId,
            inquiryItemId: item.otherData.inquiryItemId
        }))

        // Ignoring key from type using Omit
        const param: Omit<ImageMergeSave, 'itemName'> = {
            itemId: this.mappedImageObj.get().id,
            tag: this.mappedImageObj.get().tag,
            inquiryItemDocIds: this.imageInquiryTabData.get().filter(item => item.isSelected).flatMap(item => item.images.map(image => image.id)),
            info: info
        }

        const API = this.utilsService.serverVariableService.INQUIRY_SAVE_MAPPED_IMAGE_WITH_ITEM;

        this.utilsService.post(API, param, { toast: true }).pipe(
            tap(() => {
                this.modalService.close(modal);
                this.getMappedImagesTabData();
                this.getAllImageInquiryTabData();
            }),
            takeUntil(this.destroy$)
        ).subscribe()
    }

    redirectToNewItem = (itemId: number) => {
        this.utilsService.redirectTo(`/users/inventory/items/inquiry-item/${itemId}`)
    }

    customSearchFn(term: string, item: any) {
        const lowerCaseTerm = term.toLocaleLowerCase();
        return item?.skuId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
            item?.dummySKUId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
            item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
    }

    onClearDateOnly = () => {
        this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null }));
        if(this.selectedTab() === this.enumForTab.IMAGE_INQUIRES) {
            this.onChangeFilterImageInq(null, 'date', true);
        } else {
            this.onChangeFilter(null, 'date', true);
        }
    }
      
}
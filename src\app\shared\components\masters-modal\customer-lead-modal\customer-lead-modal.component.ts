import { Component, EventEmitter, inject, input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { UtilsService } from '@service/utils.service';

@Component({
  selector: 'app-customer-lead-modal',
  templateUrl: './customer-lead-modal.component.html',
  styleUrls: ['./customer-lead-modal.component.css']
})
export class CustomerLeadModalComponent implements OnInit {

  formGroup = input.required<FormGroup>();
  countryCodeDropdown = input.required<any[]>();
  isAdd = input.required<boolean>();
  isFromInquiry = input.required<boolean>();

  utilsService = inject(UtilsService)

  @Output() onSave = new EventEmitter<void>();

  constructor() { }

  ngOnInit() {
  }

  onSaveCustomerLead() {
    this.onSave.emit()
  }

}

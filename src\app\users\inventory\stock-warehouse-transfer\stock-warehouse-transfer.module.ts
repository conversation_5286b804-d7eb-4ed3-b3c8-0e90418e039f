import { RouterModule, Routes } from "@angular/router";
import { StockWarehouseTransferComponent } from "./stock-warehouse-transfer.component";
import { SharedModule } from "src/app/shared/shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { SwtMissingItemByUsReportComponent } from "./swt-missing-item-by-us-report/swt-missing-item-by-us-report.component";
import { SwtRequestedBySelfComponent } from "./swt-requested-by-self/swt-requested-by-self.component";
import { SwtRequestedByUsComponent } from "./swt-requested-by-us/swt-requested-by-us.component";
import { SwtNewApprovalStockTransferComponent } from "./swt-new-approval-stock-transfer/swt-new-approval-stock-transfer.component";
import { SwtNewRequestedBySelfComponent } from "./swt-new-requested-by-self/swt-new-requested-by-self.component";

const routes: Routes = [
    { path: '', component: StockWarehouseTransferComponent },
    { path: 'approve-request/:id', component: SwtNewApprovalStockTransferComponent },
    { path: 'swt-new-requested-by-self', component: SwtNewRequestedBySelfComponent },
]

@NgModule({
    imports: [
        CommonModule,
        SharedModule.forRoot(),
        RouterModule.forChild(routes)
    ],
    declarations: [StockWarehouseTransferComponent, SwtNewRequestedBySelfComponent, SwtNewApprovalStockTransferComponent, SwtRequestedByUsComponent, SwtRequestedBySelfComponent, SwtMissingItemByUsReportComponent]
})

export class StockWarehouseTransferModule { }

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { NewStockShiftingComponent } from './new-stock-shifting/new-stock-shifting.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { StockShiftingComponent } from './stock-shifting.component';

const routes: Routes = [
  { path: '', component: StockShiftingComponent },
  { path: 'new-stock-shifting', component: NewStockShiftingComponent },
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot(),
  ],
  declarations: [StockShiftingComponent, NewStockShiftingComponent]
})
export class StockShiftingModule { }

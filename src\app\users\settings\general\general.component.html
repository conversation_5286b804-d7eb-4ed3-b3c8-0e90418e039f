<div class="card card-theme card-settings">
  <div class="card-body pt-0" [formGroup]="settingsForm">
    <div class="settings-row-wrapper">
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Send WhatsApp message to sales person on stock inward</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="sendwhatsapp">
              <input type="checkbox" id='sendwhatsapp' formControlName="sendMsgSalesPerson"
                [(ngModel)]="obj.sendMsgSalesPerson" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Send WhatsApp/Telegram message to customers on stock inward</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="sendmessage">
              <input type="checkbox" id='sendmessage' formControlName="sendMsgCustomer"
                [(ngModel)]="obj.sendMsgCustomer" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>All Item PDF Send to Telegram</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="sendpdf">
              <input type="checkbox" id='sendpdf' formControlName="sendPDFTelegram" [(ngModel)]="obj.sendPDFTelegram" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Show Item Property During QC</p>
        </div>
        <div class="setting-col-right">
          <div class="switch-box">
            <label class="switch" htmlFor="itemPropertyDuringQC">
              <input type="checkbox" id='itemPropertyDuringQC' formControlName="itemPropertyDuringQC" [(ngModel)]="obj.isShowItemPropertyDuringQC" />
              <div class="slider round"></div>
            </label>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Purchase Price Variation</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter" mask="separator.2" thousandSeparator=""
                formControlName="purchasePriceVariation" [(ngModel)]="obj.purchasePriceVariation" [maxlength]="utilsService.validationService.MAX_10">
              <span class="input-group-text">%</span>
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>PO alert Ratio</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <input type="text" class="form-control" placeholder="Enter PO alert Ratio" formControlName="poAlertRation"
              [(ngModel)]="obj.poAlertRation" mask="separator.2" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_10">
          </div>
        </div>
      </div>
      <div class="settings-row">
        <p class="settings-info"><b>*User will input the ratio as 0.85. Once ratio is below the alert ratio display the
            row in PO created as Red</b></p>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Reminder Of Payment Due by customer</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input [maxlength]="3" mask="separator.0" thousandSeparator="" type="text" class="form-control"
                placeholder="Enter days" formControlName="payDueByCustomer" [(ngModel)]="obj.payDueByCustomer">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['payDueByCustomer'].hasError('required') && !settingsForm.controls['payDueByCustomer'].valid && settingsForm.controls['payDueByCustomer'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
          <p class="mb-0">Repeat After Every</p>
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input [maxlength]="3" mask="separator.0" thousandSeparator="" type="text" class="form-control"
                placeholder="Enter days" formControlName="repeatAfterEvery" [(ngModel)]="obj.repeatAfterEvery">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['repeatAfterEvery'].hasError('required') && !settingsForm.controls['repeatAfterEvery'].valid && settingsForm.controls['repeatAfterEvery'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Reminder Of Payment Due to supplier</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input type="text" class="form-control" placeholder="Enter days" formControlName="payDueBySupplier"
                [(ngModel)]="obj.payDueBySupplier" [maxlength]="3" mask="separator.0" thousandSeparator="">
              <span class="input-group-text">Days</span>
            </div>
            <div class="message error-message"
              *ngIf="!settingsForm.controls['payDueBySupplier'].hasError('required') && !settingsForm.controls['payDueBySupplier'].valid && settingsForm.controls['payDueBySupplier'].touched">
              {{utilsService.validationService.SETTINGS_MAX_DAYS}}
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Credit Limit for all Customers</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm">
            <div class="input-group input-group-sm">
              <input [maxlength]="utilsService.validationService.MAX_10" type="text" class="form-control"
                placeholder="Enter" aria-label="Credit Limit for all Customers" aria-describedby="button-addon1"
                formControlName="creditLimitCustomer" [(ngModel)]="obj.creditLimitCustomer" mask="separator.3"
                thousandSeparator="">
              <span class="input-group-text">Rs</span>
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Inquiry Ideal Timer (mins)</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group form-group-sm" [ngClass]="{'form-error': settingsForm.get('inquiryIdealTimer').invalid && settingsForm.get('inquiryIdealTimer').touched}">
            <div class="input-group input-group-sm">
              <input class="form-control" placeholder="Time" [(ngModel)]="obj.inquiryIdealTimer" formControlName="inquiryIdealTimer">
            </div>
          </div>
        </div>
      </div>
      <div class="settings-row">
        <div class="setting-col-left">
          <p>Financial Year*</p>
        </div>
        <div class="setting-col-right">
          <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Select" [multiple]="false" [clearable]="false" [items]="fyDropdown" bindLabel="label"
              bindValue="value" [(ngModel)]="obj.fiscalYearType" formControlName="fiscalYearType">
            </ng-select>
            <div class="message error-message"
              *ngIf="settingsForm.controls['fiscalYearType'].hasError('required') &&  settingsForm.controls['fiscalYearType'].touched">
              {{utilsService.validationService.FINANCIAL_YR_REQ}}
            </div>
          </div>
        </div>
      </div>
        <div class="settings-row">
          <div class="setting-col-left">
            <p>Extend Time</p>
          </div>
          <div class="setting-col-right">
            <div class="form-group form-group-sm" [ngClass]="{'form-error': settingsForm.get('extendTime').invalid && settingsForm.get('extendTime').touched}">
              <div class="input-group input-group-sm">
                <input type="text" class="form-control" placeholder="Enter Time" formControlName="extendTime" [maxlength]="4"
                  mask="separator.0" thousandSeparator="" [(ngModel)]="obj.extendTime">
                <span class="input-group-text">Mins</span>
              </div>
            </div>
          </div>
        </div>
        <div class="settings-row">
          <div class="setting-col-left">
            <p>Raise Stock Check Ticket after No of days</p>
          </div>
          <div class="setting-col-right">
            <div class="form-group theme-ngselect form-group-sm"
              [ngClass]="{'form-error': settingsForm.get('raiseSCAfterNoOfDays').invalid && settingsForm.get('raiseSCAfterNoOfDays').touched}">
              <div class="input-group input-group-sm">
                <input type="text" class="form-control" placeholder="Enter Days" formControlName="raiseSCAfterNoOfDays"
                  [maxlength]="4" mask="separator.0" thousandSeparator="" [(ngModel)]="obj.raiseSCAfterNoOfDays">
                <span class="input-group-text">Days</span>
              </div>
            </div>
          </div>
        </div>
        <div class="settings-row">
          <div class="setting-col-left">
            <p>Show existing mapped locations for carton mapping</p>
          </div>
          <div class="setting-col-right">
            <div class="switch-box">
              <label class="switch" htmlFor="showExistingLocationForCartonMapping">
                <input type="checkbox" id='showExistingLocationForCartonMapping'
                  formControlName="showExistingLocationForCartonMapping"
                  [(ngModel)]="obj.showExistingLocationForCartonMapping" />
                <div class="slider round"></div>
              </label>
            </div>
          </div>
        </div>
        <div class="settings-row">
          <div class="setting-col-left">
            <p>China Comment</p>
          </div>
          <div class="setting-col-right">
            <quill-editor [maxLength]="utilsService.validationService.MAX_4000" [(ngModel)]="obj.chinaCommentForPOPdf" [placeholder]="'Enter Comment'"
              [styles]="{minHeight: '150px', width: '700px'}" formControlName="chinaComment"/>
          </div>
        </div>
    </div>
  </div>
</div>

<div class='bottombar-wrapper bottom-fixed'>
  <div class='bottombar-container'>
    <div class='bottombar-left'>
      <button (click)="onSaveSettings.emit()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
          class="th th-outline-tick-circle"></i>
        Save</button>
      <button (click)="utilsService.redirectTo('/users/dashboard/')" type="button"
        class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
    </div>
    <div class='bottombar-right'>

    </div>
  </div>
</div>
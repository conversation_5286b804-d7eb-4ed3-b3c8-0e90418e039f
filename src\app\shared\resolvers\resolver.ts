import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { UtilsService } from '@service/utils.service';
import { catchError } from 'rxjs';
import { of } from 'rxjs';

export const extraTimeResolver: ResolveFn<any[]> = () => {
  const utilsService = inject(UtilsService)
  return utilsService.get(utilsService.serverVariableService.GET_ALL_USER_DROPDOWN_DATA_BY_ACTIVE_STATUS, null, true).pipe(
    catchError(() => of([]))
  )
};
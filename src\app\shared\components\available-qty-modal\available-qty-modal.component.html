<div class="dropdown-branch-marka dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
    #dropdown="ngbDropdown" container="body">
    <div class="d-flex flex-column align-items-start">
        @if(value() > 0) {
        <button (click)="onOpen()" class="btn btn-link p-0 text-primary mt-1" type="button" ngbDropdownToggle>
            {{value()}}
        </button>
        } @else {
        <button class="btn btn-link p-0 mt-1" type="button">
            {{value()}}
        </button>
        }
    </div>
    <div ngbDropdownMenu class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdown.isOpen">
        <div class="card-responsive">
            <div class="card card-theme4">
                <div class="card-header">
                    <div class="card-header-left">
                        <div class="card-header-title">
                            <h2>Available Qty</h2>
                        </div>
                    </div>
                    <div class="card-header-right">
                        <button (click)="dropdown.close()"
                            class="btn btn-xs btn-transparent btn-icon text-black column-filter-dropdown-close">
                            <i class="th th-close m-0"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive available-qty-scroll">
                        <table class="table-theme table-hover table table-bordered table-sticky">
                            <thead class="border-less">
                                <tr>
                                    <th class="">Marka</th>
                                    <th>Carton Qty</th>
                                    <th>Item/Carton</th>
                                    <th>Total C. Qty</th>
                                    <th>Loose Qty</th>
                                    <th>Total Qty</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(item of data(); let i = $index; track i) {
                                <tr>
                                    <td class="tbl-user">
                                        <div class="tbl-user-checkbox-srno">
                                            <div class="tbl-user-wrapper">
                                                <div class="tbl-user-text">
                                                    <p>{{item.marka}}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{item.cartonQty}}</td>
                                    <td>{{item.piecesPerCarton}}</td>
                                    <td>{{item.totalCartonQty}}</td>
                                    <td>{{item.looseQty}}</td>
                                    <td>{{item.totalQty}}</td>
                                    <td>{{item.locationName}}</td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="20" class="text-center">
                                        <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                    </td>
                                </tr>
                                }
                                @if(data().length > 0) {
                                <tr class="tbl-total">
                                    <td><b>Grand Total</b></td>
                                    <td>{{grandTotalComputed()?.cartonQty}}</td>
                                    <td>{{grandTotalComputed()?.piecesPerCarton}}</td>
                                    <td>{{grandTotalComputed()?.totalCartonQty}}</td>
                                    <td>{{grandTotalComputed()?.looseQty}}</td>
                                    <td>{{grandTotalComputed()?.totalQty}}</td>
                                    <td>-</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
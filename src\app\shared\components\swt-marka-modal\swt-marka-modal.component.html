<div class="dropdown-branch-marka dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
    #dropdown="ngbDropdown" container="body">
    <div class="d-flex flex-column align-items-start">
        <div class="mb-1" *ngFor="let marka of marka(); let i = index">
            <span class="d-block">{{ marka }}</span>
        </div>
        <button (click)="openMarkaModal.emit()" class="btn btn-link p-0 text-primary mt-1" type="button" ngbDropdownToggle>
            View
        </button>
    </div>
    <div ngbDropdownMenu class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdown.isOpen">
        <div class="card-responsive">
            <div class="card card-theme4">
                <div class="card-header">
                    <div class="card-header-left">
                        <div class="card-header-title">
                            <h2><PERSON>a Details</h2>
                        </div>
                    </div>
                    <div class="card-header-right">
                        <button (click)="dropdown.close()"
                            class="btn btn-xs btn-transparent btn-icon text-black column-filter-dropdown-close">
                            <i class="th th-close m-0"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive available-qty-scroll">
                        <table class="table-theme table table-bordered">
                            <thead class="border-less">
                                <tr>
                                    <th class="">
                                        Marka
                                    </th>
                                    <th *ngIf="!isLoose()">C x Qty = Total</th>
                                    <th *ngIf="isLoose()">Loose Qty</th>
                                    <th *ngIf="!isLoose()">Carton Qty</th>
                                    <th>Location</th>
                                    <th *ngIf="!isLoose() && !isPageView()">Carton</th>
                                    <th *ngIf="isLoose() && !isPageView()">Loose</th>
                                    <th class="tbl-bg-secondary" *ngIf="isPageView() && !isLoose()">Requested Carton</th>
                                    <th class="tbl-bg-secondary" *ngIf="isPageView() && isLoose()">Requested Loose</th>
                                    <th class="tbl-bg-success" *ngIf="isPageView() && !isLoose()">Send <br/> Carton Qty</th>
                                    <th class="tbl-bg-success" *ngIf="isPageView() && isLoose()">Send <br/> Loose Qty</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(data()?.markaData?.length > 0){
                                    <ng-container *ngFor="let markaData of data()?.markaData; index as i">
                                        <ng-container *ngFor="let loc of markaData.locations; index as j">
                                            <tr>
                                                <td *ngIf="j === 0" class="tbl-user w-50" [attr.rowspan]="markaData.locations.length">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <div class="tbl-user-wrapper">
                                                            <div class="tbl-user-text">
                                                                <p>{{ markaData.marka }}</p>
                                                                <span *ngIf="markaData.age">Age: {{ markaData.age }} Days</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                
                                                <td *ngIf="j === 0 && !isLoose()" [attr.rowspan]="markaData.locations.length">
                                                    {{ (markaData.totalCartonQty || 0) }} x {{ (markaData.locations[0].piecesPerCarton || 0) }}  = {{ (markaData.totalCartonQty || 0) * (markaData.locations[0].piecesPerCarton || 0) }}
                                                </td>
                                
                                                <td *ngIf="isLoose()">{{ loc.looseQty }}</td>
                                                <td *ngIf="!isLoose()">
                                                    {{ loc.cartonQty }} x {{ loc.piecesPerCarton }} = {{ loc.cartonQty * loc.piecesPerCarton }}
                                                </td>
                                                <td>{{ loc.locationName }}</td>
                                
                                                <td *ngIf="!isLoose() && !isPageView()" class="max-w-100">
                                                    <div class="form-group form-group-sm" [ngClass]="{ 'form-error': checkIfInvalid(loc.cartonField, i, j) }">
                                                        <input placeholder="Enter" (ngModelChange)="onChangeCartonLoose($event, i, j)" type="text" class="form-control"
                                                            [ngModel]="loc.cartonField" [maxlength]="5" mask="separator.0" thousandSeparator=""/>
                                                    </div>
                                                </td>
                                                <td *ngIf="isLoose() && !isPageView()" class="max-w-100">
                                                    <div class="form-group form-group-sm" [ngClass]="{ 'form-error': checkIfInvalid(loc.looseField, i, j) }">
                                                        <input placeholder="Enter" (ngModelChange)="onChangeCartonLoose($event, i, j)" type="text" class="form-control"
                                                            [ngModel]="loc.looseField" [maxlength]="5" mask="separator.0" thousandSeparator=""/>
                                                    </div>
                                                </td>
                                                <td *ngIf="isPageView() && !isLoose()">{{loc.reqQty || '-'}}</td>
                                                <td *ngIf="isPageView() && isLoose()">{{loc.reqQty || '-'}}</td>
                                                <td *ngIf="isPageView() && !isLoose()">{{loc.approveQty || '-'}}</td>
                                                <td *ngIf="isPageView() && isLoose()">{{loc.approveQty || '-'}}</td>
                                            </tr>
                                            <tr *ngIf="!data()?.markaData">
                                                <td colspan="20" class="text-center">
                                                    <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </ng-container>
                                } @else {
                                    <tr>
                                        <td colspan="20" class="text-center">
                                            <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot *ngIf="data()?.markaData?.length > 0">
                                <tr class="tbl-total">
                                    <td>-</td>
                                    <td *ngIf="!isLoose()">{{totalQtyAll()}}</td>
                                    <td *ngIf="isLoose()">-</td>
                                    <td *ngIf="!isLoose()">-</td>
                                    <td>-</td>
                                    <td *ngIf="!isLoose() && !isPageView()">{{ totalCartonFieldSum() }}</td>
                                    <td *ngIf="isLoose() && !isPageView()">{{ totalLooseFieldSum() }}</td>
                                    <td *ngIf="isPageView() && !isLoose()">-</td>
                                    <td *ngIf="isPageView() && isLoose()">-</td>
                                    <td *ngIf="isPageView()">-</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="card-footer mt-2 d-flex justify-content-between align-items-center" *ngIf="!isPageView()">
                    <div class="d-flex align-items-center">
                        <button type="button" class="btn btn-sm btn-outline-white btn-icon-text" (click)="onClear()">
                            <i class="th th-outline-close-circle"></i>
                            Clear
                        </button>
                    </div>
                    <div class="d-flex align-items-center">
                        <button [disabled]="disableIfInvalid()" (click)="onSaveMarkaQty.emit(); dropdown.close()" type="button"
                            class="btn btn-sm btn-primary btn-icon-text">
                            <i class="th th-outline-tick-circle"></i>
                            Save
                        </button>
                        <button (click)="dropdown.close()" type="button" class="btn btn-sm btn-outline-white ms-2">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
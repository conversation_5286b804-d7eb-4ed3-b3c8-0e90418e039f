import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { SalesOrdersComponent } from "./sales-orders.component";
import { NewSalesOrderComponent } from "./new-sales-order/new-sales-order.component";
import { SalesOrdersInquiryComponent } from "./sales-orders-inquiry/sales-orders-inquiry.component";
import { SalesOrdersStockCheckComponent } from "./sales-orders-stock-check/sales-orders-stock-check.component";
import { SalesOrdersDraftComponent } from "./sales-orders-draft/sales-orders-draft.component";
import { SalesOrdersProformaInvoiceComponent } from "./sales-orders-proforma-invoice/sales-orders-proforma-invoice.component";
import { SalesOrdersOtherBranchComponent } from "./sales-orders-other-branch/sales-orders-other-branch.component";
import { SalesOrdersSoCreatedComponent } from "./sales-orders-so-created/sales-orders-so-created.component";
import { SalesOrdersPackingComponent } from "./sales-orders-packing/sales-orders-packing.component";
import { SalesOrdersShippedComponent } from "./sales-orders-shipped/sales-orders-shipped.component";
import { SalesOrdersDeliveredComponent } from "./sales-orders-delivered/sales-orders-delivered.component";
import { SalesOrdersCompletedComponent } from "./sales-orders-completed/sales-orders-completed.component";
import { NewInquiryComponent } from "./new-inquiry/new-inquiry.component";
import { SalesOrderService } from "./sales-order.service";

const routes: Routes = [
    { path: '', component: SalesOrdersComponent },
    { path: 'new-inquiry', component: NewInquiryComponent, title: 'Inquiry' },
    { path: 'new-sales-order', component: NewSalesOrderComponent },
    { path: 'new-sales-order/:inquiryId/customer/:customerId', component: NewSalesOrderComponent },
    { path: 'new-sales-order/inquiry-conversion/customer/:customerId', component: NewSalesOrderComponent },
    { path: 'edit-sales-order/:id', component: NewSalesOrderComponent },
    { path: 'edit-draft/:draftId', component: NewSalesOrderComponent },
    { path: 'move-to-so-created/:convertToSoId', component: NewSalesOrderComponent },
    { path: 'new-proforma-invoice', component: NewSalesOrderComponent }
]

@NgModule({
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        SharedModule.forRoot()
    ],
    declarations: [SalesOrdersComponent,
        NewSalesOrderComponent,
        SalesOrdersInquiryComponent,
        SalesOrdersStockCheckComponent,
        SalesOrdersDraftComponent,
        SalesOrdersProformaInvoiceComponent,
        SalesOrdersOtherBranchComponent,
        SalesOrdersSoCreatedComponent,
        SalesOrdersPackingComponent,
        SalesOrdersShippedComponent,
        SalesOrdersDeliveredComponent,
        SalesOrdersCompletedComponent,
        NewInquiryComponent
    ],
    providers: [SalesOrderService]
})
export class SalesOrderModule { }
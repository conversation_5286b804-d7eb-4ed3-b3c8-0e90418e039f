import { computed, inject, signal } from "@angular/core";
import { EnumForStockBranchTransfer } from "@enums/EnumForStockBranchTransfer.enum";
import { AllBranchList, AvailableQtyModalData, SbtBranchStock, SbtWarehouse } from "@modal/SBTBranchStock";
import { BranchStockPagination } from "@modal/request/BranchStockPagination";
import { UtilsService } from "@service/utils.service";
import saveAs from "file-saver";
import { Subject, takeUntil, tap } from "rxjs";

export class SbtService {

  utilsService = inject(UtilsService);

  enumForTabs = EnumForStockBranchTransfer;
  selectedTab = signal('');
  paginationRequest = signal<BranchStockPagination>(null);

  branchStockList = signal<SbtBranchStock[]>([]);

  allBranches = signal<AllBranchList[]>([]);
  allWarehouse = signal<SbtWarehouse[]>([]);

  stockListEmpty = computed(() => (this.branchStockList() || []).length === 0);

  destroy$ = new Subject<void>();

  constructor() {

  }

  tabWiseAPI = () => {
    switch (this.selectedTab()) {
      case this.enumForTabs.BRANCH_STOCK:
        this.getBranchStockList();
        break;
    }
  }

  getBranchStockList = () => {

    const param = this.utilsService.removeKeys(this.paginationRequest(), ['totalData', 'pagination']);

    this.utilsService.post(this.utilsService.serverVariableService.BRANCH_STOCK_LISTING, param, null, true).pipe(
      tap((res) => {
        this.paginationRequest.update(a => ({
          ...a,
          pagination: res?.itemList,
          totalData: res?.itemList?.['totalElements']
        }));

        this.branchStockList.set(res?.itemList?.['content']);

        this.branchStockList.update((data) =>
          (data || []).map((a) => ({
            ...a,
            allWarehouseStocks: res?.currentBranchWarehouse || [],
            allBranches: res?.allBranches || []
          }))
        );
        this.allBranches.set(res?.allBranches || []);
        this.allWarehouse.set(res?.currentBranchWarehouse || []);
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onChangeTab = (value: string) => {
    this.selectedTab.set(value);

    switch (this.selectedTab()) {
      case this.enumForTabs.BRANCH_STOCK:
        this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: "100" }));
        this.getBranchStockList();
        break;
      default:
        break;
    }
  }

  addPageSizeData(event: any) {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
    this.tabWiseAPI();
  }

  pageNumber(event: any) {
    this.paginationRequest.update(a => ({ ...a, pageNo: event }));
    this.tabWiseAPI();
  }

  // Filter
  onChangeFilter = (event: any, type: 'search' | 'showLowStock' | 'showOutOfStock') => {
    switch (type) {
      case 'search':
        this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
        break;
      case 'showLowStock':
        this.paginationRequest.update(a => ({ ...a, showLowStock: !event ? null : event }));
        break;
      case 'showOutOfStock':
        this.paginationRequest.update(a => ({ ...a, showOutOfStock: !event ? null : event }));
        break;
    }
    this.destroy$.complete()
    this.tabWiseAPI();
  }

  onClear = () => {
    switch (this.selectedTab()) {
      case this.enumForTabs.BRANCH_STOCK:
        this.paginationRequest.update(a => ({ ...a, searchText: null, showLowStock: null, showOutOfStock: null }));
        this.getBranchStockList();
        break;
    }
  }

  getAvailableQtyCommonAPI = (itemId: number, index: number, warehouseId: number) => {

    const param = { itemId, warehouseId }

    // const current = this.branchStockList()[index];
    // if (current?.isDataLoaded) return;

    this.utilsService.post(this.utilsService.serverVariableService.AVAILABLE_QTY_OF_ITEM_WISE, param, null, true).pipe(
      tap((res: AvailableQtyModalData[]) => {
        this.branchStockList.update((data) => (data || []).map((a, i) => {
          if (i !== index) return a;
          if (i === index) {
            const availableQtyModalData = res.map(a => {
              return {
                ...a,
                looseQty: a.looseQty || 0,
                totalCartonQty: (a.cartonQty || 0) * (a.piecesPerCarton || 0),
                totalQty: ((a.cartonQty || 0) * (a.piecesPerCarton || 0)) + (a.looseQty || 0)
              }
            });
            return { ...a, availableQtyModalData, isDataLoaded: true }
          }
          return a
        })
        )
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onRefresh = () => {
    this.tabWiseAPI();
  }

  exportExcel = () => {
    const { searchText, showLowStock, showOutOfStock } = this.paginationRequest()
    const param = { searchText, showLowStock, showOutOfStock }

    let API = null;
    let name = null;
    switch (this.selectedTab()) {
      case this.enumForTabs.BRANCH_STOCK:
        API = this.utilsService.serverVariableService.BRANCH_STOCK_EXCEL;
        name = 'Branch Stock Sheet';
        break;
    }

    this.utilsService.exportReport(param, API).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), `${name}`);
    });
  }
}

<div class="page-content">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Add New Inquiry</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/sales/sales-orders']"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class="card card-theme card-forms" [formGroup]="form">
            <div class="card-body">
                <div class="row">
                    <div class="col-5">
                        <div class="form-group theme-ngselect form-group-inline-control required theme-ngselect-group-list">
                            <label class="form-label">Customer</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <div class="form-control-wrapper">
                                        <ng-select class="lead-dropdown" placeholder="Select Customer" [multiple]="false" [clearable]="false"
                                            [items]="allInquiryDropdowns.customerLead" bindLabel="displayName" bindValue="id" formControlName="customerId">
                                            <ng-template ng-option-tmp let-item="item">
                                                <div class="tbl-user">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <div class="tbl-user-wrapper">
                                                            <div class="tbl-user-text-action">
                                                                <div class="tbl-user-text wrap-ng-options">
                                                                    <p [title]="item.displayName">{{ item.displayName }} </p>
                                                                    <span>{{ item.phone ? (item.phone | asterick: 8) :'' }}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                    </div>
                                    <button
                                        [pageAccess]="{page: utilsService.enumForPage.CUSTOMER_LEAD, action: utilsService.enumForPage.ADD_CUSTOMER_LEAD}"
                                        class="btn  btn-icon btn-transparent text-black" (click)="openAddCustomerLeadModal()"><i
                                            class="th th-outline-add-circle "></i>
                                    </button>
                                    <!-- <div ngbDropdown placement="auto" #dropdown="ngbDropdown"
                                        class="dropdown dropdown-with-tables dropdown-no-arrow">
                                        <button class="btn btn-icon btn-transparent text-primary p-0" ngbDropdownToggle>
                                            <i class="th th-outline-bag"></i>
                                        </button>
                                        <div ngbDropdownMenu class="dropdown-menu-wide">
                                            <div class="card-responsive">
                                                <div class="card card-theme4 without-shadow-card-grid">
                                                    <div class="card-header">
                                                        <div class="card-header-left">
                                                            <div class="card-header-title">
                                                                <h2>Past Purchased Products</h2>
                                                            </div>
                                                        </div>
                                                        <div class="card-header-right">
                                                            <button (click)="dropdown.close()"
                                                                class="btn btn-xs btn-transparent btn-icon text-black column-filter-dropdown-close">
                                                                <i class="th th-close m-0"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table class="table-theme table-hover table table-bordered">
                                                                <thead class="border-less">
                                                                    <tr>
                                                                        <th>Marka</th>
                                                                        <th>SKU</th>
                                                                        <th>Sales Price</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr *ngFor="let item of [1,2,3]">
                                                                        <td class="tbl-user">
                                                                            <div class="tbl-user-checkbox-srno">
                                                                                <div
                                                                                    class="checkbox checkbox-primary checkbox-small">
                                                                                    <input type="checkbox"
                                                                                        id="tbl-checkbox2"
                                                                                        class="material-inputs filled-in" />
                                                                                    <label for="tbl-checkbox2"></label>
                                                                                </div>
                                                                                <div class="tbl-user-wrapper">
                                                                                    <div class="tbl-user-image">
                                                                                        <img src="assets/images/dummy-product.png"
                                                                                            alt="valamji">
                                                                                    </div>
                                                                                    <div class="tbl-user-text">
                                                                                        <p>ZA-50-585-12
                                                                                        </p>
                                                                                        <span>Age: 56
                                                                                            Days</span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td>20</td>
                                                                        <td>17/09/2024</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer">
                                                        <div class="button-group">
                                                            <button (click)="dropdown.close()" type="button"
                                                                class="btn btn-sm btn-primary btn-icon-text">
                                                                <i class="th th-outline-tick-circle"></i>
                                                                Add Items
                                                            </button>
                                                            <button (click)="dropdown.close()" type="button"
                                                                class="btn btn-sm btn-outline-white btn-icon-text">
                                                                <i class="th th-outline-close-circle"></i>
                                                                Cancel
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> -->
                                </div>
                                <div class="message error-message"
                                    *ngIf="form.controls['customerId'].hasError('required') && form.controls['customerId'].touched">
                                    {{utilsService.validationService.CUS_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control">
                            <label class="form-label">Customer Note</label>
                            <div class="form-control-wrapper">
                                <input type="text" class="form-control" placeholder="Enter Customer Note"
                                    formControlName="customerNote" [maxlength]="utilsService.validationService.MAX_200">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required theme-ngselect">
                            <label class="form-label">Mobile No</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <ng-select placeholder="Ph." [multiple]="false" [clearable]="false"
                                        [items]="allInquiryDropdowns.Country" bindLabel="countryExtension"
                                        bindValue="id" formControlName="countryId">
                                    </ng-select>
                                    <input formControlName="phone" type="text" placeholder="Enter mobile number"
                                        class="form-control">
                                </div>
                                <div class="message error-message"
                                    *ngIf="form.controls['countryId'].hasError('required') && form.controls['countryId'].touched">
                                    {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="form.controls['phone'].hasError('required') && form.controls['phone'].touched && form.controls['countryId'].valid">
                                    {{utilsService.validationService.PHONE_NUMBER_REQUIRED}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!form.controls['phone'].hasError('required') && !form.controls['phone'].valid && form.controls['phone'].touched && form.controls['countryId'].valid">
                                    {{utilsService.validationService.PHONE_NUMBER_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control">
                            <label class="form-label">Upload File</label>
                            <div class="form-control-wrapper">
                                <button (click)="fileInput.click()"
                                    class='btn btn-fileupload btn-fileupload-white fw-400'> <i class="bi bi-upload"></i>
                                    Upload File
                                    <input hidden (change)="onSelectExcel($event);fileInput.value=''" type="file"
                                        #fileInput accept=".csv" />
                                </button>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label"></label>
                            <div class="form-control-wrapper">
                                <button (click)="utilsService.downloadSampleFileInquiryItem()" class="btn btn-sm btn-light-primary btn-icon-text"><i
                                        class="bi bi-download"></i>
                                    Download Sample CSV File
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-4"
                        (dragover)="onSelectAttachments($event, false);doc.value = ''"
                        (drop)="onSelectAttachments($event, false);doc.value = ''"
                        (paste)="onSelectAttachments($event, false)">
                        <div class="d-flex flex-column h-100">
                            <div class="attachments-wrapper">
                                <div class="form-group">
                                    <div class="form-label">Upload Files<i class="th th-outline-info-circle ms-1"
                                            [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom"
                                            container="body" triggers="hover"></i>
                                    </div>
                                </div>
                                <div class='attachments-container'>
                                    <div class='attachments-content'>
                                        <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                        <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                                    </div>
                                    <input #doc type="file" ref={imageRef} multiple
                                        (change)="onSelectAttachments($event, false);doc.value = ''" />
                                </div>
                                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                    <div class='attachments-upload-row'>
                                        @for (item of attachmentsList.get(); track $index; let i = $index) {
                                        <div class="attachments-upload-col">
                                            <div class="card-attachments-upload">
                                                <div class="attachments-image">
                                                    @if (utilsService.isImage(item.originalName)) {
                                                    <img loading="lazy"
                                                        (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                        [src]="item.formattedName ? (item.file ? item.formattedName : utilsService.imgPath + item.formattedName) : null"
                                                        alt="valamji" />
                                                    } @else if (utilsService.isMedia(item.originalName)) {
                                                    <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                        src="assets/images/files/file-video.svg" alt="valamji" />
                                                    } @else if (utilsService.isExcel(item.originalName)) {
                                                    <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                        src="assets/images/files/file-excel.svg" alt="valamji" />
                                                    } @else if (utilsService.isDocument(item.originalName)) {
                                                    <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                                        src="assets/images/files/file-pdf.svg" alt="valamji" />
                                                    }
                                                </div>
                                                <div class="attachments-text" [ngbTooltip]="item.originalName"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <h6 class="file-name">{{ item.originalName }}</h6>
                                                </div>
                                                <button (click)="removeAttachment(i, false)" class="btn-close"
                                                    variant="close"><i class="th th-close"></i></button>
                                            </div>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-3">
                        <div class="timer">
                            <h2 class="text-end"> <i class="th th-outline-clock"></i>
                                @if(timeInSeconds) {
                                    <countdown #cd [config]="{leftTime: timeInSeconds, format: timeInSeconds > 3600 ? 'HH:mm:ss' : 'mm:ss'}" (event)="handleEvent($event)" />
                                }
                            </h2>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <hr />
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="inner-title-wrapper">
                            <div class="inner-title-left">
                                <div class="inner-title-text">
                                    <h6 class="">Add Items*</h6>
                                </div>
                            </div>
                            <div class="inner-title-rigth">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered " formArrayName="inquiryItems">
                                <thead class="border-less">
                                    <tr>
                                        <th>Item Details</th>
                                        <th class="text-center">Urg. Inq.</th>
                                        <th class="text-center">SO</th>
                                        <th class="text-center">Inq.</th>
                                        <th class="text-center">NIT</th>
                                        <th>NIT. Reason</th>
                                        <th>Inq. Country</th>
                                        <th>Order Unit</th>
                                        <th>Marka</th>
                                        <th>Inq. Qty</th>
                                        <th>PCS/Carton</th>
                                        <th>Total PCS</th>
                                        <th>Rate</th>
                                        <th>Total Amount</th>
                                        <th>Sales <br /> Executive <br /> Offered Price</th>
                                        <th>Sales <br /> Manager <br /> Price</th>
                                        <th>Customer <br />Requested <br />Price</th>
                                        <th>Last 3 Sales <br />Info</th>
                                        <th>Avail. Qty <br />(Rack)</th>
                                        <th>Avail. Qty <br />(Warehouse)</th>
                                        <th>Upcoming Qty</th>
                                        <th>HSN Code</th>
                                        <th>GST Rate</th>
                                        <th>GST (%)</th>
                                        <th>GST Amount</th>
                                        <th>Total Amount <br />(With GST)</th>
                                        <th>Note</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let contact of inquiryItems.controls; index as i" [formGroupName]="i"
                                        class="tbl-add-row tbl-bg-white">
                                        @let isOnlyImage = contact.get('isOnlyImage').value;
                                        @let branchMaster = contact.get('branchMaster')?.value;
                                        <td class="tbl-user tbl-form-group">
                                            <div
                                                class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100">
                                                <div
                                                    class="d-flex align-items-center gap-2 justify-content-space-between">
                                                    @if (!isOnlyImage) {
                                                    <ng-select id="itemId-{{i}}" class="new-po-select"
                                                        appendTo=".theme-ngselect" [items]="allInquiryDropdowns.item"
                                                        (change)="onItemChange($event, i)"
                                                        [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}"
                                                        formControlName="itemId" bindLabel="name"
                                                        placeholder="Select Item" bindValue="id" [virtualScroll]="true"
                                                        [searchFn]="customSearchFn">
                                                        <ng-template ng-header-tmp>
                                                            <div
                                                                class="d-flex fw-bold bg-light border-bottom py-2 px-3">
                                                                <div class="fs-14">Item</div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-label-tmp let-item="item">
                                                            <div class="tbl-user">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image">
                                                                            <img loading="lazy"
                                                                                [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                                                                alt="valamji">
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{ item.displayName }} </p>
                                                                                <span>{{item.skuId}}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-option-tmp let-item="item">
                                                            <div
                                                                class="d-flex align-items-center border-bottom py-2 px-3">
                                                                <div>
                                                                    <div class="tbl-user">
                                                                        <div class="tbl-user-checkbox-srno">
                                                                            <div class="tbl-user-wrapper">
                                                                                <div class="tbl-user-image">
                                                                                    <img loading="lazy"
                                                                                        [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                                                                        alt="valamji">
                                                                                </div>
                                                                                <div class="tbl-user-text-action">
                                                                                    <div
                                                                                        class="tbl-user-text po-description">
                                                                                        <p [title]="item.displayName">{{
                                                                                            item.displayName }} </p>
                                                                                        <span>{{ item.skuId }}</span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-notfound-tmp>
                                                            <div
                                                                class="d-flex align-items-center border-bottom py-2 px-3 justify-content-center fs-12">
                                                                <div>No Item Found</div>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                    }
                                                    @else {
                                                    <div class="tbl-user-wrapper new-po-select">
                                                        @let imagesList = contact.get('images').value;
                                                        <div class="tbl-user-image"
                                                            (click)="openWebcamModal(imagesList, i)">
                                                            @let imageF = contact.get('firstImage').value;
                                                            <img [src]="imageF" alt="valamji">
                                                        </div>
                                                        <div class="tbl-user-text-action">
                                                            <div class="tbl-user-text">
                                                                <p>{{ contact.get('itemName').value }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    }
                                                    <div class="dropdown">
                                                        <div class="d-flex align-items-center gap-2">
                                                            <button class="btn btn-xs btn-light-white btn-icon"
                                                                id="actionDropDown" data-bs-toggle="dropdown"
                                                                aria-expanded="false"
                                                                data-bs-popper-config='{"strategy":"fixed"}'>
                                                                <i class="th th-outline-more"></i>
                                                            </button>
                                                            @if (contact.get('itemId').value) {
                                                            <button ngbTooltip="Associated Items" placement="left" container="body" triggers="hover"
                                                                class="btn btn-xs btn-link btn-icon text-primary" (click)="openAssociatedInqItemModal(i)"><i
                                                                    class="th th-outline-box"></i>
                                                            </button>
                                                            }
                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                <li>
                                                                    <a (click)="removeInquiryItem(i)"
                                                                        class="dropdown-item text-danger"><i
                                                                            class="th th-outline-trash"></i>Delete</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="radio radio-primary">
                                                <input (change)="onChangeInquiryType(i)" type="radio"
                                                    [id]="enumForInquiryType.URGENT_INQUIRY + i"
                                                    [value]="enumForInquiryType.URGENT_INQUIRY"
                                                    formControlName="inquiryType" />
                                                <label [for]="enumForInquiryType.URGENT_INQUIRY + i"></label>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="radio radio-primary" *ngIf="!isOnlyImage">
                                                <input (change)="onChangeInquiryType(i)" type="radio"
                                                    [id]="enumForInquiryType.SO_INQUIRY + i"
                                                    [value]="enumForInquiryType.SO_INQUIRY"
                                                    formControlName="inquiryType" />
                                                <label [for]="enumForInquiryType.SO_INQUIRY + i"></label>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="radio radio-primary">
                                                <input (change)="onChangeInquiryType(i)" type="radio"
                                                    [id]="enumForInquiryType.INQUIRY + i"
                                                    [value]="enumForInquiryType.INQUIRY"
                                                    formControlName="inquiryType" />
                                                <label [for]="enumForInquiryType.INQUIRY + i"></label>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="radio radio-primary">
                                                <input (change)="onChangeInquiryType(i)" type="radio"
                                                    [id]="enumForInquiryType.NIT + i" [value]="enumForInquiryType.NIT"
                                                    formControlName="inquiryType" />
                                                <label [for]="enumForInquiryType.NIT + i"></label>
                                            </div>
                                        </td>
                                        @let isNIT = contact.get('inquiryType').value === enumForInquiryType.NIT;
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select [disableControl]="!isNIT" [id]="'reasonId-'+ i"
                                                    [ngClass]="{'required': contact.get('reasonId').invalid && contact.get('reasonId').touched}"
                                                    placeholder="Select" [multiple]="false" [clearable]="true"
                                                    [items]="allInquiryDropdowns.NITReason" bindLabel="label"
                                                    bindValue="value" appendTo=".theme-ngselect"
                                                    formControlName="reasonId">
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select [id]="'enumsItemsBelong-'+ i"
                                                    [ngClass]="{'required': contact.get('enumsItemsBelong').invalid && contact.get('enumsItemsBelong').touched}"
                                                    placeholder="Select" [multiple]="false" [clearable]="true"
                                                    [items]="allInquiryDropdowns.itemsBelong" bindLabel="label"
                                                    bindValue="value" appendTo=".theme-ngselect"
                                                    formControlName="enumsItemsBelong">
                                                </ng-select>
                                            </div>
                                        </td>
                                        @let isOutOfStock = !contact.get('outOfStock').value;
                                        @let itemId = contact.get('itemId')?.value;
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select [id]="'enumForOrderUnit-'+ i"
                                                    [ngClass]="{'required': contact.get('enumForOrderUnit').invalid && contact.get('enumForOrderUnit').touched}"
                                                    (change)="onOrderUnitChange(i)" placeholder="Select"
                                                    [multiple]="false" [clearable]="false"
                                                    [items]="contact.get('orderUnits').value" bindLabel="label"
                                                    bindValue="value" appendTo=".theme-ngselect"
                                                    formControlName="enumForOrderUnit">
                                                </ng-select>
                                                <div class="message error-message fs-11" *ngIf="!isOutOfStock && contact.get('enumForOrderUnit').value && itemId">
                                                    {{utilsService.validationService.OUT_OF_STOCK}}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div *ngIf="itemId"
                                                class="dropdown-branch-marka dropdown-with-tabs-tables dropdown-no-arrow"
                                                ngbDropdown placement="auto" #dropdown="ngbDropdown" container="body">
                                                <button (click)="openMarkaPopup(i, false)" class="btn btn-link text-primary"
                                                    type="button" ngbDropdownToggle>
                                                    View
                                                </button>
                                                <div cdkDragBoundary="body" ngbDropdownMenu class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdown.isOpen" cdkDrag [cdkDragFreeDragPosition]="dragPosition">
                                                    <div class="card-dropdown-with-tabs-tables">
                                                        <div class="card-header">
                                                            <div class='nav-tabs-outer nav-tabs-style2'>
                                                                <nav>
                                                                    <div class="nav nav-tabs align-items-center d-flex w-100 justify-content-between" role="tablist">
                                                                
                                                                        <!-- Scrollable branch tabs -->
                                                                        <div class="tabs-scroll d-flex">
                                                                            <button *ngFor="let tab of branchMaster; index as b"
                                                                                class="nav-link d-flex flex-column align-items-center px-3" (click)="changeBranchTab(i, b, false)"
                                                                                [class.active]="allMarkaBranchData.activeTab === b" type="button">
                                                                                <div><i class="th th-outline-house-2"></i> {{ tab.branchName }}</div>
                                                                                <small class="text-muted mt-1">{{ tab.totalAvailableQty }}</small>
                                                                            </button>
                                                                            <button (click)="changeToUpcomingTab(i)" [class.active]="isUpcomingTab"
                                                                                class="nav-link d-flex flex-column" type="button">
                                                                                <div><i class="th th-outline-building"></i> Upcoming Qty</div>
                                                                                <!-- <small class="text-muted mt-1">{{ allMarkaBranchData.totalUpcomingQty }}</small> -->
                                                                            </button>
                                                                        </div>
                                                                
                                                                        <!-- Fixed right section -->
                                                                        <div class="fixed-tabs d-flex align-items-center ms-2">
                                                                            <button class="btn btn-primary btn-icon ms-2" cdkDragHandle>
                                                                                <i class="bi bi-arrows-move m-0"></i>
                                                                            </button>
                                                                            <button class="btn btn-outline-primary btn-icon ms-2" (click)="dropdown.close()">
                                                                                <i class="bi bi-x m-0"></i>
                                                                            </button>
                                                                        </div>
                                                                
                                                                    </div>
                                                                </nav>                                                             
                                                            </div>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class='nav-tabs-outer nav-tabs-style2'>
                                                                <div class="tab-content" *ngIf="!isUpcomingTab">
                                                                    <div class="p-3 border rounded bg-light mb-3">
                                                                        <div
                                                                            class="d-flex justify-content-between align-items-start flex-wrap">
                                                                            <!-- Left: Available Carton Counts -->
                                                                            <div class="d-flex flex-wrap gap-4">
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        W. Pickup (CTN | PCS)</div>
                                                                                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalWPCarton || 0 }} | {{ allMarkaBranchData.markaDropDown?.totalWPLoose || 0 }}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Hold (CTN | PCS)</div>
                                                                                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalHoldCartonQty || 0 }} | {{ allMarkaBranchData.markaDropDown?.totalHoldLooseQty || 0 }}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Available Cartons</div>
                                                                                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.availableCartonsQty || 0 }}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Available Loose</div>
                                                                                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.availableLooseQty || 0 }}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Total Available Quantity</div>
                                                                                    <div class="fw-bold">{{ allMarkaBranchData.markaDropDown?.totalAvailableQty || 0 }}</div>
                                                                                </div>
                                                                            </div>

                                                                            <!-- Right: Input -->
                                                                            <div class="d-flex flex-wrap align-items-center gap-3 mt-4">
                                                                                <!-- Search Input -->
                                                                                <div>
                                                                                    <input type="text" class="form-control form-control-sm" style="width: 250px;"
                                                                                        id="searchText-{{ allMarkaBranchData.activeTab }}" placeholder="Search Marka" [(ngModel)]="searchText"
                                                                                        [ngModelOptions]="{ standalone: true }" (input)="filtersMarkaModal('marka', searchText)" />
                                                                                </div>
                                                                            
                                                                                <!-- Warehouse Select -->
                                                                                <div class="form-group theme-ngselect fg-marka">
                                                                                    <ng-select [items]="allMarkaBranchData.warehouse"
                                                                                        bindLabel="warehouseName" bindValue="id" [(ngModel)]="warehouseIdFilter" [ngModelOptions]="{ standalone: true }"
                                                                                        placeholder="Warehouse" [clearable]="false" [multiple]="true" [closeOnSelect]="true"
                                                                                        (change)="filtersMarkaModal('warehouseId', warehouseIdFilter)">
                                                                                        <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                                                                            <div class="ng-value" *ngFor="let item of items | slice:0:1">
                                                                                                <span class="text-truncate ng-value-label max-w-100" [title]="item['warehouseName']">{{ item['warehouseName'] }}</span>
                                                                                                <span class="ng-value-icon right" (click)="clear(item)">×</span>
                                                                                            </div>
                                                                                            <div class="ng-value" *ngIf="items.length > 1">
                                                                                                <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                                                            </div>
                                                                                        </ng-template>
                                                                                    </ng-select>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="tab-pane fade show active">
                                                                        <div class="table-responsive marka-inquiry-scroll p-2">
                                                                            <table
                                                                                class="table-theme table-hover table table-bordered table-sticky">
                                                                                <thead class="border-less">
                                                                                    <tr>
                                                                                        <th>Marka</th>
                                                                                        <th>Warehouse</th>
                                                                                        <!-- <th>Location</th> -->
                                                                                        <th>Carton Qty</th>
                                                                                        <th>Loose Qty</th>
                                                                                        <th>Total Qty</th>
                                                                                        <th>Image Link</th>
                                                                                    </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                
                                                                                    <ng-container *ngFor="let marka of filteredMarkaList; let mi = index">
                                                                                        <ng-container *ngFor="let warehouse of marka.warehouseStocks; let wi = index">
                                                                                            <tr [ngClass]="{'tbl-bg-danger': marka?.expiresInDays < 0}">
                                                                                                <!-- Marka Cell -->
                                                                                                <td *ngIf="wi === 0" [attr.rowspan]="marka.warehouseStocks.length">
                                                                                                    <div class="fw-semibold">{{ marka.marka }}</div>
                                                                                                    <div class="text-muted">Age: {{ marka.age || '-' }}</div>
                                                                                                    <div class="text-muted">Quality: {{marka.tag || '-'}}</div>
                                                                                                </td>
                                                                                    
                                                                                                <!-- Warehouse Name -->
                                                                                                <td>{{ warehouse.warehouseName }}</td>
                                                                                    
                                                                                                <!-- Carton Qty -->
                                                                                                <td>
                                                                                                    <div *ngIf="warehouse.pcsPerCarton">
                                                                                                        {{ warehouse.totalCartons }} x {{ warehouse.pcsPerCarton }} = {{ warehouse.totalCartonQty }}
                                                                                                    </div>
                                                                                                    <div *ngIf="!warehouse.pcsPerCarton">-</div>
                                                                                                </td>
                                                                                    
                                                                                                <!-- Loose Qty -->
                                                                                                <td>{{ warehouse.totalLooseQty || '-' }}</td>
                                                                                    
                                                                                                <!-- Total Qty (No rowspan so alignment stays) -->
                                                                                                <td>{{ warehouse.totalCartonQty }}</td>
                                                                                    
                                                                                                <!-- Image Link -->
                                                                                                <td *ngIf="wi === 0" [attr.rowspan]="marka.warehouseStocks.length" class="tbl-action">
                                                                                                    <div class="tbl-action-group">
                                                                                                        <ng-container *ngIf="marka.grnGroupLinkId; else noLink">
                                                                                                            <span>{{ marka.countOfImage > 5 ? '5+' : marka.countOfImage }}</span>
                                                                                                            <a *ngIf="marka.countOfImage > 0" (click)="utilsService.openStockDetailsInNewTab(marka.grnGroupLinkId)" class="btn-link text-primary">
                                                                                                                Link
                                                                                                            </a>
                                                                                                        </ng-container>
                                                                                                        <ng-template #noLink>
                                                                                                            <span>0</span>
                                                                                                        </ng-template>
                                                                                                        <button (click)="onCopy(marka.grnGroupLinkId)" [copyText]="getGrnGroupLink(marka.grnGroupLinkId)"
                                                                                                            *ngIf="marka.grnGroupLinkId && marka.countOfImage > 0" class="btn btn-xs btn-transparent btn-icon"
                                                                                                            ngbTooltip="Copy Link" placement="bottom" container="body" triggers="hover">
                                                                                                            <i class="th th-outline-copy"></i>
                                                                                                        </button>
                                                                                                    </div>
                                                                                                </td>
                                                                                            </tr>
                                                                                        </ng-container>
                                                                                    
                                                                                        <!-- Marka Total Row -->
                                                                                        <tr class="fw-semibold bg-white border-top">
                                                                                            <td colspan="2">-</td>
                                                                                            <td>{{ marka.sumCartonPcs }}</td>
                                                                                            <td>-</td>
                                                                                            <td>{{ marka.sumTotalQty }}</td>
                                                                                            <td>-</td>
                                                                                        </tr>
                                                                                    </ng-container>
                                                                                    
                                                                                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(filteredMarkaList)">
                                                                                        <td colspan="20" class="text-center">
                                                                                            <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                                                        </td>
                                                                                    </tr>
                                                                                
                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="tab-content" *ngIf="isUpcomingTab">
                                                                    <div class="p-3 border rounded bg-light mb-3">
                                                                        <div
                                                                            class="d-flex justify-content-between align-items-start flex-wrap">
                                                                            <!-- Left: Available Carton Counts -->
                                                                            <div class="d-flex flex-wrap gap-4">
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Upcoming Cartons</div>
                                                                                    <div class="fw-bold">{{allMarkaBranchData.upcomingCartonQty}}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Total Upcoming Qty (Available)
                                                                                    </div>
                                                                                    <div class="fw-bold">{{allMarkaBranchData.totalUpcomingQty}}</div>
                                                                                </div>
                                                                            </div>

                                                                            <!-- Right: Input -->
                                                                            <div>
                                                                                <div>
                                                                                    <input type="text" class="form-control form-control-sm" style="width: 230px;"
                                                                                        id="searchTextUpcoming-{{ allMarkaBranchData.activeTab }}" placeholder="Search Marka" [(ngModel)]="searchText"
                                                                                        [ngModelOptions]="{ standalone: true }" (input)="filterUpcomingQty('marka', searchText)" />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="tab-pane fade show active">
                                                                        <div class="table-responsive marka-inquiry-scroll p-2">
                                                                            <table
                                                                                class="table-theme table-hover table table-bordered table-sticky">
                                                                                <thead class="border-less">
                                                                                    <tr>
                                                                                        <th>Marka</th>
                                                                                        <!-- <th>Stage</th> -->
                                                                                        <th>Expected Delivery Date</th>
                                                                                        <th>Carton Qty</th>
                                                                                        <th>Total Qty</th>
                                                                                        <!-- <th>Image Link</th> -->
                                                                                    </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                    @for(item of filteredUpcomingQtyList; let i = $index; track $index) {
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div class="fw-semibold">
                                                                                                {{item.marka}}
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- <td>
                                                                                            <div class="fw-semibold">
                                                                                                {{item.stage['label']}}
                                                                                            </div>
                                                                                            <div class="text-muted">
                                                                                                {{item.stageDate ? (item.stageDate | date: 'dd/MM/YYYY h:mm a') : '-'}}
                                                                                            </div>
                                                                                            <div class="text-muted" *ngIf="item.containerName">
                                                                                                {{item.containerName}}
                                                                                            </div>
                                                                                        </td> -->
                                                                                        <td>
                                                                                            <span>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</span>
                                                                                        </td>
                                                                                        <td>
                                                                                            @if(item.cartonPic && item.cartonQty) {
                                                                                            <span>{{item.cartonQty}} x {{item.cartonPic}} = {{item.totalQty}}</span>
                                                                                            } @else {
                                                                                            <span>-</span>
                                                                                            }
                                                                                        </td>
                                                                                        <td>
                                                                                            <span>{{item.totalQty ? item.totalQty : 0}}</span>
                                                                                        </td>
                                                                                    </tr>
                                                                                    } @empty {
                                                                                    <tr
                                                                                        *ngIf="utilsService.isEmptyObjectOrNullUndefined(allMarkaBranchData?.markaDropDown?.markaStocks)">
                                                                                        <td colspan="20"
                                                                                            class="text-center">
                                                                                            <span
                                                                                                class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                                                        </td>
                                                                                    </tr>
                                                                                    }

                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="card-footer" *ngIf="false">
                                                            <div class="button-group">
                                                                <button (click)="dropdown.close()" type="button"
                                                                    class="btn btn-sm btn-primary btn-icon-text">
                                                                    <i class="th th-outline-tick-circle"></i>
                                                                    Save
                                                                </button>
                                                                <button (click)="dropdown.close()" type="button"
                                                                    class="btn btn-sm btn-outline-white btn-icon-text">
                                                                    <i class="th th-outline-close-circle"></i>
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-100"
                                                [ngClass]="{'form-error': contact.get('inquiryQuantity').invalid && contact.get('inquiryQuantity').touched}">
                                                <input [id]="'inquiryQuantity-'+ i" (input)="onChangeInquiryQty(i)"
                                                    type="text" class="form-control" placeholder="Enter"
                                                    formControlName="inquiryQuantity"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    mask="separator.0" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group form-group-100">
                                                <input [id]="'piecePerCarton-'+ i" (input)="onChangeInquiryQty(i)"
                                                    type="text" class="form-control" placeholder="Enter"
                                                    formControlName="piecePerCarton"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    mask="separator.0" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{contact.get('totalPiece')?.value || 0}}</td>
                                        <td>
                                            @if(isOnlyImage) {
                                            <div class="form-group form-group-100" [ngClass]="{'form-error': (contact.get('rate').invalid && contact.get('rate').touched)}">
                                                <input [id]="'rate-'+ i" (input)="onChangeInquiryQty(i)" type="text"
                                                    class="form-control" placeholder="Enter" formControlName="rate"
                                                    [maxlength]="utilsService.validationService.MAX_10"
                                                    mask="separator.0" thousandSeparator="">
                                            </div>
                                            } @else {
                                                <span>{{contact.get('rate')?.value || 0}}</span>
                                                <div class="form-group theme-ngselect form-border-less">
                                                    <div class="message error-message fs-11"
                                                        *ngIf="contact.get('isRateNotFound')?.value && contact.get('enumForOrderUnit')?.touched">
                                                        {{utilsService.validationService.INQ_NO_RATE}}
                                                    </div>
                                                </div>
                                            }
                                        </td>
                                        <td>{{contact.get('totalAmount')?.value || 0}}</td>
                                        <td>
                                            <!-- @let salesPriceLessThanRate = contact.hasError('salesPriceLessThanRate') && contact.get('salesExecutiveOfferedPrice').touched; -->
                                            <div class="form-group form-group-100"
                                                [ngClass]="{'form-error': (contact.get('salesExecutiveOfferedPrice').invalid && contact.get('salesExecutiveOfferedPrice').touched)}">
                                                <input [id]="'salesExecutiveOfferedPrice-'+ i" type="text"
                                                    class="form-control" placeholder="Enter"
                                                    formControlName="salesExecutiveOfferedPrice"
                                                    [maxlength]="utilsService.validationService.MAX_15"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        <td>{{contact.get('salesManagerPrice')?.value || 0}}</td>
                                        <td>
                                            <div class="form-group form-group-100"
                                                [ngClass]="{'form-error': contact.get('customerRequestedPrice').invalid && contact.get('customerRequestedPrice').touched}">
                                                <input [id]="'customerRequestedPrice-'+ i" type="text"
                                                    class="form-control" placeholder="Enter"
                                                    formControlName="customerRequestedPrice"
                                                    [maxlength]="utilsService.validationService.MAX_15"
                                                    mask="separator.2" thousandSeparator="">
                                            </div>
                                        </td>
                                        @let lastSalesInfo = inquiryItems.at(i).get('lastSalesInfo')?.value;
                                        <td>
                                            <div class="dropdown-branch-marka-sm dropdown-with-tables dropdown-no-arrow" ngbDropdown placement="auto"
                                            #dropdown="ngbDropdown" container="body">
                                            @if(lastSalesInfo?.length > 0) {
                                                <button class="btn btn-link" type="button" ngbDropdownToggle>
                                                    Last 3 Sales
                                                </button>
                                            } @else {
                                                <span>-</span>
                                            }
                                            <div ngbDropdownMenu class="dropdown-last-3" dropdownClass="dropdown-last-3" *ngIf="dropdown.isOpen">
                                                <app-last-three [lastThreeInfo]="lastSalesInfo" [dropdown]="dropdown" />
                                            </div>
                                        </div>
                                        </td>
                                        <td>
                                            <!-- <span class="w-100 d-block">Ava.: 50-10 = 40</span>
                                            <span class="w-100 d-block">W. Pickup: 25</span>
                                            <span class="w-100 d-block">Hold: 10</span> -->
                                        </td>
                                        <td>
                                            <!-- <span class="w-100 d-block">Ava.: 50-10 = 40</span>
                                            <span class="w-100 d-block">W. Pickup: 25</span>
                                            <span class="w-100 d-block">Hold: 10</span> -->
                                        </td>
                                        <td>
                                            <div *ngIf="itemId" class="dropdown-branch-marka dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown
                                                placement="auto" #dropdownA="ngbDropdown" container="body">
                                                <button (click)="openMarkaPopup(i, true)" class="btn btn-link text-primary" type="button" ngbDropdownToggle>
                                                    Upcoming
                                                </button>
                                                <div cdkDragBoundary="body" ngbDropdownMenu class="dropdown-menu-marka" dropdownClass="dropdown-menu-marka" *ngIf="dropdownA.isOpen" cdkDrag [cdkDragFreeDragPosition]="dragPosition">
                                                    <div class="card-dropdown-with-tabs-tables">
                                                        <div class="card-header">
                                                            <div class="nav-tabs-outer nav-tabs-style2">
                                                                <nav>
                                                                    <div class="nav nav-tabs align-items-center d-flex w-100 justify-content-between" role="tablist">
                                                                        <div class="tabs-scroll flex-grow-1">
                                                                            <button (click)="changeToUpcomingTab(i)" [class.active]="isUpcomingTab"
                                                                                class="nav-link d-flex flex-column align-items-center px-3" type="button">
                                                                                <div><i class="th th-outline-building"></i> Upcoming Qty</div>
                                                                                <small class="text-muted mt-1">{{ allMarkaBranchData.totalUpcomingQty }}</small>
                                                                            </button>
                                                                        </div>
                                                                        <div class="fixed-tabs d-flex align-items-center ms-2">
                                                                            <button class="btn btn-primary btn-icon ms-2" cdkDragHandle>
                                                                                <i class="bi bi-arrows-move m-0"></i>
                                                                            </button>
                                                                            <button class="btn btn-outline-primary btn-icon ms-2" (click)="dropdownA.close()">
                                                                                <i class="bi bi-x m-0"></i>
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class='nav-tabs-outer nav-tabs-style2'>
                                                                <div class="tab-content" *ngIf="isUpcomingTab">
                                                                    <div class="p-3 border rounded bg-light mb-3">
                                                                        <div class="d-flex justify-content-between align-items-start flex-wrap">
                                                                            <!-- Left: Available Carton Counts -->
                                                                            <div class="d-flex flex-wrap gap-4">
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Upcoming Cartons</div>
                                                                                    <div class="fw-bold">{{allMarkaBranchData.upcomingCartonQty}}</div>
                                                                                </div>
                                                                                <div>
                                                                                    <div class="text-muted small">
                                                                                        Total Upcoming Qty (Available)
                                                                                    </div>
                                                                                    <div class="fw-bold">{{allMarkaBranchData.totalUpcomingQty}}</div>
                                                                                </div>
                                                                            </div>
                                            
                                                                            <!-- Right: Input -->
                                                                            <div>
                                                                                <div>
                                                                                    <input type="text" class="form-control form-control-sm" style="width: 230px;"
                                                                                        id="searchTextUpcoming-{{ allMarkaBranchData.activeTab }}"
                                                                                        placeholder="Search Marka" [(ngModel)]="searchText"
                                                                                        [ngModelOptions]="{ standalone: true }"
                                                                                        (input)="filterUpcomingQty('marka', searchText)" />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                            
                                                                    <div class="tab-pane fade show active">
                                                                        <div class="table-responsive p-2 marka-inquiry-scroll">
                                                                            <table class="table-theme table-hover table table-bordered table-sticky">
                                                                                <thead class="border-less">
                                                                                    <tr>
                                                                                        <th>Marka</th>
                                                                                        <!-- <th>Stage</th> -->
                                                                                        <th>Expected Delivery Date</th>
                                                                                        <th>Carton Qty</th>
                                                                                        <th>Total Qty</th>
                                                                                        <!-- <th>Image Link</th> -->
                                                                                    </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                    @for(item of filteredUpcomingQtyList; let i = $index; track $index) {
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div class="fw-semibold">
                                                                                                {{item.marka}}
                                                                                            </div>
                                                                                        </td>
                                                                                        <!-- <td>
                                                                                            <div class="fw-semibold">
                                                                                                {{item.stage['label']}}
                                                                                            </div>
                                                                                            <div class="text-muted">
                                                                                                {{item.stageDate ? (item.stageDate | date: 'dd/MM/YYYY h:mm a') : '-'}}
                                                                                            </div>
                                                                                            <div class="text-muted" *ngIf="item.containerName">
                                                                                                {{item.containerName}}
                                                                                            </div>
                                                                                        </td> -->
                                                                                        <td>
                                                                                            <span>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</span>
                                                                                        </td>
                                                                                        <td>
                                                                                            @if(item.cartonPic && item.cartonQty) {
                                                                                            <span>{{item.cartonQty}} x {{item.cartonPic}} = {{item.totalQty}}</span>
                                                                                            } @else {
                                                                                            <span>-</span>
                                                                                            }
                                                                                        </td>
                                                                                        <td>
                                                                                            <span>{{item.totalQty ? item.totalQty : 0}}</span>
                                                                                        </td>
                                                                                    </tr>
                                                                                    } @empty {
                                                                                    <tr
                                                                                        *ngIf="utilsService.isEmptyObjectOrNullUndefined(allMarkaBranchData?.markaDropDown?.markaStocks)">
                                                                                        <td colspan="20" class="text-center">
                                                                                            <span
                                                                                                class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                                                        </td>
                                                                                    </tr>
                                                                                    }
                                            
                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="tbl-form-group">
                                            <div class="form-group theme-ngselect form-border-less">
                                                <ng-select [id]="'hsnCodeId-'+ i" [disableControl]="isOnlyImage"
                                                    (change)="onHsnCodeChange(i)" placeholder="Select"
                                                    [multiple]="false" [clearable]="true"
                                                    [items]="contact.get('hsnDropdown')?.value" bindLabel="hsnCode"
                                                    bindValue="id" appendTo=".theme-ngselect"
                                                    formControlName="hsnCodeId">
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td>{{contact.get('taxName')?.value || '-'}}</td>
                                        <td>{{contact.get('gstRate')?.value || 0}}</td>
                                        <td>{{contact.get('gstAmount')?.value || 0}}</td>
                                        <td>{{contact.get('totalAmountWithGst')?.value || 0}}</td>
                                        <td>
                                            <div class="form-group form-group-200">
                                                <input type="text" class="form-control" placeholder="Enter"
                                                    formControlName="note"
                                                    [maxlength]="utilsService.validationService.MAX_100">
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="tbl-add-new">
                                        <td colspan="100">
                                            <div class="d-flex gap-2">
                                                <button (click)="addInquiryItem()"
                                                    class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                        class="th-bold-add-circle"></i>
                                                    Add New Row
                                                </button>
                                                <button (click)="openWebcamModal(null, null)"
                                                    class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                        class="th-bold-add-circle"></i>
                                                    Take Photo
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>
                <div class='bottombar-left'>
                    <button (click)="onSave(false)" [disabled]="!isAnyItemInquiryTypeSO()" type="button"
                        class="btn btn-primary btn-icon-text btn-sm"> <i class="th th-outline-tick-circle"></i>Continue to Sale</button>
                    <!-- <button type="button" class="btn btn-secondary btn-icon-text btn-sm"
                        [routerLink]="['/users/sales/sales-orders/new-proforma-invoice']"> <i
                            class="th th-outline-tick-circle"></i>Create Proforma Invoice</button> -->
                    <button (click)="onSave(true)" type="button" class="btn btn-outline-primary btn-icon-text btn-sm">
                        <i class="th th-outline-document-text"></i>Save As Inquiry</button>
                </div>
                <div class='bottombar-right'>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Associated Details Modal Start                       -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="associatedInqItemModal" tabindex="-1"
    aria-labelledby="associatedInqItemModalLabel">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Associated Items</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="card card-theme card-table-sticky" *ngIf="inquiryAssociatedItemsFlag">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th>
                                            <div class="d-flex align-items-center gap-2">
                                                Item Details
                                            </div>
                                        </th>
                                        <th>HSN Code</th>
                                        <th>Available Carton</th>
                                        <th>Total Carton Qty</th>
                                        <th>Loose Qty</th>
                                        <th>Total Available Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(item of inquiryAssociatedItems; track item.id; let i = $index) {
                                    <tr>
                                        <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                    <div class="tbl-user-image">
                                                        <img [src]="item.image ? (utilsService.imgPath + item.image) : ''"
                                                            alt="valamji">
                                                    </div>
                                                    <div class="tbl-user-text-action">
                                                        <div class="tbl-user-text">
                                                            <p>{{item.displayName}}</p>
                                                            <span>{{item.skuId}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{item.hsnCode}}</td>
                                        <td>
                                            <span class="text-center">
                                                {{item.stock.availableCarton ? item.stock.availableCarton : '-'}}
                                            </span>
                                        </td>
                                        <td>{{item.stock.totalCartonQty}}</td>
                                        <td>{{item.stock.looseQty}}</td>
                                        <td>{{item.stock.totalAvailableCartonQty}}</td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-end">
                    <button data-bs-dismiss="modal" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Done</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Associated Details Modal End                        -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Customer Forms Modal Start                  -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="addCustomerLeadModalI" tabindex="-1"
    aria-labelledby="addCustomerLeadModalILabel">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <app-customer-lead-modal [formGroup]="formGroupLead" [countryCodeDropdown]="allInquiryDropdowns.Country"
            [isAdd]="true" (onSave)="onSaveCustomerLead()" [isFromInquiry]="true"/>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Customer Forms Modal End                   -->
<!-- ----------------------------------------------------------------------- -->


<!-- WEBCAM MODAL -->
<div class="modal modal-theme fade" id="webcamModal" tabindex="-1" aria-labelledby="webcamModalLabel">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content" *ngIf="isWebModalOpen">
            <div class="modal-header">
                <h5 class="modal-title">Add New Product Photos</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="attachments-wrapper">
                            <div class="attachments-container h-100">
                                <div class="attachments-content text-center">
                                    <button class="btn btn-attachments mb-2">
                                        <i class="bi bi-upload"></i>
                                    </button>
                                    <p class="mb-0">
                                        Drag and Drop images here or <span class="text-primary">Choose file</span>
                                    </p>
                                </div>
                                <input #docW type="file" [multiple]="true"
                                    accept="image/x-png,image/jpeg,image/jpg, image/jfif"
                                    (change)="onSelectAttachments($event, true); docW.value = ''" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex justify-content-center flex-column align-items-center">
                            <webcam (initError)="handleInitError($event)" [height]="250" [width]="350"
                                (imageCapture)="takePhoto($event)" [trigger]="triggerObservable"></webcam>
                            <div class="mt-2">
                                <button type="button" class="btn btn-primary btn-icon-text" (click)="triggerSnapshot()">
                                    <i class="th th-bold-camera"></i> Take Photo</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="attachments-upload-grid-container attachments-upload-grid-container2">
                        <div class="attachments-upload-row row m-0">
                            @for (item of webcamImages.get(); track $index; let i = $index) {
                            <div class="attachments-upload-col col-md-3 mb-3">
                                <div class="card-attachments-upload position-relative">
                                    <div class="attachments-image text-center">
                                        @if (utilsService.isImage(item.originalName)) {
                                        <img loading="lazy"
                                            (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                            [src]="item.formattedName ? (item.file ? item.formattedName : utilsService.imgPath + item.formattedName) : null"
                                            alt="valamji" />
                                        } @else if (utilsService.isMedia(item.originalName)) {
                                        <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                            src="assets/images/files/file-video.svg" alt="valamji" />
                                        } @else if (utilsService.isExcel(item.originalName)) {
                                        <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                            src="assets/images/files/file-excel.svg" alt="valamji" />
                                        } @else if (utilsService.isDocument(item.originalName)) {
                                        <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                            src="assets/images/files/file-pdf.svg" alt="valamji" />
                                        }
                                    </div>
                                    <div class="attachments-text text-center" [ngbTooltip]="item.originalName"
                                        placement="bottom" container="body" triggers="hover">
                                        <h6 class="file-name">{{ item.originalName }}</h6>
                                    </div>
                                    <button (click)="removeAttachment(i, true)"
                                        class="btn-close position-absolute top-0 end-0 m-1" aria-label="Close">
                                        <i class="th th-close"></i>
                                    </button>
                                </div>
                            </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn justify-content-start">
                    <button [disabled]="webcamImagesEmpty()" (click)="onSaveWebcamImages()" type="button"
                        class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- WEBCAM MODAL -->


<!-- ----------------------------------------------------------------------- -->
<!--                           Error Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="transport-modal modal modal-theme fade" id="hsnFileErrorInquiryModal" tabindex="-1"
    aria-labelledby="hsnFileErrorInquiryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Error Logs ({{errorsList?.length ? errorsList?.length :0}})</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th>Error description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of errorsList; index as i">
                                <td class="tbl-description">
                                    <div>{{item}}</div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Error Modal End                            -->
<!-- ----------------------------------------------------------------------- -->
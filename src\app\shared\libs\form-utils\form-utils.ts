import { AbstractControl, FormArray, FormControl, FormGroup } from "@angular/forms";

export function setDisableField<T, K extends keyof T>(disable: boolean, form: FormGroup, fieldName: K, emitEvent = false) {
  disable ? form.get(fieldName as string)!.disable({ emitEvent }) : form.get(fieldName as string)!.enable({ emitEvent });
}

export function setDisableFields<T, K extends keyof T>(disable: boolean, form: FormGroup, fieldNames: K[], emitEvent = false) {
  for (const fieldName of fieldNames) {
    this.setDisableField(disable, form, fieldName, emitEvent);
  }
}

export function setDisableAllFieldsExcept<T, K extends keyof T>(disable: boolean, form: FormGroup, excludedFieldNames: K[], emitEvent = false) {
  for (const control in form.controls) {
    const fieldName = control as string as K;
    if (excludedFieldNames.includes(fieldName)) {
      this.setDisableField(!disable, form, fieldName, emitEvent);
    } else {
      this.setDisableField(disable, form, fieldName, emitEvent);
    }
  }
}

export function formValue(value: string, formGroup: FormGroup): FormControl {
  return formGroup.get(value) as FormControl;
}

export type FormControlsOf<T> = {
  [K in keyof T]: T[K] extends Array<infer U> ? FormArray<FormGroup<FormControlsOf<U>>> : FormControl<T[K]>
};

export function resetValidators(control: AbstractControl | null) {
  if (!control) return;
  control.clearValidators();
  control.updateValueAndValidity();
}

export function disableFieldUtil(keys: string[], form: FormGroup, disable: boolean) {
  for (const key of keys) {
    disable ? form.get(key)?.disable() : form.get(key)?.enable();
    form.get(key)?.updateValueAndValidity();
  }
}
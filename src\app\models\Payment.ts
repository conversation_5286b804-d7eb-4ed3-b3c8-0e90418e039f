export type PaymentSave = Omit<PaymentDTO, 'dueAmount' | 'credit' | 'paymentImgs'>;

export interface PaymentDTO {
    id: number;
    supplierId: number;
    paymentDate: string;
    paymentAmountRMB: number;
    conversionRate: number;
    paidBy: string;
    notes: string;
    bankGroupId: number;
    paymentTypeId: number;
    paymentImgs: { id: number, file: File, originalName: string, formattedName: string }[];
    deletedDocumentID: number[];
    dueAmount: number;
    credit: number;
    isExpand: boolean;
    supplierName: string;
    supplierShortCode: string;
    phone: string;
    debitAmount: number;
    creditAmount: number;
    payments: PaymentChild[];
    paymentItems: PaymentItems[];
    documents: { id: number, file: File, originalName: string, formattedName: string }[];
    paymentStatus: string
}

export interface PaymentChild {
    id: number;
    paymentDate: string;
    paymentAmountRMB: number;
    conversionRate: number;
    paymentAmountINR: number;
    paymentItems: PaymentItems[];
    bankName: string;
    isExpand: boolean;
    paymentId: number;
    paidBy : string;
    paymentStatus: {label: string, value: string}
}

 export interface PaymentItems {
    amount: number;
    conversionRate: number;
    amountINR: number;
    rcId: string;
 }
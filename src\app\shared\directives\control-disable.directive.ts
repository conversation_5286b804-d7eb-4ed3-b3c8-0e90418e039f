import { Directive, Input, OnChanges, SimpleChanges } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
    selector: '[disableControl]',
})

export class DisableControlDirective implements OnChanges {

    @Input() disableControl: boolean = false;

    constructor(private ngControl: NgControl) { }

    ngOnChanges(changes: SimpleChanges): void {
        const control = this.ngControl?.control;
        if (!control) return;

        if (changes['disableControl']) {
            if (this.disableControl) {
                control.disable({ emitEvent: false });
            } else {
                control.enable({ emitEvent: false });
            }
        }
    }
}

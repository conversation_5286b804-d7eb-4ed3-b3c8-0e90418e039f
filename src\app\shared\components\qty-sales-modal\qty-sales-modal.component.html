<div class="card-dropdown-with-tabs-tables">
  <div class="card-header">
    <div class='nav-tabs-outer nav-tabs-style2'>
      <nav>
        <div class="nav nav-tabs align-items-center d-flex w-100 justify-content-between" role="tablist">
          <div class="tabs-scroll d-flex">
            <button *ngFor="let tab of saveResMarkaWise; index as b"
              class="nav-link d-flex flex-column align-items-center px-3" (click)="onOpenQty(b, tab)"
              [class.active]="activeTabQtyTab === b" type="button">
              <div><i class="th th-outline-house-2"></i> {{ tab.branchName }}</div>
            </button>
          </div>
          <div class="fixed-tabs d-flex align-items-center ms-2">
            <button (click)="dropdown.close()" class="btn btn-xs btn-transparent btn-icon text-black column-filter-dropdown-close">
              <i class="th th-close m-0"></i>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
  <div class="card-body">
    <div class='nav-tabs-outer nav-tabs-style2'>
      <div class="tab-content">
        <div class="fade show active">
          <div class="table-responsive p-2 marka-inquiry-scroll">
            @if(!isUpcomingTab) {
              @if (!inputBooleansObj?.isMarkaPresent && inputBooleansObj?.isLoose) {
              <table class="table-theme table-hover table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>Warehouse</th>
                    <th>Carton/Loose</th>
                  </tr>
                </thead>
                <tbody *ngIf="saveResMarkaWise?.length > 0">
                  <ng-container *ngFor="let markaStock of saveResMarkaWise[activeTabQtyTab]?.markaStocks">
                    <ng-container *ngFor="let wh of markaStock.warehouseStocks; let wIndex = index">
                      <tr>
                        <td>{{ wh.warehouseName }}</td>
                        <td>
                          @if(wh.totalCartons > 0) {
                            <span class="w-100 d-block">
                              {{ wh.totalCartons }} × {{ wh.pcsPerCarton }} = {{ wh.totalCartonQty}}
                            </span>
                          }
                          @if(wh.totalLooseQty > 0) {
                            <span class="w-100 d-block" >
                              LOOSE - {{ wh.totalLooseQty }}
                            </span>
                          }
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
                <tfoot>
                  <tr class="tbl-total">
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty}}</td>
                  </tr>
                </tfoot>
              </table>
              }
              @if (!inputBooleansObj?.isMarkaPresent && !inputBooleansObj?.isLoose) {
              <table class="table-theme table-hover table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>Carton x PCS = Total</th>
                    <th>Warehouse</th>
                    <th>Carton</th>
                  </tr>
                </thead>
                <tbody *ngIf="saveResMarkaWise?.length > 0">
                  <ng-container *ngFor="let markaStock of saveResMarkaWise[activeTabQtyTab].markaStocks">
                    <ng-container *ngFor="let wh of markaStock.warehouseStocks; let wIndex = index">
                      <tr>
                        <!-- Only show first column on very first row for this markaStock -->
                        <td *ngIf="wIndex === 0" [attr.rowspan]="markaStock?.warehouseStocks?.length">
                          {{ markaStock.totalCartons }} × {{ markaStock.pcsPerCarton }} =
                          {{ markaStock.totalQty }}
                        </td>
  
                        <td>{{ wh.warehouseName }}</td>
                        <td>{{ wh.totalCartons }} × {{ wh.pcsPerCarton }} = {{ wh.totalQty }}</td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
                <tfoot>
                  <tr class="tbl-total">
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.availableCartonsQty}}</td>
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty}}</td>
                  </tr>
                </tfoot>
              </table>
              }
              @if (inputBooleansObj?.isMarkaPresent && !inputBooleansObj?.isLoose) {
              <table class="table-theme table-hover table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>Marka</th>
                    <th>Carton x PCS = Total</th>
                    <th>Warehouse</th>
                    <th>Carton/Loose</th>
                  </tr>
                </thead>
                <tbody *ngIf="saveResMarkaWise?.length > 0">
                  <ng-container *ngFor="let markaStock of saveResMarkaWise[activeTabQtyTab]?.markaStocks">
                    <ng-container *ngFor="let wh of markaStock.warehouseStocks; let wIndex = index">
                      <tr>
                        <td *ngIf="wIndex === 0" [attr.rowspan]="markaStock?.warehouseStocks?.length">
                          <strong>{{ markaStock.marka }}</strong>
                        </td>
                        <td *ngIf="wIndex === 0" [attr.rowspan]="markaStock?.warehouseStocks?.length">
                          {{ markaStock.totalCartons }} × {{ markaStock.pcsPerCarton }} =
                          {{ markaStock.totalQty }}
                        </td>
                        <td>{{ wh.warehouseName }}</td>
                         <td>
                          @if(wh.totalCartons > 0) {
                            <span class="w-100 d-block">
                              {{ wh.totalCartons }} × {{ wh.pcsPerCarton }} = {{ wh.totalCartonQty}}
                            </span>
                          }
                          @if(wh.totalLooseQty > 0) {
                            <span class="w-100 d-block" >
                              LOOSE - {{ wh.totalLooseQty }}
                            </span>
                          }
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
                <tfoot>
                  <tr class="tbl-total">
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.availableCartonsQty}}</td>
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty}}</td>
                  </tr>
                </tfoot>
              </table>
              }
              @if (inputBooleansObj?.isMarkaPresent && inputBooleansObj?.isLoose) {
              <table class="table-theme table-hover table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>Marka</th>
                    <th>Carton x PCS = Total</th>
                    <th>Warehouse</th>
                    <th>Carton/Loose</th>
                  </tr>
                </thead>
                <tbody *ngIf="saveResMarkaWise?.length > 0">
                  <ng-container *ngFor="let markaStock of saveResMarkaWise[activeTabQtyTab]?.markaStocks">
                    <ng-container *ngFor="let wh of markaStock.warehouseStocks; let wIndex = index">
                      <tr>
                        <td *ngIf="wIndex === 0" [attr.rowspan]="markaStock?.warehouseStocks?.length">
                          <strong>{{ markaStock.marka }}</strong>
                        </td>
                        <td *ngIf="wIndex === 0" [attr.rowspan]="markaStock?.warehouseStocks?.length">
                            @if(markaStock.totalCartons > 0) {
                              {{ markaStock.totalCartons }} × {{ markaStock.pcsPerCarton }} =
                              {{ markaStock.totalCartonQty }}
                            }
                            @if(markaStock.totalLooseQty > 0) {
                              <span class="w-100 d-block" >
                                LOOSE - {{ markaStock.totalLooseQty }}
                              </span>
                            }
                        </td>
                        <td>{{ wh.warehouseName }}</td>
                        <td>
                          @if(wh.totalCartons > 0) {
                            <span class="w-100 d-block">
                              {{ wh.totalCartons }} × {{ wh.pcsPerCarton }} = {{ wh.totalCartonQty}}
                            </span>
                          }
                          @if(wh.totalLooseQty > 0) {
                            <span class="w-100 d-block" >
                              LOOSE - {{ wh.totalLooseQty }}
                            </span>
                          }
                        </td>
                      </tr>
                    </ng-container>
                  </ng-container>
                </tbody>
                <tfoot>
                  <tr class="tbl-total">
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.availableCartonsQty}}</td>
                    <td>-</td>
                    <td>{{saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty}}</td>
                  </tr>
                </tfoot>
              </table>
              }
            }
            @if(isUpcomingTab) {
              <table class="table-theme table-hover table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>Marka</th>
                    <th>Expected Delivery Date</th>
                    <th>C x Qty = Total</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let markaStock of saveResMarkaWise[activeTabQtyTab]?.markaStocks">
                    <td><strong>{{ markaStock.marka }}</strong></td>
                    <td>{{ (markaStock?.expectedDeliveryDate ? (markaStock.expectedDeliveryDate | date: 'dd/MM/yyyy') : '-') }}</td>
                    <td>{{ markaStock.totalCartons }} × {{ markaStock.pcsPerCarton }} = {{ markaStock.totalQty }}</td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="tbl-total">
                    <td>-</td>
                    <td>-</td>
                    <td>
                      {{saveResMarkaWise[activeTabQtyTab]?.availableCartonsQty ? saveResMarkaWise[activeTabQtyTab]?.availableCartonsQty :
                      0}} Carton,
                      Total Qty: {{saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty ?
                      saveResMarkaWise[activeTabQtyTab]?.totalAvailableQty : 0}}
                    </td>
                  </tr>
                </tfoot>
              </table>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
import { Component, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { UtilsService } from '@service/utils.service';
import saveAs from 'file-saver';
declare var window: any;

@Component({
  selector: 'app-print-qr-code',
  templateUrl: './print-qr-code.component.html',
  styleUrls: ['./print-qr-code.component.css']
})
export class PrintQrCodeComponent implements OnInit {

  utilsService = inject(UtilsService);
  formGroup: FormGroup;

  prinQRPageModal: any;

  constructor(private fb: FormBuilder) { }

  ngOnInit() {
    this.initForm();

    this.prinQRPageModal = new window.bootstrap.Modal(
      document.getElementById('prinQRPageModal')
    );
  }

  initForm = () => {
    this.formGroup = this.fb.group({
      noOfQrCode: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]),],
    })
  }

  openQRModal = () => {
    this.prinQRPageModal.show();
  }

  downloadQR() {

    if (this.formGroup.invalid) {
      this.formGroup.markAllAsTouched();
      return;
    }

    this.utilsService.exportReportGetApi(this.utilsService.serverVariableService.PRINT_QR_CODE + `?noOfQrCode=${this.formGroup.get('noOfQrCode').value}`).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/pdf' }), 'QR Code');
      this.prinQRPageModal.hide();
      this.formGroup.reset();
    });
  }

}

import { deserializeAs, serializeAs } from 'cerialize';

export class ContainerExpense {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @deserializeAs('expenseID')
    private _expenseID: number;

    @deserializeAs('temp_date')
    private _temp_date: any;

    @serializeAs('expenseDate')
    @deserializeAs('expenseDate')
    private _expenseDate: string;

    @serializeAs('containerID')
    @deserializeAs('containerID')
    private _containerID: number;

    @serializeAs('notes')
    @deserializeAs('notes')
    private _notes: string;

    @serializeAs('importerID')
    @deserializeAs('importerID')
    private _importerID: number;

    @serializeAs('chaShippingTypes')
    @deserializeAs('chaShippingTypes')
    private _chaShippingTypes: string;

    @serializeAs('ourShippingTypes')
    @deserializeAs('ourShippingTypes')
    private _ourShippingTypes: string;

    @serializeAs('expenseCarringType')
    @deserializeAs('expenseCarringType')
    private _expenseCarringType: string;

    @serializeAs('ourPrice')
    @deserializeAs('ourPrice')
    private _ourPrice: number;

    @serializeAs('chaPrice')
    @deserializeAs('chaPrice')
    private _chaPrice: number;

    @serializeAs('otherPrice')
    @deserializeAs('otherPrice')
    private _otherPrice: number;

    @serializeAs('ourAmount')
    @deserializeAs('ourAmount')
    private _ourAmount: number;

    @serializeAs('expenseTypeItems')
    @deserializeAs('expenseTypeItems')
    private _expenseTypeItems: ContainerExpenseList[];

    @serializeAs('deletedTypeID')
    @deserializeAs('deletedTypeID')
    private _deletedTypeID: number[];

    @deserializeAs('docs')
    private _docs: any[];

    @serializeAs('deletedDocumentID')
    @deserializeAs('deletedDocumentID')
    private _deletedDocumentID: number[];

    @deserializeAs('documents')
    private _documents: any[];

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @deserializeAs('vehicleNo')
    private _vehicleNo: string;

    @deserializeAs('containersName')
    private _containersName: string;

    @deserializeAs('totalExpenseAmount')
    private _totalExpenseAmount: number;

    @deserializeAs('isCompleted')
    private _isCompleted: boolean;

    @serializeAs('chaRate')
    @deserializeAs('chaRate')
    private _chaRate: number;

    @serializeAs('otherRate')
    @deserializeAs('otherRate')
    private _otherRate: number;

    @serializeAs('otherAmount')
    @deserializeAs('otherAmount')
    private _otherAmount: number;

    @serializeAs('chaAmount')
    @deserializeAs('chaAmount')
    private _chaAmount: number;

    @deserializeAs('importType')
    private _importType: string;

    @serializeAs('isApprove')
    @deserializeAs('isApprove')
    private _isApprove: boolean;

    @deserializeAs('expenseStatus')
    private _expenseStatus: any;

    @serializeAs('status')
    @deserializeAs('status')
    private _status: string;

    @deserializeAs('isRollBack')
    private _isRollBack: boolean;

    constructor() {
        this.expenseTypeItems = [];
        this.deletedTypeID = [];
        this.docs = [];
        this.deletedDocumentID = [];
        this.documents = [];
        this.isSelected = false;
        this.isCompleted = false;
        this.isApprove = false;
        this.isRollBack = false;
    }

    /**
     * Getter isRollBack
     * @return {boolean}
     */
	public get isRollBack(): boolean {
		return this._isRollBack;
	}

    /**
     * Setter isRollBack
     * @param {boolean} value
     */
	public set isRollBack(value: boolean) {
		this._isRollBack = value;
	}

    /**
     * Getter status
     * @return {string}
     */
	public get status(): string {
		return this._status;
	}

    /**
     * Setter status
     * @param {string} value
     */
	public set status(value: string) {
		this._status = value;
	}

    /**
     * Getter expenseStatus
     * @return {any}
     */
	public get expenseStatus(): any {
		return this._expenseStatus;
	}

    /**
     * Setter expenseStatus
     * @param {any} value
     */
	public set expenseStatus(value: any) {
		this._expenseStatus = value;
	}


    /**
     * Getter isApprove
     * @return {boolean}
     */
	public get isApprove(): boolean {
		return this._isApprove;
	}

    /**
     * Setter isApprove
     * @param {boolean} value
     */
	public set isApprove(value: boolean) {
		this._isApprove = value;
	}

    /**
     * Getter importType
     * @return {string}
     */
	public get importType(): string {
		return this._importType;
	}

    /**
     * Setter importType
     * @param {string} value
     */
	public set importType(value: string) {
		this._importType = value;
	}


    /**
     * Getter otherAmount
     * @return {number}
     */
	public get otherAmount(): number {
		return this._otherAmount;
	}

    /**
     * Getter chaAmount
     * @return {number}
     */
	public get chaAmount(): number {
		return this._chaAmount;
	}

    /**
     * Setter otherAmount
     * @param {number} value
     */
	public set otherAmount(value: number) {
		this._otherAmount = value;
	}

    /**
     * Setter chaAmount
     * @param {number} value
     */
	public set chaAmount(value: number) {
		this._chaAmount = value;
	}


    /**
     * Getter otherRate
     * @return {number}
     */
	public get otherRate(): number {
		return this._otherRate;
	}

    /**
     * Setter otherRate
     * @param {number} value
     */
	public set otherRate(value: number) {
		this._otherRate = value;
	}


    /**
     * Getter chaRate
     * @return {number}
     */
	public get chaRate(): number {
		return this._chaRate;
	}

    /**
     * Setter chaRate
     * @param {number} value
     */
	public set chaRate(value: number) {
		this._chaRate = value;
	}

    /**
     * Getter ourAmount
     * @return {number}
     */
	public get ourAmount(): number {
		return this._ourAmount;
	}

    /**
     * Setter ourAmount
     * @param {number} value
     */
	public set ourAmount(value: number) {
		this._ourAmount = value;
	}


    /**
     * Getter isCompleted
     * @return {boolean}
     */
	public get isCompleted(): boolean {
		return this._isCompleted;
	}

    /**
     * Setter isCompleted
     * @param {boolean} value
     */
	public set isCompleted(value: boolean) {
		this._isCompleted = value;
	}


    /**
     * Getter totalExpenseAmount
     * @return {number}
     */
	public get totalExpenseAmount(): number {
		return this._totalExpenseAmount;
	}

    /**
     * Setter totalExpenseAmount
     * @param {number} value
     */
	public set totalExpenseAmount(value: number) {
		this._totalExpenseAmount = value;
	}


    /**
     * Getter vehicleNo
     * @return {string}
     */
	public get vehicleNo(): string {
		return this._vehicleNo;
	}

    /**
     * Getter containersName
     * @return {string}
     */
	public get containersName(): string {
		return this._containersName;
	}

    /**
     * Setter vehicleNo
     * @param {string} value
     */
	public set vehicleNo(value: string) {
		this._vehicleNo = value;
	}

    /**
     * Setter containersName
     * @param {string} value
     */
	public set containersName(value: string) {
		this._containersName = value;
	}


    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


    /**
     * Getter docs
     * @return {any[]}
     */
	public get docs(): any[] {
		return this._docs;
	}

    /**
     * Getter deletedDocumentID
     * @return {number[]}
     */
	public get deletedDocumentID(): number[] {
		return this._deletedDocumentID;
	}

    /**
     * Getter documents
     * @return {any[]}
     */
	public get documents(): any[] {
		return this._documents;
	}

    /**
     * Setter docs
     * @param {any[]} value
     */
	public set docs(value: any[]) {
		this._docs = value;
	}

    /**
     * Setter deletedDocumentID
     * @param {number[]} value
     */
	public set deletedDocumentID(value: number[]) {
		this._deletedDocumentID = value;
	}

    /**
     * Setter documents
     * @param {any[]} value
     */
	public set documents(value: any[]) {
		this._documents = value;
	}


    /**
     * Getter deletedTypeID
     * @return {number[]}
     */
	public get deletedTypeID(): number[] {
		return this._deletedTypeID;
	}

    /**
     * Setter deletedTypeID
     * @param {number[]} value
     */
	public set deletedTypeID(value: number[]) {
		this._deletedTypeID = value;
	}


    /**
     * Getter expenseID
     * @return {number}
     */
	public get expenseID(): number {
		return this._expenseID;
	}

    /**
     * Setter expenseID
     * @param {number} value
     */
	public set expenseID(value: number) {
		this._expenseID = value;
	}


    /**
     * Getter temp_date
     * @return {any}
     */
	public get temp_date(): any {
		return this._temp_date;
	}

    /**
     * Setter temp_date
     * @param {any} value
     */
	public set temp_date(value: any) {
		this._temp_date = value;
	}


    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Getter expenseDate
     * @return {string}
     */
    public get expenseDate(): string {
        return this._expenseDate;
    }

    /**
     * Getter containerID
     * @return {number}
     */
    public get containerID(): number {
        return this._containerID;
    }

    /**
     * Getter notes
     * @return {string}
     */
    public get notes(): string {
        return this._notes;
    }

    /**
     * Getter importerID
     * @return {number}
     */
    public get importerID(): number {
        return this._importerID;
    }

    /**
     * Getter chaShippingTypes
     * @return {string}
     */
    public get chaShippingTypes(): string {
        return this._chaShippingTypes;
    }

    /**
     * Getter ourShippingTypes
     * @return {string}
     */
    public get ourShippingTypes(): string {
        return this._ourShippingTypes;
    }

    /**
     * Getter expenseCarringType
     * @return {string}
     */
    public get expenseCarringType(): string {
        return this._expenseCarringType;
    }

    /**
     * Getter ourPrice
     * @return {number}
     */
    public get ourPrice(): number {
        return this._ourPrice;
    }

    /**
     * Getter chaPrice
     * @return {number}
     */
    public get chaPrice(): number {
        return this._chaPrice;
    }

    /**
     * Getter otherPrice
     * @return {number}
     */
    public get otherPrice(): number {
        return this._otherPrice;
    }

    /**
     * Getter expenseTypeItems
     * @return {ContainerExpenseList[]}
     */
    public get expenseTypeItems(): ContainerExpenseList[] {
        return this._expenseTypeItems;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Setter expenseDate
     * @param {string} value
     */
    public set expenseDate(value: string) {
        this._expenseDate = value;
    }

    /**
     * Setter containerID
     * @param {number} value
     */
    public set containerID(value: number) {
        this._containerID = value;
    }

    /**
     * Setter notes
     * @param {string} value
     */
    public set notes(value: string) {
        this._notes = value;
    }

    /**
     * Setter importerID
     * @param {number} value
     */
    public set importerID(value: number) {
        this._importerID = value;
    }

    /**
     * Setter chaShippingTypes
     * @param {string} value
     */
    public set chaShippingTypes(value: string) {
        this._chaShippingTypes = value;
    }

    /**
     * Setter ourShippingTypes
     * @param {string} value
     */
    public set ourShippingTypes(value: string) {
        this._ourShippingTypes = value;
    }

    /**
     * Setter expenseCarringType
     * @param {string} value
     */
    public set expenseCarringType(value: string) {
        this._expenseCarringType = value;
    }

    /**
     * Setter ourPrice
     * @param {number} value
     */
    public set ourPrice(value: number) {
        this._ourPrice = value;
    }

    /**
     * Setter chaPrice
     * @param {number} value
     */
    public set chaPrice(value: number) {
        this._chaPrice = value;
    }

    /**
     * Setter otherPrice
     * @param {number} value
     */
    public set otherPrice(value: number) {
        this._otherPrice = value;
    }

    /**
     * Setter expenseTypeItems
     * @param {ContainerExpenseList[]} value
     */
    public set expenseTypeItems(value: ContainerExpenseList[]) {
        this._expenseTypeItems = value;
    }


}

export class ContainerExpenseList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('amount')
    @deserializeAs('amount')
    private _amount: number;

    @serializeAs('expenseTypeId')
    @deserializeAs('expenseTypeId')
    private _expenseTypeId: number;

    @serializeAs('isAddInLedger')
    @deserializeAs('isAddInLedger')
    private _isAddInLedger: boolean;

    @serializeAs('isAddInAvgAmount')
    @deserializeAs('isAddInAvgAmount')
    private _isAddInAvgAmount: boolean;

    @deserializeAs('expenseDropdown')
    private _expenseDropdown: any[];

    @deserializeAs('expenseTypeName')
    private _expenseTypeName: string;

    @deserializeAs('defaultCode')
    private _defaultCode: string;

    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @deserializeAs('isGST')
    private _isGST: boolean;

    constructor() {
        this.expenseDropdown = [];
        this.isAddInLedger = false;
        this.isAddInAvgAmount = false;
        this.isDefault = false;
        this.isGST = false;
    }


    /**
     * Getter defaultCode
     * @return {string}
     */
	public get defaultCode(): string {
		return this._defaultCode;
	}

    /**
     * Setter defaultCode
     * @param {string} value
     */
	public set defaultCode(value: string) {
		this._defaultCode = value;
	}
    

    /**
     * Getter isGST
     * @return {boolean}
     */
	public get isGST(): boolean {
		return this._isGST;
	}

    /**
     * Setter isGST
     * @param {boolean} value
     */
	public set isGST(value: boolean) {
		this._isGST = value;
	}

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter expenseTypeName
     * @return {string}
     */
	public get expenseTypeName(): string {
		return this._expenseTypeName;
	}

    /**
     * Setter expenseTypeName
     * @param {string} value
     */
	public set expenseTypeName(value: string) {
		this._expenseTypeName = value;
	}


    /**
     * Getter isAddInLedger
     * @return {boolean}
     */
	public get isAddInLedger(): boolean {
		return this._isAddInLedger;
	}

    /**
     * Setter isAddInLedger
     * @param {boolean} value
     */
	public set isAddInLedger(value: boolean) {
		this._isAddInLedger = value;
	}

    /**
     * Getter isAddInAvgAmount
     * @return {boolean}
     */
	public get isAddInAvgAmount(): boolean {
		return this._isAddInAvgAmount;
	}

    /**
     * Setter isAddInAvgAmount
     * @param {boolean} value
     */
	public set isAddInAvgAmount(value: boolean) {
		this._isAddInAvgAmount = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Getter amount
     * @return {number}
     */
    public get amount(): number {
        return this._amount;
    }

    /**
     * Setter amount
     * @param {number} value
     */
    public set amount(value: number) {
        this._amount = value;
    }

    /**
     * Getter expenseTypeId
     * @return {number}
     */
    public get expenseTypeId(): number {
        return this._expenseTypeId;
    }

    /**
     * Setter expenseTypeId
     * @param {number} value
     */
    public set expenseTypeId(value: number) {
        this._expenseTypeId = value;
    }

    /**
     * Getter expenseDropdown
     * @return {any[]}
     */
    public get expenseDropdown(): any[] {
        return this._expenseDropdown;
    }

    /**
     * Setter expenseDropdown
     * @param {any[]} value
     */
    public set expenseDropdown(value: any[]) {
        this._expenseDropdown = value;
    }

}
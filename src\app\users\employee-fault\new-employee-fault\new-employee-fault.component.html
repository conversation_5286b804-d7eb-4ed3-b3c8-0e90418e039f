<div class="page-content">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Add New Fault</h4>
        </div>
        <div class="page-title-right">
            <Button [routerLink]="'/users/employee-fault'" class="btn btn-sm btn-icon btn-outline-white"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </Button>
        </div>
    </div>

    <div class="content-area">
        <div class="card card-theme">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="form-group form-group-inline-control theme-ngselect required">
                            <div class="form-label">Employee</div>
                            <div class="form-control-wrapper">

                                <ng-select class="" placeholder="Select Demo" [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required">
                            <div class="form-label">Date</div>
                            <div class="form-group-icon-end">
                                <i class="th th-outline-calendar"></i>
                                <input type="text" class="form-control" placeholder="Cureent Date">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control theme-ngselect required">
                            <div class="form-label">Fault Type</div>
                            <div class="form-control-wrapper">

                                <ng-select class="" placeholder="Select Demo" [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control theme-ngselect required">
                            <div class="form-label">Critical Level</div>
                            <div class="form-control-wrapper">

                                <ng-select class="" placeholder="Select Demo" [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>

                        <div class="form-group form-group-inline-control required">
                            <div class="form-label">Notes</div>
                            <textarea class="form-control" placeholder="Enter Notes"></textarea>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="attachments-wrapper">
                            <div class='attachments-container h-100'>
                                <div class='attachments-content'>
                                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                    <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                                </div>
                                <input type="file" ref={imageRef} multiple />
                            </div>
                            <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                <div class='attachments-upload-row'>
                                    <div class='attachments-upload-col'>
                                        <div class='card-attachments-upload'>
                                            <div class='attachments-image'>
                                                <img src="assets/images/avatar.jpg" alt="valamji" />
                                            </div>
                                            <div class="attachments-text">
                                                <h6 class="file-name">Filename.jpg</h6>
                                                <p class="file-size">Size: 5mb</p>
                                            </div>
                                            <button class="btn-close" variant="close"><i
                                                    class='th th-close'></i></button>
                                        </div>
                                        <div class="radio radio-primary">
                                            <input type="radio" id="thumb" name="thumb" checked="">
                                            <label for="thumb">Mark default</label>
                                        </div>
                                    </div>
                                    <div class='attachments-upload-col'>
                                        <div class='card-attachments-upload'>
                                            <div class='attachments-image'>
                                                <img src="assets/images/avatar.jpg" alt="valamji" />
                                            </div>
                                            <div class="attachments-text">
                                                <h6 class="file-name">Filename.jpg</h6>
                                                <p class="file-size">Size: 5mb</p>
                                            </div>
                                            <button class="btn-close" variant="close"><i
                                                    class='th th-close'></i></button>
                                        </div>
                                        <div class="radio radio-primary">
                                            <input type="radio" id="thumb2" name="thumb" checked="">
                                            <label for="thumb2">Mark default</label>
                                        </div>
                                    </div>
                                    <div class='attachments-upload-col'>
                                        <div class='attachments-container attachments-container2'>
                                            <div class='attachments-content'>
                                                <button class='btn btn-primary btn-icon btn-sm btn-round'><i
                                                        class="th th-outline-add"></i></button>
                                            </div>
                                            <input type="file" ref={imageRef} multiple />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>
                <div class='bottombar-left'>
                    <button type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
                            class="th th-outline-close-circle"></i>Cancel</button>
                </div>
                <div class='bottombar-right'>
                </div>
            </div>
        </div>
    </div>
</div>
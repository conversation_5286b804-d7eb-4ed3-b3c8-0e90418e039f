.modal-theme {
    .modal-content {
        border: 0;
        border-radius: 15px;

        .modal-header,
        .modal-body,
        .modal-footer {
            padding: 30px;
        }

        .modal-header {
            padding-bottom: 0;
        }

        .modal-body {
            padding-bottom: 20px;
        }

        .modal-footer {
            padding-top: 0;
        }
    }

    .btn-close {
        background: 0;
        font-size: 20px;
        color: $text_color;
        opacity: 1;
        box-shadow: none;
        border: 0;
        padding: 0;
        height: 31px;
        width: 31px;

        i {
            display: inline-block;
            transition: 0.70s;
            -webkit-text-stroke: 0.5px;
        }

        &:hover {
            background-color: #F4F5F6;
            color: $black_color;

            i {
                transform: rotate(90deg);
            }
        }
    }

    .modal-header {
        border-bottom: 0;

        .modal-header-details {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .modal-title {
            font-size: 18px;
            color: $text_black_color;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;

            i {
                margin-right: 4px;
            }
        }

        p {
            font-size: 14px;
            color: $text_color;
            font-weight: 600;
            margin-bottom: 0;
            word-wrap: break-word;
        }
    }

    .modal-footer {
        border-top: 0;

        .btn {
            min-width: 100px;
            padding: 8px 16px;
        }
    }

    .modal-body {
        .btn-close {
            position: absolute;
            right: 15px;
            top: 15px;
        }

        p {
            font-size: 14px;
            color: $text_color;
            font-weight: 400;
            margin-bottom: 0;
            word-wrap: break-word;
            word-break: break-word;
            white-space: normal;
        }
    }

    .modal-button-group {
        margin-top: 25px;
        display: flex;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;


        &.modal-full-width-btn {
            .btn {
                width: 100%;
            }
        }

        &.modal-btn-end {
            justify-content: flex-end;
        }


    }

    .modal-footer-group {
        margin-top: 0;
        display: flex;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;

        &.modal-full-width-btn {
            .btn {
                width: 100%;
            }
        }

        &.modal-btn-end {
            justify-content: flex-end;
        }
    }

    &.modal-confirmation {
        .modal-dialog {
            @media only screen and (min-width:1200px) {
                width: 400px;
            }
        }

        .modal-body {
            padding: 0 25px 25px;
        }

        .modal-confirmation-container {
            text-align: center;
        }

        .modal-confirmation-image {

            img {
                height: 70px;
                width: auto;
            }
        }

        .modal-confirmation-icon {
            width: 70px;
            height: 70px;
            border-radius: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 25px;
            line-height: 25px;
            position: relative;
            margin: 0 auto;
            color: $white_color;

            i {
                z-index: 22;
            }

            &::before {
                content: "";
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                border-radius: 50px;
                background-color: $primary_color;
            }

            &::after {
                content: "";
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 70px;
                height: 70px;
                border-radius: 50px;
                background-color: $primary_color;
                opacity: 0.3;
            }


        }

        .modal-confirmation-content {
            margin-top: 30px;
            color: #2D3C5C;

            h5 {
                font-size: 20px;
                margin-bottom: 10px;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
            }

            p {
                margin-bottom: 0;
            }
        }

        .modal-forms {
            margin-top: 25px;
            text-align: center;

            .form-group {
                .form-label {
                    text-align: left;
                    width: 100%;
                }
            }
        }
    }

    &.modal-approve {
        .modal-confirmation-icon {

            &::before,
            &::after {
                background-color: $success_color;
            }
        }
    }

    &.modal-reject {
        .modal-confirmation-icon {

            &::before,
            &::after {
                background-color: $danger_color;
            }
        }
    }

    &.modal-warning {
        .modal-confirmation-icon {

            &::before,
            &::after {
                background-color: $warning_color;
            }
        }
    }

    &.modal-warning-two {
        .modal-confirmation-icon {

            &::before,
            &::after {
                background-color: $secondary_two_color;
            }
        }
    }

    &.modal-close-icon {
        .btn-close {
            position: absolute;
            right: -30px;
            top: -30px;
            padding: 0;
            margin: 0;
            color: $white_color;

            &:hover {
                color: $danger_color;
            }
        }
    }
}

.modal-delete {

    .modal-content .modal-header,
    .modal-content .modal-body,
    .modal-content .modal-footer {
        padding: 34px;
    }

    .modal-content .modal-footer {
        padding-top: 0;
    }

    .delete-modal {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .delete-icon {
            width: 54px;
            height: 54px;
            border-radius: 25px;
            color: $danger_color;
            background-color: #FEF1F2;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            font-size: 26px;
        }

        .delete-text {
            h6 {
                font-size: 18px;
                font-weight: 600;
                line-height: 23.4px;
                text-align: center;
                margin-bottom: 16px;
                color: $black_color;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
            }

            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 22.1px;
                text-align: center;
                margin-bottom: 0px;
                color: $text_color;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
            }
        }
    }

    .modal-footer {
        display: flex;

        .btn {
            width: 48%;
        }
    }
}

.modal-color {
    .modal-body {
        min-height: 500px;
    }
}


// multiple modals modal-backdrop CSS - Start
.modal+.modal {
    z-index: 1057;
}

.modal+.modal+.modal {
    z-index: 1060;
}

.modal-backdrop+.modal-backdrop {
    z-index: 1056;
}

.modal-backdrop+.modal-backdrop+.modal-backdrop {
    z-index: 1059;
}

// multiple modals modal-backdrop CSS - End

// All masters model css - Start
.level-modal {
    .purchase-permission-wrapper {
        padding: 12px;
        background-color: $primary_light_color;
        border-radius: 10px;

        .purchase-permission-row {
            padding: 12px;
            background-color: $white_color;
            border-radius: 10px;
            margin-bottom: 10px;

            .form-group {
                margin: 0;
                width: 100%;

                @media only screen and (max-width: 1023px) {
                    margin-bottom: 12px;
                }

            }

            .purchase-permission-group {
                display: flex;
                align-items: flex-end;
                justify-content: space-between;
                gap: 12px;

                @media only screen and (max-width: 767px) {
                    align-items: flex-start;
                    flex-direction: column;
                    gap: 5px;
                }
            }
        }
    }
}

// All masters model css - End

// All Items model css - Start
.product-slider-modal {
    .modal-dialog {
        max-width: 600px;

        @media only screen and (max-width: 767px) {
            max-width: max-content;
        }
    }
}

.product-details-container {
    margin-bottom: 27px;

    &.product-status {
        .tbl-user-text {
            display: flex;
            flex-direction: column;

            p {
                &:first-child {
                    padding-right: 0 !important;
                    margin-right: 0 !important;
                    border: 0 !important;
                }
            }

            span.grid-item {
                width: 200px;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
                
                div {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                }
                color: $text_color;
                font-size: 12px;
            }
        }
    }

    .product-details-left {
        display: flex;
        align-items: center;
        gap: 10px;

        .product-image {
            flex: 0 0 40px;
            width: 40px;
            height: 40px;
            border-radius: 4px;
            border: 1px solid $stock_light;
            background-color: #F9F9F9;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .product-title {
            display: flex;
            align-items: center;

            p {
                display: inline-block;
                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                color: $text_color;
                margin: 0;

                &:first-child {
                    padding-right: 10px;
                    margin-right: 10px;
                    border-right: 1px solid #B8C3CF;
                }
            }
        }
    }

    .product-details-right {}

    &.product-import {
        .import-items-wrapper {
            .import-items-nav {
                .import-items-list {
                    display: flex;
                    flex-direction: column;

                    ul {
                        margin: 0;
                        display: flex;
                        align-items: center;
                        flex-direction: row;
                        gap: 10px 25px;

                        li {
                            display: flex;
                            align-items: center;
                            cursor: pointer;
                            position: relative;

                            &:last-child {
                                .step-boxs {
                                    padding-right: 0;
                                    margin-right: 0;
                                }
                            }

                            &.active {
                                .step-boxs {
                                    .step-number {
                                        color: $primary_color;
                                        border: 1px solid #B1DDFC;
                                    }

                                    h6 {
                                        color: $primary_color;
                                    }
                                }
                            }

                            &.done {
                                .step-boxs {
                                    .step-number {
                                        display: none;
                                    }

                                    .step-done {
                                        display: flex;
                                    }

                                    h6 {
                                        color: $text_black_color;
                                    }
                                }
                            }

                            .step-boxs {
                                display: flex;
                                align-items: center;
                                flex-wrap: nowrap;
                                position: relative;
                                z-index: 9;
                                gap: 10px;

                                .step-number {
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    font-weight: 600;
                                    line-height: 20.8px;
                                    text-align: center;
                                    color: $text_color;
                                    border: 1px solid $primary_light_color;
                                    background-color: $primary_light_color;
                                    overflow: hidden;
                                }

                                .step-done {
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    flex: 0 0 40px;
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 8px;
                                    border: 1px solid $success_bg_color;
                                    background-color: $success_bg_color;
                                    overflow: hidden;
                                    display: none;

                                    img {
                                        width: 21px;
                                        height: 21px;
                                        object-fit: contain;
                                    }

                                    i {
                                        font-size: 20px;
                                    }
                                }

                                h6 {
                                    font-size: 14px;
                                    font-weight: 600;
                                    line-height: 18px;
                                    color: $text_color;
                                    margin-bottom: 0;
                                }

                                p {
                                    font-size: 14px;
                                    font-weight: 400;
                                    line-height: 23px;
                                    color: $text_color;
                                    margin-bottom: 0;
                                }
                            }
                        }
                    }
                }
            }
        }

    }
}

// All Items model css - End

// sales-orders model css - Start
.stock-availability-modal {

    .modal-header,
    .modal-footer {
        padding: 14px !important;
    }

    .modal-header {
        .modal-title {
            font-size: 14px;
            line-height: 18.2px;
        }

        .btn-close {
            font-size: 18px;
            height: 28px;
            width: 28px;
        }
    }

    .modal-body {
        padding: 0 !important;
    }

    .file-select-type {
        .file-select-type-item {

            .file-type-box {
                padding: 10px 12px;
                background-color: $white_color;
                border-bottom: 2px solid $primary_color;

                label {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 16.8px;
                    color: $primary_color;
                    display: flex;
                    align-items: center;
                    gap: 0 6px;
                    padding-left: 22px;

                    i {
                        font-size: 15px;
                        line-height: 15px;
                    }

                }

                &.active {
                    background-color: $success_color;

                    label {
                        color: $white_color;

                        &::before {
                            border-color: $white_color;
                            background-color: transparent;
                        }
                    }

                    input[type="radio"] {
                        &:checked+label::after {
                            background-color: $white_color;
                        }
                    }
                }
            }
        }
    }
}


// sales-orders model css - Start

/* --------------------------- carton-card-wrapper  start -------------------------- */
.carton-card-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 13px;

    .carton-card {
        border-radius: 10px;
        background-color: #F0F3F5;
        overflow: hidden;
        box-shadow: none;
        border: 0;

        .card-body {
            padding: 12px;

            .card-details {
                p {
                    font-size: 14px;
                    line-height: 23.8px;
                    font-weight: 400;
                    letter-spacing: 0%;
                    color: $text_color;
                    margin-bottom: 2px;
                }

                h6 {
                    font-size: 14px;
                    line-height: 18px;
                    font-weight: 600;
                    color: $primary_color;
                    margin-bottom: 0;
                }
            }
        }
    }
}

/* --------------------------- carton-card-wrapper  end -------------------------- */

/* NGB Modals */

.modal-xs .modal-dialog {
    max-width: 300px;
}

.modal-sm .modal-dialog {
    max-width: 300px;
}

.modal-md .modal-dialog {
    max-width: 400px;
}

.modal-lg .modal-dialog {
    max-width: 700px;
}
.modal-lg-two .modal-dialog {
    max-width: 800px;
}

.modal-xl .modal-dialog {
    max-width: 1140px;
}

.modal-ng-scroll {
    .modal-body {
        overflow-y: auto;
        max-height: 75vh;
    }
}
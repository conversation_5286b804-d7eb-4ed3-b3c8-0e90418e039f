import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { Component, OnInit } from "@angular/core";
import { Branch } from "@modal/Branch";
import { UtilsService } from "@service/utils.service";
import { Deserialize, Serialize } from "cerialize";
declare var window: any;

@Component({
  selector: 'app-branch-list',
  templateUrl: './branch-list.component.html',
  styleUrls: ['./branch-list.component.css'],
})
export class BranchListComponent implements OnInit {

  branchList: Branch[] = []
  branchObj = new Branch();
  deleteBranchModal: any;

  markAsPrimaryBModal: any;
  markAsPrimaryObj = {
    item: null,
    value: null,
    index: null
  }

  isDragging = false;

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {

    this.deleteBranchModal = new window.bootstrap.Modal(
      document.getElementById('deleteBranchModal')
    );

    this.markAsPrimaryBModal = new window.bootstrap.Modal(
      document.getElementById('markAsPrimaryBModal')
    );

    this.getBranchList();
  }

  getBranchList() {

    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.BRANCH_SAVE_EDIT_DELETE, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.branchList = Deserialize(response, Branch);
      }
      else {
        this.branchList = []
      }
    })
  }

  onChangeStatus(item: Branch, value, index) {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.BRANCH_STATUS_CHANGE + `${item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.branchList[index].isActive = value
      } else {
        this.branchList[index].isActive = !value
      }
      this.getBranchList()
    }, true);
  }

  // onMarkAsPrimary(item: Branch, value, index) {
  //   this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.BRANCH_MARK_AS_PRIM + `${item.id}`, {}, '', (response) => {
  //     if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
  //       this.branchList[index].isMainBranch = value
  //     } else {
  //       this.branchList[index].isMainBranch = !value
  //     }
  //     this.getBranchList()
  //   }, true);
  // }

  //delete 
  openDeleteBranchModal(obj: Branch) {
    this.branchObj = Serialize(obj)
    this.deleteBranchModal.show();
  }

  deleteBranch() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.BRANCH_SAVE_EDIT_DELETE + `?id=${this.branchObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteBranchModal.hide();
        this.getBranchList();
      }
    })
  }

  //drag drop for moving card from prev index to current index
  drop(event: CdkDragDrop<any>) {

    // this.branchList[event.previousContainer.data.index] = event.container.data.item;
    // this.branchList[event.container.data.index] = event.previousContainer.data.item;
    if(event.previousIndex === event.currentIndex) {
      return;
    }
    // inbuild function for moving item from prev index to current index
    moveItemInArray(this.branchList, event.previousIndex, event.currentIndex);

    let seqArr = this.branchList.map((v, i) => ({
      id: v.id,
      sortOrder: i + 1
    }));
    const param = {
      branches: seqArr
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.BRANCH_ORDER, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getBranchList();
      }
    }, null, true)
  }

  openMarkAsPrimary(item: Branch, value, index) {
    this.markAsPrimaryObj.item = Serialize(item)
    this.markAsPrimaryObj.value = (value)
    this.markAsPrimaryObj.index = (index)
    this.markAsPrimaryBModal.show();
  }

  onMarkAsPrimary() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.BRANCH_MARK_AS_PRIM + `${this.markAsPrimaryObj.item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.branchList[this.markAsPrimaryObj.index].isMainBranch = this.markAsPrimaryObj.value
      } else {
        this.branchList[this.markAsPrimaryObj.index].isMainBranch = !this.markAsPrimaryObj.value
      }
      this.markAsPrimaryBModal.hide();
      this.getBranchList()
    }, true);
  }

  trackBy(index: number, name: Branch): number {
    return name.id;
  }
}

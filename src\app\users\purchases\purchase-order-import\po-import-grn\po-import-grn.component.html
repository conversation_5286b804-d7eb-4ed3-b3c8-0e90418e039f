<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input type="text" class="form-control" placeholder="Search by GRN / Container / Marka " (change)="onSearch()"
                    [(ngModel)]="paginationRequest.searchText">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter w-50">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input pickerDirective class="form-control" readonly ngxDaterangepickerMd
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true"
                    [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                    [showClearButton]="false" placeholder="GRN Start Date & Time" [autoApply]="true"
                    [showRangeLabelOnInput]="true" startKey="start" endKey="end" [closeOnAutoApply]="true">
            </div>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>

    </div>
    <div class="page-filters-right">
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme table-hover table table-bordered tbl-collapse">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            GRN No
                        </th>
                        <th>Tempo No</th>
                        <th>GRN Start Date & Time</th>
                        <th>Linked Containers</th>
                        <th>Total Tempo <br/> Cartons</th>
                        <th>Received Cartons</th>
                        <th>GRN Status</th>
                        <th>Action</th>
                    </tr>
                </thead>

                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="item.grnItems.length > 0 ? toggleExpand(i) : null">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}</span>
                                    <b class="text-black">
                                        {{item.grnno ? item.grnno : '-'}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.tempoNo ? item.tempoNo : '-'}}</td>
                            <td>{{item.grndate ? (item.grndate | date: 'dd/MM/YYYY h:mm a') : '-'}}</td>
                            <td>{{item.containersName ? item.containersName : '-'}}</td>
                            <td>{{item.cartonQtyVehicle ? item.cartonQtyVehicle : '-'}}</td>
                            <td>{{item.totalReceivedQty ? item.totalReceivedQty : '-'}}</td>
                            <td [ngClass]="{'text-secondary': item.grnStatus?.value === 'INPROGRESS', 'text-success': item.grnStatus?.value === 'COMPLETED'}">
                                <strong>{{item.grnStatus?.label}}</strong>
                            </td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button *ngIf="item.grnItems.length > 0" (click)="toggleExpand(i)" class="btn btn-xs btn-light-white btn-icon"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}" role="button" aria-expanded="false"
                                        [attr.data.target]="'#table-collapse-2'+ i" [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr *ngIf="item.isExpand" class="collapse" [id]="'table-collapse-2' + i" [ngClass]="{'show': item.isExpand}">
                            <td colspan="30" class="p-0 tbl-collapse-child tbl-collapse-child-responsive">
                        
                                <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky">
                                        <thead class="border-less">
                                            <tr>
                                                <ng-container *ngFor="let th of headerObj?.optionsArray; index as k">
                                                    <th *ngIf="th.show" [class]="th.class" [innerHTML]="th.displayName">
                                                    </th>
                                                </ng-container>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr *ngFor="let child of item.grnItems; index as l; trackBy: trackByChild">
                                                <ng-container *ngFor="let column of headerObj.columnArr;">
                                                    <td class="tbl-user" *ngIf="column.show">
                                                        <ng-container [ngSwitch]="column.key">
                                                
                                                            <ng-container *ngSwitchCase="0">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <span>{{(l + 1) | padNum}}</span>
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image" *ngIf="child?.item">
                                                                            <img *ngIf="child.item?.formattedName" loading="lazy"
                                                                                [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                alt="valamji">
                                                                            <ng-container *ngIf="!child.item?.formattedName">{{
                                                                                child.displayName?.charAt(0).toUpperCase()
                                                                                }}
                                                                            </ng-container>
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{child.item?.skuId}}</p>
                                                                                <span class="tbl-description">{{child.item.displayName}}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown"
                                                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM])">
                                                                            <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                                                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                                                data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option" placement="bottom"
                                                                                container="body" triggers="hover" ngbTooltip="More Option" placement="bottom"
                                                                                container="body" triggers="hover">
                                                                                <i class="th th-outline-more"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                  <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                    <i class="th th-outline-eye"></i> View Item Details
                                                                                  </a>
                                                                                </li>
                                                                              </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="1">
                                                                <span class="d-flex flex-column align-items-start">
                                                                    <div>{{child.marka ? child.marka : '-'}}</div>
                                                                    <div>
                                                                        <ng-container *ngIf="child.cartonLength; else noDim">
                                                                            {{child.cartonLength ? child.cartonLength :
                                                                            '-'}} X
                                                                            {{child.cartonWidth ? child.cartonWidth :
                                                                            '-'}} X
                                                                            {{child.cartonHeight ? child.cartonHeight :
                                                                            '-'}}
                                                                            {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                        </ng-container>
                                                                        <ng-template #noDim>
                                                                            <span>-</span>
                                                                        </ng-template>
                                                                    </div>
                                                                    <div>
                                                                        <p>{{child.pricePerCarton ? child.pricePerCarton
                                                                            : '-'}}</p>
                                                                    </div>
                                                                    <div>
                                                                        <p class="tbl-po-notes">{{child.chinaComment ?
                                                                            child.chinaComment : ''}}</p>
                                                                    </div>
                                                                    <div class="fs-14">
                                                                        <b>{{child.item?.skuId || ''}}</b>
                                                                    </div>
                                                                </span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="2">
                                                                <span>{{child.grnReceivedCarton ? child.grnReceivedCarton : 0}}</span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="3">
                                                                <span>{{child.containerName ? child.containerName : '-'}}</span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="4">
                                                                <span>{{child.poNumber ? child.poNumber : 0}}</span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="5">
                                                                <span>{{child.poOrderDate ? (child.poOrderDate | date: 'dd/MM/YYYY') : '-'}}</span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="6">
                                                                <span>{{child.poCarton ? child.poCarton : 0}}</span>
                                                            </ng-container>
                                                
                                                            <ng-container *ngSwitchCase="7">
                                                                <span>{{child.pricePerCarton ? child.pricePerCarton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="8">
                                                                <span>{{child.totalQty ? child.totalQty : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="9">
                                                                <span>{{child.grnReceivedQty ? child.grnReceivedQty : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="10">
                                                                <ng-container *ngIf="child.cartonLength; else noLength">
                                                                    {{child.cartonLength ? child.cartonLength :
                                                                    '-'}} X
                                                                    {{child.cartonWidth ? child.cartonWidth :
                                                                    '-'}} X
                                                                    {{child.cartonHeight ? child.cartonHeight :
                                                                    '-'}}
                                                                    {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                </ng-container>
                                                                <ng-template #noLength>
                                                                    <span>-</span>
                                                                </ng-template>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="11">
                                                                <ng-container *ngIf="child.realCartonLength; else noDim">
                                                                    {{child.realCartonLength ? child.realCartonLength :
                                                                    '-'}} X
                                                                    {{child.realCartonWidth ? child.realCartonWidth :
                                                                    '-'}} X
                                                                    {{child.realCartonHeight ? child.realCartonHeight :
                                                                    '-'}}
                                                                    {{child.realCartonDimUnit}}
                                                                </ng-container>
                                                                <ng-template #noDim>
                                                                    <span>-</span>
                                                                </ng-template>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="12">
                                                                <span>{{child.realCBMPerCarton ? child.realCBMPerCarton : 0}}</span>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="13">
                                                                <span>{{child.totalCBM ? child.totalCBM : 0}}</span>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="14">
                                                                <span>{{child.realCartonWeight ? child.realCartonWeight : 0}}</span>
                                                            </ng-container>
                
                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>

                </tbody>

            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData">
    </app-pagination>
</div>

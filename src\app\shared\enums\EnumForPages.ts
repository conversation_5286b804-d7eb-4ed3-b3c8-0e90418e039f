export const EnumForPages = {
    // MASTER
    MASTER: 'Master',
    VIEW_MASTER: 'View Masters',
    ADD_MASTER: 'Add Masters',
    EDIT_MASTER: 'Edit Masters',
    DELETE_MASTER: 'Delete Masters',

    // ROLES
    ROLES: 'Roles',
    VIEW_ROLE: 'View Role',
    ADD_ROLE: 'Add Role',
    EDIT_ROLE: 'Edit Role',
    DELETE_ROLE: 'Delete Role',

    // MASTER
    BRANCH: 'Branch',
    VIEW_BRANCH: 'View Branch',
    ADD_BRANCH: 'Add Branch',
    EDIT_BRANCH: 'Edit Branch',
    DELETE_BRANCH: 'Delete Branch',

    //USERS
    USER: 'Users',
    VIEW_USER: 'View Users',
    ADD_USER: 'Add Users',
    EDIT_USER: 'Edit Users',
    DELETE_USER: 'Delete Users',

    //Item Group
    ITEM_GROUP: 'Item Group',
    VIEW_ITEM_GRP: 'View Item Group',
    ADD_ITEM_GRP: 'Add Item Group',
    EDIT_ITEM_GRP: 'Edit Item Group',
    DELETE_ITEM_GRP: 'Delete Item Group',

    //Item
    ITEM: 'Item',
    VIEW_ITEM: 'View Item',
    ADD_ITEM: 'Add Item',
    EDIT_ITEM: 'Edit Item',
    EDIT_SALES_PRICE: 'Edit Sales Price',
    DELETE_ITEM: 'Delete Item',

    //CATEGORY
    CATEGORY: 'Category',
    VIEW_CATEGORY: 'View Category',
    ADD_CATEGORY: 'Add Category',
    EDIT_CATEGORY: 'Edit Category',
    DELETE_CATEGORY: 'Delete Category',

    //Accounts
    ACCOUNTS: "Accounts",
    VIEW_ACCOUNTS: 'View Accounts',
    ADD_ACCOUNTS: 'Add Accounts',
    EDIT_ACCOUNTS: 'Edit Accounts',
    DELETE_ACCOUNTS: 'Delete Accounts',

    //Settings
    SETTINGS: 'Setting',
    VIEW_SETTING: 'setting',
    CARTOM_MAPPING: 'Carton Mapping',

    // Registration
    REG: 'Registration',
    VIEW_REG: 'View Registration',
    ADD_REG: 'Add Registration',
    EDIT_REG: 'Edit Registration',
    DELETE_REG: 'Delete Registration',

    //Warehouse
    WAREHOUSE: 'Warehouse',
    VIEW_WAREHOUSE: 'View Warehouse',
    VIEW_AR: 'View Aisle & Rack',
    VIEW_DROP_OFF_LOCATION: 'View Drop off location',
    ADD_WAREHOUSE: 'Add Warehouse',
    ADD_RACK: 'Add Rack',
    ADD_AISLE: 'Add Aisle',
    ADD_DROP_OFF_LOCATION: 'Add Drop off location',
    EDIT_WAREHOUSE: 'Edit Warehouse',
    EDIT_RACK: 'Edit Rack',
    EDIT_AISLE: 'Edit Aisle',
    EDIT_DROP_OFF_LOCATION: 'Edit Drop off location',
    DELETE_WAREHOUSE: 'Delete Warehouse',
    DELETE_RACK: 'Delete Rack',
    DELETE_AISLE: 'Delete Aisle',
    DELETE_DROP_OFF_LOCATION: 'Delete Drop off location',
    REMOVE_WAREHOUSE_EMP: 'Remove warehouse Employee',

    // PURCHASE ORDER (PO Import)
    PO: 'PO Import',
    VIEW_PO: 'View Purchase Order',
    EDIT_PO: 'Edit Purchase Order',
    ADD_PO: 'Add Purchase Order',
    DEL_PO: 'Delete Purchase Order',
    MOVE_TO_PO_CREATED: 'Move To PO Created',
    DEL_PO_ITEM: 'Delete PO Item',

    MOVE_TO_RC_CHINA: 'Move To Receive China',
    VIEW_RC_CHINA: 'View Receive China',
    EDIT_RC_CHINA: 'Edit Receive China',
    DEL_RC_CHINA: 'Delete Receive China',
    MOVE_TO_LOADED: 'Move To Loaded',

    VIEW_LOADED: 'View Loaded',
    MOVE_TO_RELEASED: 'Move To Released',
    VIEW_RELEASED: 'View Released',
    EDIT_CONTAINER_NO: 'Edit Container No',

    VIEW_LOADING_CONTAINER: 'View Loading Container',
    ADD_LOADING_CONTAINERR: 'Add Loading Container',
    EDIT_LOADING_CONTAINER: 'Edit Loading Container',

    VIEW_TEMPO: 'View Tempo',
    ADD_TEMPO: 'Add Tempo',
    EDIT_TEMPO: 'Edit Tempo',
    DELETE_TEMPO: 'Delete Tempo',
    MARK_AS_COMPLETED_TEMPO: 'Mark As Completed Tempo',

    VIEW_GRN: 'View GRN',
    MOVE_TO_COMPLETED: 'Move To Completed',
    VIEW_COMPLETED: 'View Completed',

    // Expenses
    EXPENSES: 'Expenses',
    VIEW_TEMPO_EXP: 'View Tempo Expense',
    VIEW_CONTAINER_EXP: 'View Container Expense',
    DELETE_TEMPO_EXP: 'Delete Tempo Expense',
    DELETE_CONTAINER_EXP: 'Delete Container Expense',
    EDIT_CONTAINER_EXP: 'Edit Container Expense',
    ADD_CONTAINER_EXP: 'Add Container Expense',
    APPROVE_CONTAINER_EXP: 'Approve Container Expense',
    REJECT_CONTAINER_EXP: 'Reject Container Expense',
    EDIT_TEMPO_EXP: 'Edit Tempo Expense',
    ADD_TEMPO_EXP: 'Add Tempo Expense',
    APPROVE_TEMPO_EXP: 'Approve Tempo Expense',
    REJECT_TEMPO_EXP: 'Reject Tempo Expense',

    // PAYMENT
    VIEW_PAYMENT: 'View Payment',
    ADD_PAYMENT: 'Add Payment',
    EDIT_PAYMENT: 'Edit Payment',
    DELETE_PAYMENT: 'Delete Payment',
    APPROVE_PAYMENT: 'Approve Payment',
    REJECT_PAYMENT: 'Reject Payment',

    // AVERAGE PRICE
    AVERAGE_PRICE: 'Average Price',
    VIEW_AVERAGE_PRICE: 'View Average Price',
    APPROVE_AVERAGE_PRICE: 'Approve Average Price',
    SAVE_AVERAGE_PRICE: 'Save Average Price',

    // CUSTOMER LEAD
    CUSTOMER_LEAD: 'Customer Lead',
    VIEW_CUSTOMER_LEAD: 'View Customer Lead',
    ADD_CUSTOMER_LEAD: 'Add Customer Lead',
    EDIT_CUSTOMER_LEAD: 'Edit Customer Lead',
    DELETE_CUSTOMER_LEAD: 'Delete Customer Lead',
    CONVERT_TO_CUSTOMER: 'Convert to Customer',

    // INQUIRY
    INQUIRY: 'Inquiry',
    VIEW_INQUIRY: 'View Inquiry',
    ADD_INQUIRY: 'Add Inquiry',
    DELETE_INQUIRY: 'Delete Inquiry',
    DELETE_IMAGE_INQUIRY: 'Delete Image Inquiry',
    DELETE_MAPPED_IMAGE_INQUIRY: 'Delete Mapped Image',
    LINK_INQUIRY: 'Link Inquiry',
    CONVERT_TO_ITEM: 'Convert to Item',
    APPROVE_SALE_PRICE_FOLLOWUP_DATE: 'Approve Sale Price & Follow-up Date',

    // SALES
    SALES_ORDERS: 'Sales Orders',
    VIEW_SO: 'View Sales Order',
    EXTEND_RELEASE_HOLD: 'Extend & Release Hold',
    EDIT_SO: 'Edit Sales Order',
    ADD_SO: 'Add Sales Order',
    CANCEL_SO: 'Cancel Sales Order',
    DELETE_SO: 'Delete Sales Order',

} as const

import { Component, computed, inject, OnDestroy, OnInit, signal, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { EnumForInquiryType } from '@enums/EnumForInquiryType';
import { EnumForOrderUnit } from '@enums/EnumForOrderUnit.enum';
import { createArraySignal } from '@libs';
import { Attachment, BranchMaster, InquiryAssociatedItemModal, InquiryBranchMarkaModal, InquiryDropdowns, InquiryItemDropdown, MarkaBranchData, MarkaStocks, SalesInquiry, SalesInquiryItem, UpcomingQty, WarehouseStocks } from '@modal/SalesInquiry';
import { UtilsService } from '@service/utils.service';
import { CountdownComponent } from 'ngx-countdown';
import { WebcamImage, WebcamInitError } from 'ngx-webcam';
import { fromEvent, map, Observable, Subject, takeUntil, tap } from 'rxjs';
declare var window: any;

@Component({
  selector: 'app-new-inquiry',
  templateUrl: './new-inquiry.component.html',
  styleUrls: ['./new-inquiry.component.css']
})
export class NewInquiryComponent implements OnInit, OnDestroy {

  dragPosition = {x: 0, y: 0};
  @ViewChild('cd') cd: CountdownComponent;
  timerAction =  '';
  timeInSeconds: number = null;

  utilsService = inject(UtilsService)
  form: FormGroup;
  formGroupLead: FormGroup;
  allInquiryDropdowns: InquiryDropdowns = {
    Country: [],
    NITReason: [],
    customerLead: [],
    hsnCodeMaster: [],
    itemsBelong: [],
    orderUnit: [],
    item: [],
    InquiryIdealTimer: null,
    inquiryItems: []
  }
  allMarkaBranchData: MarkaBranchData = {
    activeTab: null,
    markaDropDown: null,
    upcomingQty: [],
    warehouse: [],
    upcomingCartonQty: null,
    totalUpcomingQty: null
  }

  filteredMarkaList: MarkaStocks[] = []
  filteredUpcomingQtyList: UpcomingQty[] = []

  isUpcomingTab = false;

  searchText: string;
  warehouseIdFilter: number[] = []
  errorsList: any[] = []

  attachmentsList = createArraySignal<Attachment>([])
  webcamImages = createArraySignal<Attachment>([])

  inquiryAssociatedItems: InquiryAssociatedItemModal[] = []
  inquiryAssociatedItemsFlag = false

  webcamImagesEmpty = computed(() => this.webcamImages.get().length === 0)
  isAnyItemInquiryTypeSO = signal(false);

  enumForInquiryType = EnumForInquiryType
  enumForOrderUnit = EnumForOrderUnit

  private destroy$ = new Subject<void>();

  private addCustomerLeadModalI: any;
  private associatedInqItemModal: any;
  private hsnFileErrorInquiryModal: any;
  private webcamModal: any;
  private excelUpload: { fileName: string | null; selectedFile: File | null } = {
    fileName: null,
    selectedFile: null
  }

  isWebModalOpen: boolean = false;
  public webcamImage: WebcamImage = null;
  private trigger: Subject<void> = new Subject<void>();
  private imageIndex: number = 0;

  formArrayItemIndex: number;

  constructor(private fb: FormBuilder) { }

  ngOnInit() {
    this.initForm();
    this.customerLeadForm();

    this.getRequiredData();

    this.addCustomerLeadModalI = new window.bootstrap.Modal(
      document.getElementById('addCustomerLeadModalI')
    );

    this.associatedInqItemModal = new window.bootstrap.Modal(
      document.getElementById('associatedInqItemModal')
    );

    this.webcamModal = new window.bootstrap.Modal(
      document.getElementById('webcamModal')
    );

    this.hsnFileErrorInquiryModal = new window.bootstrap.Modal(
      document.getElementById('hsnFileErrorInquiryModal')
    );

    document.getElementById('webcamModal').addEventListener('hidden.bs.modal', () => {
      this.isWebModalOpen = false;
    });

    document.getElementById('associatedInqItemModal').addEventListener('hidden.bs.modal', () => {
      this.inquiryAssociatedItemsFlag = false;
    });

    this.handleTimerLogic();
    this.onChangeCustomer();
    this.checkIfAnyIteminquiryTypeSO();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  //// Customer LEAD
  customerLeadForm = () => {
    this.formGroupLead = this.fb.group({
      id: [null],
      firstName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      middleName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      lastName: [null, Validators.compose([ Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      email: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      countryId: [null, Validators.compose([Validators.required])],
      phone: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isActive: [true],
    })
  }

  onSaveCustomerLead = () => {
    if (this.formGroupLead.invalid) {
      this.formGroupLead.markAllAsTouched()
      return
    }

    const param = this.formGroupLead.value
    this.utilsService.post(this.utilsService.serverVariableService.CUSTOMER_LEAD_SAVE_EDIT_DELETE, param, { toast: true }).pipe(
      tap(() => {
        this.addCustomerLeadModalI.hide()
        this.getRequiredData(true)
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  openAddCustomerLeadModal = () => {
    this.formGroupLead.reset()
    this.addCustomerLeadModalI.show()

    const indiaCode = this.allInquiryDropdowns.Country.find((item) => item.countryExtension === '+91')

    this.formGroupLead.patchValue({ 
      isActive: true,
      countryId: indiaCode.isActive ? indiaCode?.id : null,
    });
  }

  initForm = () => {
    this.form = this.fb.group({
      customerId: [null, Validators.compose([Validators.required])],
      customerNote: [null],
      countryId: [null, Validators.compose([Validators.required])],
      phone: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      inquiryItems: this.fb.array([]),
    })
    this.addInquiryItem()
  }

  onChangeCustomer = () => {
    this.form.get('customerId')?.valueChanges.pipe(
      tap((value) => {
        const customer = this.allInquiryDropdowns.customerLead.find((item) => item.id === value)
        this.getRequiredData();
        const keys = ['countryId', 'phone']
        keys.forEach((controlName) => {
          this.form.get(controlName)?.enable();
        })

        if (customer?.phone && customer?.countryId) {
          this.form.get('countryId')?.setValue(customer?.countryId)
          this.form.get('phone')?.setValue(customer?.phone?.split(' ')[1] || '');
          keys.forEach((controlName) => {
            this.form.get(controlName)?.disable();
          })
        } else {
          this.form.get('countryId')?.setValue(null)
          this.form.get('phone')?.setValue(null);
        }

        keys.forEach((controlName) => {
          this.form.get(controlName)?.updateValueAndValidity();
        })
        this.enableContinueSalesBtn();
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  get inquiryItems(): FormArray {
    return this.form.get('inquiryItems') as FormArray;
  }

  addInquiryItem = () => {
    this.inquiryItems.push(this.fb.group({
      itemId: [null, Validators.compose([Validators.required])],
      inquiryType: [this.enumForInquiryType.URGENT_INQUIRY],
      reasonId: [null],
      enumsItemsBelong: [null, Validators.compose([Validators.required])],
      enumForOrderUnit: [null, Validators.compose([Validators.required])],
      inquiryQuantity: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      piecePerCarton: [null],
      totalPiece: [null],
      rate: [null],
      isRateNotFound: [null],
      totalAmount: [null],
      salesExecutiveOfferedPrice: [null],
      customerRequestedPrice: [null],
      hsnCodeId: [null],
      gstAmount: [null],
      totalAmountWithGst: [null],
      note: [null],
      hsnDropdown: [null],
      orderUnits : [null],
      taxName: [null],
      gstRate: [null],
      // webcam
      isOnlyImage: [false],
      images: [null],
      itemName: [null],
      firstImage: [null],
      //itemGroupId
      itemGroupId: [null],
      // out of stock flag
      outOfStock: [false],
      lastSalesInfo: [null],
      branchMaster: [null],
      salesManagerPrice : [null]
    }))
  }

  removeInquiryItem = (index: number) => {
    this.inquiryItems.removeAt(index);
  }

  addItemFromListofItem = (items: { requestedPrice: number, sku: string, qty: number }[]) => {
    for (const item of items) {
      this.addInquiryItem();
      const addedItem = this.inquiryItems.at(this.inquiryItems.length - 1)
      const itemWithSku = (this.allInquiryDropdowns.item || []).find(a => a.skuId === item.sku);

      addedItem.patchValue({
        itemId: itemWithSku?.id,
        customerRequestedPrice: item.requestedPrice,
        inquiryQuantity: item.qty,
      });

      if(itemWithSku?.id) {
        this.onItemChange(itemWithSku, this.inquiryItems.length - 1);
      }
    }
    if (items.length === 0) {
      return;
    }
    this.removeEmptyInitialItems();
  }

  removeEmptyInitialItems = () => {
    for (let i = this.inquiryItems.length - 1; i >= 0; i--) {
      const itemName = this.inquiryItems.at(i)?.get('itemName')?.value?.toString().trim();
      const itemId = this.inquiryItems.at(i)?.get('itemId')?.value;

      if (!itemId && (!itemName || itemName === '')) {
        this.removeInquiryItem(i);
      }
    }
  };

  getRequiredData = (fromLead = false) => {
    const customerId = this.form.get('customerId')?.value;
    let API = this.utilsService.serverVariableService.INQUIRY_REQ_DATA;
    if (customerId) {
      API += `?id=${customerId}`
    }
    this.utilsService.get(API).pipe(
      map((res) => res.data),
      tap((dropdown: InquiryDropdowns) => {
        this.allInquiryDropdowns = dropdown;
        for (const key of Object.keys(this.allInquiryDropdowns)) {
          if (key !== 'itemsBelong' && key !== 'orderUnit' && key !== 'item') {
            if (this.allInquiryDropdowns[key] && Array.isArray(this.allInquiryDropdowns[key])) {
              this.allInquiryDropdowns[key] = this.utilsService.transformDropdownItems(this.allInquiryDropdowns[key]);
            }
          }
        }
        this.allInquiryDropdowns.Country = this.utilsService.filterIsActive(this.allInquiryDropdowns.Country, null);
        this.allInquiryDropdowns.customerLead = this.utilsService.filterIsActiveLV(this.allInquiryDropdowns.customerLead, null);
        this.allInquiryDropdowns.NITReason = this.utilsService.filterIsActiveLV(this.allInquiryDropdowns.NITReason, null);
        this.allInquiryDropdowns.hsnCodeMaster = this.utilsService.filterIsActive(this.allInquiryDropdowns.hsnCodeMaster, null);

        if(dropdown?.InquiryIdealTimer) {
          this.timeInSeconds = this.utilsService.convertTimeToSeconds(dropdown.InquiryIdealTimer);
        }

        if(fromLead) {
          this.form.get('customerId')?.setValue(this.allInquiryDropdowns.customerLead[0].id);
          this.onChangeCustomer();
        }

        this.setItemData(this.allInquiryDropdowns);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  get items() {
      return this.form.get('inquiryItems') as FormArray
    }

  setItemData = (res: InquiryDropdowns) => {
    let responseItem = res['inquiryItems'] as SalesInquiryItem[];
    if (!responseItem) {
      return;
    };
    
    this.items.clear();
    for (let index = 0; index < responseItem.length; index++) {
      const item = responseItem[index];
      console.log(item);
      this.addInquiryItem();
          
      setTimeout(() => {
         this.items.at(index)?.patchValue({
              ...item,
              enumForOrderUnit: item.enumForOrderUnit['value'],
              inquiryType : item.inquiryType['value'],
            });
      });
      const itemWithSku = (this.allInquiryDropdowns.item || []).find(a => a.id === item.itemId);
      this.onItemChange(itemWithSku, index);
      setTimeout(() => {
        this.onHsnCodeChange(index);
      });
    }
  }

  onSelectAttachments = (event: any, isWebcam: boolean): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        const allowedExts = isWebcam ? ['jpeg', 'png', 'jpg', 'jfif'] : ['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'];

        if (allowedExts.includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            switch (isWebcam) {
              case true:
                if ((this.webcamImages.get() || []).length >= 10) {
                  this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                    positionClass: 'toast-top-right',
                    closeButton: true,
                    timeOut: 10000
                  });
                  selectedFiles = null;
                  return;
                }
                this.webcamImages.push(fileData);
                break;
              default:
                if ((this.attachmentsList.get() || []).length >= 10) {
                  this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                    positionClass: 'toast-top-right',
                    closeButton: true,
                    timeOut: 10000
                  });
                  selectedFiles = null;
                  return;
                }
                this.attachmentsList.push(fileData);
                break;
            }
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_INVALID_EXTENSION);
        }
      });
    }
  }

  openLink = (link: string, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment = (i: number, isWebcam: boolean) => {
    switch (isWebcam) {
      case true:
        this.webcamImages.removeAt(i);
        break;
      default:
        this.attachmentsList.removeAt(i);
        break;
    }
  }

  onSave = (isSaveOnlyInquiry: boolean) => {

    const formData = new FormData();

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      const invalidItem = this.inquiryItems.controls.find((control) => control.invalid);
      if (invalidItem) {
        const index = this.inquiryItems.controls.indexOf(invalidItem);
        const invalidControl = invalidItem as FormGroup;
        const invalidFieldName = Object.keys(invalidControl.controls).find(key =>
          invalidControl.get(key)?.invalid
        );

        if (invalidFieldName) {
          let targetElement = document.getElementById(`${invalidFieldName}-${index}`);
          if (!targetElement) {
            targetElement = document.querySelector(`[formarrayname="inquiryItems"] > div:nth-child(${index + 1})`);
          }

          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'auto',
              block: 'nearest',
              inline: 'center'
            });
          }
        }
      }
      return;
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.inquiryItems?.length === 0)) {
      this.utilsService.toasterService.error('Minimum One Item is required.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (!this.utilsService.isEverythingUniqueMultiple(this.inquiryItems.value, 'itemId', 'enumForOrderUnit')) {
      this.utilsService.toasterService.error(this.utilsService.validationService.SAME_ITEM_SAME_ORDER_UNIT, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if ((this.attachmentsList.get() || []).length > 0) {
      for (const file of this.attachmentsList.get() || []) {
        if (file.file) {
          formData.append('inquiryDocs', file.file);
        }
      }
    }

    const param: SalesInquiry = {
      ...this.form.value,
      countryId: this.form.get('countryId')?.getRawValue(),
      phone: this.form.get('phone')?.getRawValue(),
      inquiryItems: this.form.value.inquiryItems.map((item: SalesInquiryItem) => {
        const obj = {
          ...item,
          inquiryItemName: item.itemName,
        }
        const finalObj = this.utilsService.removeKeys(obj, ['hsnDropdown', 'taxName', 'images', 'firstImage', 'isOnlyImage', 'itemName', 'itemGroupId', 'lastSalesInfo']);
        return finalObj;
      })
    }

    let docMapReq: { label: string; value: number[] }[] = [];
    let globalIndex = 0;

    this.inquiryItems.controls.forEach((group: FormGroup) => {
      const itemName = group.get('itemName')?.value;
      const images = group.get('images')?.value || [];

      const indexes: number[] = [];

      for (const index in images) {
        const img: Attachment = images[index];
        if (img?.file) {
          formData.append('inquiryItemDocs', img.file);
          indexes.push(globalIndex);
          globalIndex++;
        }
      }

      if (indexes.length > 0) {
        docMapReq.push({
          label: itemName,
          value: indexes,
        });
      }
    });

    formData.set('docMapReq', JSON.stringify(docMapReq));
    formData.set('inquiryInfo', JSON.stringify(param));

    this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_SAVE_EDIT_DELETE, formData, { toast: true }).pipe(
      map((res: any) => res?.data),
      tap((data: any) => {
        if (isSaveOnlyInquiry) {
          this.utilsService.redirectTo('/users/sales/sales-orders')
        } else {
          this.utilsService.redirectTo(`/users/sales/sales-orders/new-sales-order/${data?.id}/customer/${data.customerId}`)
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  /// on Change item
  onItemChange = (selectedItem: InquiryItemDropdown, index: number) => {

    for (const key of ['note', 'customerRequestedPrice', 'salesExecutiveOfferedPrice', 'inquiryType', 'reasonId', 'enumsItemsBelong', 'enumForOrderUnit', 'taxName', 'gstRate', 'gstAmount', 'totalAmountWithGst', 
                      'hsnCodeId', 'rate', 'itemGroupId', 'totalPiece', 'inquiryQuantity', 'piecePerCarton', 'branchMaster', 'lastSalesInfo']) {
      this.inquiryItems.at(index).get(key)?.setValue(null);
    }
    this.inquiryItems.at(index).get('inquiryType')?.setValue(this.enumForInquiryType.URGENT_INQUIRY);

    this.onOrderUnitChange(index);

    const hsnDropdown = this.allInquiryDropdowns?.item?.find(item => item.id === selectedItem?.id)?.hsnCodes || [];
    const orderUnits = this.allInquiryDropdowns?.item?.find(item => item.id === selectedItem?.id)?.orderUnits || [];
    this.inquiryItems.at(index).patchValue({
      hsnDropdown, orderUnits,
      outOfStock: selectedItem.outOfStock,
      itemGroupId: selectedItem?.itemGroupId || null,
      lastSalesInfo: selectedItem?.lastSalesInfo || null,
    });

    ['rate', 'salesExecutiveOfferedPrice', 'customerRequestedPrice'].forEach(key => {
      this.inquiryItems.at(index).get(key)?.addValidators([Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])]);
      this.inquiryItems.at(index).get(key)?.updateValueAndValidity();
    });

    if (!this.utilsService.isEmptyObjectOrNullUndefined(selectedItem)) {
      this.inquiryItems.at(index).get('branchMaster')?.setValue(selectedItem?.branchMaster || []);
      this.allMarkaBranchData.activeTab = 0;
      this.onChangeInquiryQty(index);
    }
  }

  // on change inquiry type
  onChangeInquiryType = (index: number) => {
    const value = this.inquiryItems.at(index)?.get('inquiryType')?.value;
    switch (value) {
      case this.enumForInquiryType.NIT:
        this.inquiryItems.at(index).get('reasonId')?.addValidators([Validators.required]);
        break;
      default:
        this.inquiryItems.at(index).get('reasonId')?.clearValidators();
        break;
    }
    this.inquiryItems.at(index).get('reasonId')?.setValue(null);
    this.inquiryItems.at(index).get('reasonId')?.updateValueAndValidity();
  }

  // on change order unit
  onOrderUnitChange = (index: number) => {
    const value = this.inquiryItems.at(index)?.get('enumForOrderUnit')?.value;
    switch (value) {
      case this.enumForOrderUnit.CARTON:
        this.inquiryItems.at(index).get('piecePerCarton')?.enable();
        break;
      default:
        this.inquiryItems.at(index).get('piecePerCarton')?.disable();
        break;
    }

    const itemId = this.inquiryItems.at(index)?.get('itemId')?.value;
    const item = this.allInquiryDropdowns?.item?.find(item => item.id === itemId) || null;
    const rate = value === this.enumForOrderUnit.LOOSE ? (item?.itemPrice || 0) : (item?.cartonPrice || 0);

    let rateNotFound = false;
    if (rate === 0) {
      rateNotFound = true;
    }
    this.inquiryItems.at(index).get('isRateNotFound')?.setValue(rateNotFound);

    this.inquiryItems.at(index).get('rate')?.setValue(rate || null);

    this.inquiryItems.at(index).get('outOfStock')?.setValue(item?.outOfStock || false);
    this.inquiryItems.at(index).get('piecePerCarton')?.setValue(null);
    this.inquiryItems.at(index).get('piecePerCarton')?.updateValueAndValidity();
    this.onChangeInquiryQty(index);
  }

  onChangeInquiryQty = (index: number) => {
    this.updateItemCalculation(index, 'totalPiece');
    this.updateItemCalculation(index, 'totalAmount');
    this.updateItemCalculation(index, 'hsnCodeId');
  }

  updateItemCalculation = (index: number, type: 'totalPiece' | 'totalAmount' | 'hsnCodeId') => {
    const item = this.inquiryItems.at(index);
    if (!item) return;

    switch (type) {
      case 'totalPiece': {
        const inquiryQuantity = +item.get('inquiryQuantity')?.value || 0;
        const piecePerCarton = item.get('piecePerCarton')?.value;
        const totalPiece = piecePerCarton != null ? inquiryQuantity * +piecePerCarton : inquiryQuantity;

        item.get('totalPiece')?.setValue(totalPiece);
        item.get('totalPiece')?.updateValueAndValidity();
        break;
      }

      case 'totalAmount': {
        const totalPiece = +item.get('totalPiece')?.value || 0;
        const rate = +item.get('rate')?.value || 0;
        const totalAmount = totalPiece * rate;

        item.get('totalAmount')?.setValue(totalAmount);
        item.get('totalAmount')?.updateValueAndValidity();
        break;
      }

      case 'hsnCodeId': {
        const value = this.inquiryItems.at(index)?.get('hsnCodeId')?.value;
        const hsnDropdown = this.inquiryItems.at(index)?.get('hsnDropdown')?.value;
        const dropdown = hsnDropdown?.find((item: any) => item.id === value) || null;
        this.inquiryItems.at(index).get('taxName')?.setValue(dropdown?.taxName);
        this.inquiryItems.at(index).get('gstRate')?.setValue(dropdown?.rate || null);

        const totalAmount = +item.get('totalAmount')?.value || 0;
        const gstRate = +item.get('gstRate')?.value || 0;
        const gstAmount = isFinite(totalAmount) && isFinite(gstRate) ? totalAmount * gstRate / 100 : 0;
        const totalAmountWithGst = totalAmount + gstAmount;

        item.get('gstAmount')?.setValue(gstAmount);
        item.get('gstAmount')?.updateValueAndValidity();
        item.get('totalAmountWithGst')?.setValue(totalAmountWithGst);
        item.get('totalAmountWithGst')?.updateValueAndValidity();
        break;
      }
    }
  };

  // on Change hsn code
  onHsnCodeChange = (index: number) => {
    this.updateItemCalculation(index, 'hsnCodeId');
  }

  onSelectExcel = (event: any): void => {
    if (event.target.files && event.target.files[0]) {
      this.excelUpload = null;
      const reader = new FileReader();
      const max_file_size = 5 * 1024 * 1024; // 5 MB
      reader.readAsDataURL(event.target.files[0]);
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();
        const allowedExtensions = ['csv'];
        const isExcelFile = allowedExtensions.includes(ext1);

        if (!isExcelFile) {
          this.utilsService.toasterService.error(this.utilsService.validationService.UPLOAD_VALID_FILE, '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          return;
        }

        if (selectedFile.size > max_file_size) {
          this.utilsService.toasterService.error(this.utilsService.validationService.EXCEL_MAX_FILE, '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          return;
        }

        this.excelUpload = {
          fileName: selectedFile.name,
          selectedFile
        };
        this.onUpload();
      }

    }
  }

  // Upload CSV api
  onUpload = () => {
    const formData = new FormData();
    formData.set('csvFile', this.excelUpload?.selectedFile);

    this.utilsService.post(this.utilsService.serverVariableService.INQUIRY_UPLOAD_CSV, formData).pipe(
      map((res) => res?.data),
      tap((data: any) => {
        this.errorsList = [];
        this.errorsList = data?.invalidData || []

        if(this.errorsList?.length > 0) {
          this.openCsvErrorModal();
        }

        if(data?.validData) {
          this.addItemFromListofItem(data?.validData);
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // Webcam modal integration

  handleInitError = (error: WebcamInitError): void => {
    if (error.mediaStreamError && error.mediaStreamError.name === "NotAllowedError") {
      window.alert("Camera access was not allowed by user!");
    }
  }

  openWebcamModal = (images: Attachment[], index: number) => {
    this.formArrayItemIndex = index ?? null;
    this.webcamImages.set([]);
    this.isWebModalOpen = true;

    if(images?.length > 0) {
      this.webcamImages.set(images);
    }

    this.webcamModal.show()
  }

  get triggerObservable(): Observable<void> {
    return this.trigger.asObservable();
  }

  takePhoto = (webcamImage: WebcamImage) => {
    this.webcamImage = webcamImage;
  }

  triggerSnapshot = (): void => {
    this.trigger.next();
    const file = this.utilsService.base64ImageToFile(this.webcamImage.imageAsBase64, `Image_${this.imageIndex}`);
    let fileData = null;
    const fileUrl = URL.createObjectURL(file);
    fileData = {
      id: null,
      file: file,
      originalName: file.name,
      formattedName: fileUrl,
    };
    if ((this.webcamImages.get() || []).length >= 10) {
      this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }
    this.webcamImages.push(fileData);
    this.imageIndex++;
  }

  onSaveWebcamImages = () => {
    const images = this.webcamImages.get() || [];
  
    if (images.length > 0) {
      const existingCount = this.inquiryItems.controls.filter(ctrl => {
        const name = ctrl.get('itemName')?.value || '';
        return /^New Item \d+$/.test(name);
      }).length;
  
      const newItemName = `New Item ${existingCount + 1}`;
      let item: FormGroup;
  
      if (this.formArrayItemIndex != null) {
        item = this.inquiryItems.at(this.formArrayItemIndex) as FormGroup;
      } else {
        this.addInquiryItem();
        item = this.inquiryItems.at(this.inquiryItems.length - 1) as FormGroup;
      }
  
      item.patchValue({
        isOnlyImage: true,
        images: images,
        itemName: this.formArrayItemIndex != null ? item.get('itemName')?.value : newItemName,
        firstImage: images[0]?.formattedName,
      });
      
      ['rate', 'itemId', 'salesExecutiveOfferedPrice', 'customerRequestedPrice'].forEach(key => {
        item.get(key)?.clearValidators();
        if(key === 'rate' || key === 'salesExecutiveOfferedPrice' || key === 'customerRequestedPrice') {
          item.get(key)?.addValidators(Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)]));
        }
        item.get(key)?.updateValueAndValidity();
      });
      this.removeEmptyInitialItems();
    }
  
    this.webcamModal.hide();
    this.imageIndex = 0;
    setTimeout(() => {
      this.webcamImages.set([]);
    }, 50);
  };


  // Timer
  handleEvent = (event: any) => {
    this.timerAction = event.action
    if(event.action === 'done') {
      window.location.reload();
    }
  }

  handleTimerLogic = () => {
    this.form.valueChanges.pipe(
      tap(() => {
        this.cd?.restart()
      }),
      takeUntil(this.destroy$)
    ).subscribe();

    // fromEvent<KeyboardEvent>(window, 'keydown').pipe(
    //   tap(() => {
    //     this.cd.restart()
    //   }),
    //   takeUntil(this.destroy$)
    // ).subscribe();
  }

  // Sales price validation
  salesPriceValidation: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    let salesPrice = group.get('salesExecutiveOfferedPrice')?.value;
    let rate = group.get('rate')?.value
    return salesPrice < rate ? { salesPriceLessThanRate: true } : null
  }

  // Open Associated(Inq grp code) modal
  openAssociatedInqItemModal = (index: number) => {

    this.inquiryAssociatedItems = []
    if (!this.inquiryItems.at(index).get('itemGroupId')?.value) {
      this.utilsService.toasterService.error('Item Group not found', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    const API = `${this.utilsService.serverVariableService.INQUIRY_GROUP_CODE_ASSOCIATED_ITEMS}?itemGroupCodeId=${this.inquiryItems.at(index).get('itemGroupId')?.value}`
    this.associatedInqItemModal.show()
    this.inquiryAssociatedItemsFlag = true;

    this.utilsService.get(API).pipe(
      map((res) => res?.data),
      map((linkedItems => linkedItems?.linkedItems)),
      tap((data: InquiryAssociatedItemModal[]) => {
        this.inquiryAssociatedItems = data;
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  // Open Marka Popup

  calculateMarkaStockSums(markaStocks: MarkaStocks[]): MarkaStocks[] {
    return markaStocks.map(marka => {
      const sumTotalCartonQty = marka.warehouseStocks.reduce((sum, ws) => sum + (ws.totalCartonQty || 0), 0);
      const sumTotalQty = marka.warehouseStocks.reduce((sum, ws) => sum + (ws.totalQty || 0), 0);
      const sumCartonPcs = marka.warehouseStocks.reduce((sum, ws) => sum + (ws.pcsPerCarton || 0), 0);

      return { ...marka, sumTotalCartonQty, sumTotalQty, sumCartonPcs };
    });
  }

  openMarkaPopup = (index: number, isOnlyUpcoming: boolean) => {

    this.changePosition();
    const branchMaster = this.inquiryItems.at(index).get('branchMaster')?.value;

    this.isUpcomingTab = isOnlyUpcoming;
    this.resetMarkaModalFilters();

    const itemId = this.inquiryItems.at(index).get('itemId')?.value as number;

    const isBranchMasterEmpty = (branchMaster || []).length === 0;
    if (isBranchMasterEmpty) {
      this.isUpcomingTab = true;
    }

    let branchId = null;
    let API = null;

    if (!this.isUpcomingTab && !isBranchMasterEmpty) {
      branchId = branchMaster[this.allMarkaBranchData.activeTab !== null ? this.allMarkaBranchData.activeTab : 0].id;
      API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&branchId=${branchId}&isUpComingQty=${this.isUpcomingTab}`;
    } else {
      API = `${this.utilsService.serverVariableService.INQUIRY_MARKA_STOCK_QUANTITY}?itemId=${itemId}&isUpComingQty=${this.isUpcomingTab}`;
    }

    this.utilsService.get(API).pipe(
      map((res) => res?.data),
      tap((data: any) => {
        this.allMarkaBranchData.markaDropDown = data['markaDropDown'] ?? null
        this.allMarkaBranchData.upcomingQty = data?.['upComingQty']?.['upComingQty'] ?? []
        this.allMarkaBranchData.warehouse = data['warehouse'] ?? []
        this.allMarkaBranchData.upcomingCartonQty = data?.['upComingQty']?.['cartonQty'] ?? 0
        this.allMarkaBranchData.totalUpcomingQty = data?.['upComingQty']?.['totalQty'] ?? 0

        if (this.allMarkaBranchData.markaDropDown?.markaStocks?.length > 0) {
          this.allMarkaBranchData.markaDropDown.markaStocks = this.calculateMarkaStockSums(this.allMarkaBranchData.markaDropDown.markaStocks)
        }
        this.filtersMarkaModal(null, null)
        this.filterUpcomingQty(null, null)

        if (this.isUpcomingTab) {
          this.allMarkaBranchData.activeTab = null
          this.resetMarkaModalFilters();
        } else {
          this.changeBranchTab(index, this.allMarkaBranchData.activeTab !== null ? this.allMarkaBranchData.activeTab : 0, true);
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  changeBranchTab = (itemIndex: number, activeTab: number, firstTimeCall: boolean) => {

    if(this.allMarkaBranchData.activeTab === activeTab) {
      return;
    }
    this.allMarkaBranchData.markaDropDown = {} as InquiryBranchMarkaModal;

    this.resetMarkaModalFilters();

    this.allMarkaBranchData.activeTab = firstTimeCall ? 0 : activeTab;
    this.openMarkaPopup(itemIndex, false);
  }

  changeToUpcomingTab = (index: number) => {
    this.openMarkaPopup(index, true);
  }

  resetMarkaModalFilters = () => {
    this.searchText = null;
    this.warehouseIdFilter = [];
  }

  clearWarhouseId = () => {
    this.warehouseIdFilter = [];
  }

  getTotalLocationsCount(aisles: WarehouseStocks[]): number {
    return aisles.reduce((total, aisle) => total + aisle.aisleStocks.length, 0);
  }

  filtersMarkaModal(searchType: 'marka' | 'warehouseId', searchValue: string | number[]) {
    let filteredResults = [];

    if (!searchValue || (Array.isArray(searchValue) && searchValue.length === 0)) {
      this.filteredMarkaList = (this.allMarkaBranchData.markaDropDown?.markaStocks || []);
      return;
    }

    if (searchType === 'marka') {
      filteredResults = (this.allMarkaBranchData.markaDropDown?.markaStocks || []).filter(markaStock =>
        markaStock.marka.toLowerCase().includes(searchValue.toString().toLowerCase())
      );
    }

    else if (searchType === 'warehouseId') {
      filteredResults = (this.allMarkaBranchData.markaDropDown?.markaStocks || []).filter(markaStock =>
        markaStock.warehouseStocks.some(warehouseStock =>
          Array.isArray(searchValue) ? searchValue.includes(warehouseStock.warehouseId) : false
        )
      );
    }
    this.filteredMarkaList = filteredResults;
  }
  
  filterUpcomingQty(searchType: 'marka', searchValue: string) {
    let filteredResults = [];

    if (!searchValue) {
      this.filteredUpcomingQtyList = (this.allMarkaBranchData.upcomingQty || []);
      return;
    }

    if (searchType === 'marka') {
      filteredResults = (this.allMarkaBranchData.upcomingQty || []).filter(upcomingQty =>
        upcomingQty.marka.toLowerCase().includes(searchValue.toString().toLowerCase())
      );
    }
    this.filteredUpcomingQtyList = filteredResults;
  }

  openCsvErrorModal = () => {
    this.hsnFileErrorInquiryModal.show()
  }

  checkIfAnyIteminquiryTypeSO = () => {
   this.inquiryItems.valueChanges.pipe(
    tap((value) => {
      this.enableContinueSalesBtn();
    }),
    takeUntil(this.destroy$)
   ).subscribe()
  }
  
  enableContinueSalesBtn = () => {
      const findCustomer = this.allInquiryDropdowns.customerLead.find(item => item.id === this.form.get('customerId')?.value);
      const customerId = findCustomer?.registrationTypeName != 'LEAD_CUSTOMER';
      const inquiryTypeSO = this.inquiryItems.value.some(item => item.inquiryType === EnumForInquiryType.SO_INQUIRY) && customerId;
      this.isAnyItemInquiryTypeSO.set(inquiryTypeSO);
  }

  customSearchFn(term: string, item: InquiryItemDropdown) {
    const lowerCaseTerm = term.toLocaleLowerCase();
    return item?.skuId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
  }

  getGrnGroupLink(grnGroupLinkId: number): string {
    const origin = window.location.origin;
    const baseHref = document.querySelector('base')?.getAttribute('href') || '/';
  
    // Ensure base does not end with a slash
    const base = baseHref.endsWith('/') ? baseHref.slice(0, -1) : baseHref;
  
    return `${origin}${base}/#/marka-image/${grnGroupLinkId}`;
  }  

  onCopy(grnGroupLinkId: number) {
    if(grnGroupLinkId){
      this.utilsService.toasterService.success('Copied To Clipboard', '', { positionClass: 'toast-top-right', closeButton: true, timeOut: 2000 });
    }
  }

  changePosition() {
    this.dragPosition = {x: this.dragPosition.x, y: this.dragPosition.y};
  }
  
}

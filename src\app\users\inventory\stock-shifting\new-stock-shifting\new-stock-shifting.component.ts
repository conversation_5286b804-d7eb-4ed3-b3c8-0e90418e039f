import { Component, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { createArraySignal, FormControlsOf } from '@libs';
import { AttachmentStockShifting, AvailableQtyRes, LocationType, StockShiftingAssociateItemReqList, StockShiftingItemDropdown, StockShiftingReq, StockShiftingSaveDTO } from '@modal/StockShifting';
import { UtilsService } from '@service/utils.service';
import moment from 'moment';
import { map, Subject, takeUntil, tap } from 'rxjs';
declare var window: any;

type StockShiftingFormControls = FormControlsOf<StockShiftingSaveDTO>;
type AssociateItemFormControls = FormControlsOf<StockShiftingAssociateItemReqList>;

@Component({
  selector: 'app-new-stock-shifting',
  templateUrl: './new-stock-shifting.component.html',
  styleUrls: ['./new-stock-shifting.component.css']
})
export class NewStockShiftingComponent implements OnInit, OnDestroy {

  utilsService = inject(UtilsService)
  moment = moment;

  dropdown: StockShiftingReq = {
    items: [],
    reason: [],
    user: [],
    warehouse: [],
    aisle: [],
    rack: [],
  }

  markaData: { data: AvailableQtyRes[], arrayIndex: number }[] = []

  form: FormGroup<StockShiftingFormControls>;

  attachmentsList = createArraySignal<AttachmentStockShifting>([])
  selectedItemIndex = signal<number>(null);
  itemName = signal('');

  locationType = LocationType;
  oldWarehouseId: number;

  private destroy$ = new Subject<void>();
  private stockShiftingDeleteItemModal: any;
  private warehouseShiftChangeModal: any;


  private availableQtyCache = new Map<string, AvailableQtyRes[]>();

  constructor(private fb: FormBuilder) { }

  ngOnInit() {
    this.getRequiredData();
    this.initForm();

    this.stockShiftingDeleteItemModal = new window.bootstrap.Modal(
      document.getElementById('stockShiftingDeleteItemModal')
    );

    this.warehouseShiftChangeModal = new window.bootstrap.Modal(
      document.getElementById('warehouseShiftChangeModal')
    );

    document.getElementById('warehouseShiftChangeModal').addEventListener('hidden.bs.modal', () => {
      this.form.get('warehouseId').setValue(this.oldWarehouseId, { emitEvent: false })
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initForm = () => {
    this.form = this.fb.group<StockShiftingFormControls>({
      shiftingDate: this.fb.control(null, Validators.compose([Validators.required])),
      reasonMasterId: this.fb.control(null, Validators.compose([Validators.required])),
      note: this.fb.control(null, Validators.compose([Validators.required])),
      assignTo: this.fb.control(null),
      reportTo: this.fb.control(null),
      warehouseId: this.fb.control(null, Validators.compose([Validators.required])),
      associateItemReqList: this.fb.array<FormGroup<AssociateItemFormControls>>([]),
    }, { validators: this.checkAssignToReportTo })

    this.form.patchValue({
      shiftingDate: moment().format('YYYY-MM-DD'),
      reportTo: this.utilsService.userId
    })
  }

  captureOldValue = () => {
    this.oldWarehouseId = this.form.get('warehouseId').value
  }

  onChangeWarehouse = () => {
    if (this.items.length > 0 && this.dropdown.aisle.length > 0) {
      this.warehouseShiftChangeModal.show();
    } else {
      if (this.form.get('warehouseId').value) {
        this.getAisleByWarehouseOrItem(this.form.get('warehouseId').value, null)
        this.getRequiredData(this.form.get('warehouseId').value)
      }
    }
  }

  warehouseChanged = () => {
    this.items.clear();
    this.warehouseShiftChangeModal.hide();
    this.oldWarehouseId = this.form.get('warehouseId').value
    // this.addItems()
    this.getRequiredData(this.oldWarehouseId)
    this.getAisleByWarehouseOrItem(this.form.get('warehouseId').value, null)
  }

  get items(): FormArray<FormGroup<AssociateItemFormControls>> {
    return this.form.get('associateItemReqList') as FormArray<FormGroup<AssociateItemFormControls>>;
  }

  addItems = () => {
    this.items.push(this.fb.group({
      itemDropdown: this.fb.control([]),
      qty: this.fb.control(null, [Validators.required]),
      fromLocationId: this.fb.control(null),
      fromLocationType: this.fb.control(null),
      toLocationId: this.fb.control(null),
      toLocationType: this.fb.control(null),
      itemId: this.fb.control(null, [Validators.required]),
      aisleId: this.fb.control(null, [Validators.required]),
      rackId: this.fb.control(null),
      aisleList: this.fb.control([]),
      cLrackList: this.fb.control([]),
      tLrackList: this.fb.control([]),
      aisleIdTo: this.fb.control(null, [Validators.required]),
      rackIdTo: this.fb.control(null),
      looseQty: this.fb.control(null),
      cartonQty: this.fb.control(null),
      isMarkaSelected: this.fb.control(false)
    }) as FormGroup<AssociateItemFormControls>)

    this.items.at(this.items.length - 1)?.get('itemDropdown')?.patchValue(this.dropdown.items || [])
  }

  openRemoveStockShiftingItemModal(index: number) {
    const itemName = this.dropdown.items.find((a) => a.id === this.items.at(index)?.get('itemId')?.value)?.displayName;
    this.itemName.set(itemName ?? null);
    this.selectedItemIndex.set(index);
    this.stockShiftingDeleteItemModal.show();
  }

  removeStockShiftingItem() {
    this.items.removeAt(this.selectedItemIndex());
    this.stockShiftingDeleteItemModal.hide();
  }

  getRequiredData = (warehouseId?: number) => {

    let API = ""
    if (warehouseId) {
      API = this.utilsService.serverVariableService.REQ_STOCK_SHIFTING_DATA + `?warehouseId=${warehouseId}`
    } else {
      API = this.utilsService.serverVariableService.REQ_STOCK_SHIFTING_DATA
    }

    this.utilsService.get(API).pipe(
      map((res) => res?.data),
      tap((data: StockShiftingReq) => {
        this.dropdown.items = (data.items || [])
        this.dropdown.reason = this.utilsService.transformDropdownItems(data.reason || [])
        this.dropdown.user = this.utilsService.transformDropdownItems(data.user || [])
        this.dropdown.warehouse = this.utilsService.transformDropdownItems(data.warehouse || [])

        this.dropdown.reason = this.utilsService.filterIsActiveLV(this.dropdown?.reason, null);
        this.dropdown.user = this.utilsService.filterIsActiveLV(this.dropdown?.user, null);
        this.dropdown.warehouse = this.utilsService.filterIsActiveLV(this.dropdown?.warehouse, null);
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  getAisleByWarehouseOrItem = (warehouseId: number, itemId?: number, index?: number) => {

    let API = "";
    if(itemId) {
      API = `${this.utilsService.serverVariableService.GET_ALISE_RACK_OF_ITEM_IN_WAREHOUSE}?warehouseId=${warehouseId}&itemId=${itemId}`
    } else {
      API = `${this.utilsService.serverVariableService.GET_ALL_AISLE_DROPDOWN_DATA_BY_WAREHOUSE}?warehouseId=${warehouseId}`
    }

    this.utilsService.get(API).pipe(
      map((res) => res?.data),
      tap((data: any[]) => {
        if(!itemId) {
          this.dropdown.aisle = this.utilsService.transformDropdownItems(data || [])
          this.dropdown.aisle = this.utilsService.filterIsActiveLV(this.dropdown?.aisle, null);
        } else {
          let aisLeDropdown = this.utilsService.transformDropdownItems(data || [])
          aisLeDropdown = this.utilsService.filterIsActiveLV(aisLeDropdown, null);
          this.items.at(index)?.get('aisleList')?.patchValue(aisLeDropdown)
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  // Attachment

  onSelectAttachments = (event: any): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        const allowedExts = ['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'];

        if (allowedExts.includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if ((this.attachmentsList.get() || []).length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }
            this.attachmentsList.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_INVALID_EXTENSION);
        }
      });
    }
  }

  openLink = (link: string, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment = (i: number) => {
    this.attachmentsList.removeAt(i)
  }

  // dropdown search
  customSearchFn = (term: string, item: StockShiftingItemDropdown) => {
    const lowerCaseTerm = term.toLocaleLowerCase();
    return item?.skuId?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1 ||
      item?.displayName?.toLocaleLowerCase().indexOf(lowerCaseTerm) > -1;
  }

  // aisle change
  onChangeAisle = (index: number, isCurrentLocation: boolean, aisleList?: any) => {

    const key = isCurrentLocation ? 'aisleId' : 'aisleIdTo'
    const aisleId = this.items.at(index)?.get(key)?.value
    if (aisleId) {
      if (isCurrentLocation) {
        this.items.at(index)?.patchValue({
          cLrackList: aisleList?.sub,
          aisleId: aisleId
        })
        const arr = isCurrentLocation ? ['rackId'] : ['rackIdTo']
        arr.forEach((controlName) => {
          this.items.at(index)?.get(controlName)?.reset()
          this.items.at(index)?.get(controlName)?.clearValidators()
        })
        if (aisleList?.sub?.length > 0) {
          this.items.at(index)?.get('rackId')?.setValidators([Validators.required])
        }
        arr.forEach((controlName) => {
          this.items.at(index)?.get(controlName)?.updateValueAndValidity()
        })
      } else {
        this.getRackByAisle(aisleId, isCurrentLocation, index)

      }
    }
    else {
      const arr = isCurrentLocation ? ['rackId'] : ['rackIdTo']
      arr.forEach((controlName) => {
        this.items.at(index)?.get(controlName)?.reset()
        this.items.at(index)?.get(controlName)?.clearValidators()
        this.items.at(index)?.get(controlName)?.updateValueAndValidity()
      })
    }
    if(isCurrentLocation) {
      this.commonCacheClear(null, index);
    }
  }

  // Get rack by aisle
  getRackByAisle = (aisleId: number, isCurrentLocation: boolean, index: number) => {
    const API = `${this.utilsService.serverVariableService.GET_ALL_RACK_DROPDOWN_DATA_BY_AISLE}?aisleId=${aisleId}`

    this.utilsService.get(API).pipe(
      map((res) => res?.data),
      tap((data: any[]) => {

        const arr = isCurrentLocation ? ['rackId'] : ['rackIdTo']

        arr.forEach((controlName) => {
          this.items.at(index)?.get(controlName)?.reset()
          this.items.at(index)?.get(controlName)?.clearValidators()
        })

        if (!isCurrentLocation) {
          let tLrackList = this.utilsService.transformDropdownItems(data || [])
          tLrackList = this.utilsService.filterIsActiveLV(tLrackList, null)
          this.items.at(index)?.get('tLrackList')?.setValue(tLrackList)
          if (data?.length > 0) {
            this.items.at(index)?.get('rackIdTo')?.setValidators([Validators.required])
          }
        }

        arr.forEach((controlName) => {
          this.items.at(index)?.get(controlName)?.updateValueAndValidity()
        })

      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  // Called when marka modal opened
  getAvailableQty = (index: number) => {
    const rackId = this.items.at(index)?.get('rackId')?.value;
    const aisleId = this.items.at(index)?.get('aisleId')?.value;
    const itemId = this.items.at(index)?.get('itemId')?.value;

    let fromLocationId = rackId ? rackId : aisleId;
    let fromLocationType = rackId ? this.locationType.RACK : this.locationType.AISLE;

    // Cache key with item, location ids, index
    const cacheKey = `${itemId}-${fromLocationType}-${fromLocationId}-${index}`;

    // If cache exists, return, no api calling
    if (this.availableQtyCache.has(cacheKey)) {
      this.markaData[index] = {
        data: this.availableQtyCache.get(cacheKey)!,
        arrayIndex: index
      };
      return;
    }

    const param = { fromLocationId, fromLocationType, itemId };

    this.utilsService.post(this.utilsService.serverVariableService.AVAILABLE_QTY_OF_ITEM_AT_LOCATION, param).pipe(
      map((res) => res?.data),
      tap((data: AvailableQtyRes[]) => {
        const cartonQtySum = data.reduce((acc, v) => acc + Number(v.cartonQty || 0), 0);
        const looseQtySum = data.reduce((acc, v) => acc + Number(v.looseQty || 0), 0);
        const res: AvailableQtyRes[] = data.map(v => ({
          ...v,
          cartonQtySum: cartonQtySum || 0,
          looseQtySum: looseQtySum || 0,
          isLoose: v.looseQty > 0,
          qtyCartonSum: 0,
          itemIndex: index
        }));

        this.availableQtyCache.set(cacheKey, res);

        this.markaData[index] = { data: res, arrayIndex: index };
        console.warn(this.markaData[index], index);
        console.warn(cartonQtySum, looseQtySum)
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  };

  // Disable save button if any item is invalid
  disableIfInvalid = () => {
    return this.markaData.some((group, i) =>
      group.data.some((item, j) => {
        const cartonInvalid = this.isQtyInvalid(i, j, false);
        const looseInvalid = item.looseQty ? this.isQtyInvalid(i, j, true) : false;
        return cartonInvalid || looseInvalid;
      })
    );
  }

  onSave = () => {

    const formData = new FormData();

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return
    }

    if ((this.attachmentsList.get() || []).length > 0) {
      for (const file of this.attachmentsList.get() || []) {
        if (file.file) {
          formData.append('file', file.file);
        }
      }
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.items?.length === 0)) {
      this.utilsService.toasterService.error('Minimum One Item is required.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    // Check if 5 keys same or not
    if (!this.utilsService.isEverythingSameDynamic(this.items.value, ['itemId', 'aisleId', 'rackId', 'aisleIdTo', 'rackIdTo'])) {
      this.utilsService.toasterService.error(this.utilsService.validationService.SAME_ITEM_SAME_LOCATION, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    // Check if marka selected or carton/loose qty entered
    for (let i = 0; i < this.items.length; i++) {
      const isMarkaSelected = this.items.at(i)?.get('isMarkaSelected')?.value;
      const cartonQty = this.items.at(i)?.get('cartonQty')?.value;
      const looseQty = this.items.at(i)?.get('looseQty')?.value;

      if (!isMarkaSelected && (!cartonQty && !looseQty)) {
        this.utilsService.toasterService.error(this.utilsService.validationService.MARKA_NOT_SELECTED_EITHER_CARTON_LOOSE_QTY_REQUIRED, '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }
    }

    const param = {
      ...this.form.value,
      associateItemReqList: this.form.get('associateItemReqList')?.value.map((v, arrayIndex) => {
        const markaRes = (this.markaData[arrayIndex]?.data || []).filter(m => m.qtyCarton > 0 || m.qtyLoose > 0);
        const cartonQty = v.isMarkaSelected ? markaRes.reduce((acc, curr) => acc + (curr.qtyCarton || 0), 0) : v.cartonQty;
        const looseQty = v.isMarkaSelected ? markaRes.reduce((acc, curr) => acc + (curr.qtyLoose || 0), 0) : v.looseQty;
        console.warn(markaRes)
        console.warn(cartonQty, looseQty)
        return {
          itemId: v.itemId,
          qty: v.qty || null,
          fromLocationId: v.rackId ? v.rackId : v.aisleId,
          fromLocationType: v.rackId ? this.locationType.RACK : this.locationType.AISLE,
          toLocationId: v.rackIdTo ? v.rackIdTo : v.aisleIdTo,
          toLocationType: v.rackIdTo ? this.locationType.RACK : this.locationType.AISLE,
          movementTicket: v.isMarkaSelected ? markaRes.map(m => ({
            marka: m.marka ?? null,
            piecesPerCarton: m.piecesPerCarton ?? null,
            qtyCarton: m.qtyCarton ?? null,
            qtyLoose: m.qtyLoose ?? null,
          })) : [
            {
              qtyCarton: v.cartonQty || null,
              qtyLoose: v.looseQty || null,
            }
          ],
          marka: v.isMarkaSelected ? "VIEW" : null,
          cartonQty: cartonQty || null,
          looseQty: looseQty || null,
        };
      })
    };
    console.warn(param)
    formData.set('shiftingRequest', JSON.stringify(param));

    this.utilsService.post(this.utilsService.serverVariableService.STOCK_SHIFTING_SAVE, formData, { toast: true }).pipe(
      tap(() => {
        this.utilsService.redirectTo('/users/inventory/stock-shifting')
      }),
      takeUntil(this.destroy$)
    ).subscribe();

  }

  // dropdown functions (marka modals)

  isQtyInvalid = (index: number, itemIndex: number, isLoose = false): boolean => {
    const item = this.markaData[itemIndex]?.data[index];
    if (!item) return false;

    if (isLoose) {
      const qtyLoose = Number(item.qtyLoose);
      const maxLooseQty = Number(item.looseQty);

      if ((item.qtyLoose as any) === '' || item.qtyLoose == null) {
        return false;
      }

      if (isNaN(qtyLoose) || qtyLoose < 0 || !Number.isInteger(qtyLoose)) {
        return true;
      }

      return qtyLoose > maxLooseQty;
    } else {
      const qty = Number(item.qtyCarton);
      const maxCartonQty = Number(item.cartonQty);

      if ((item.qtyCarton as any) === '' || item.qtyCarton == null) {
        return false;
      }

      if (isNaN(qty) || qty < 0 || !Number.isInteger(qty)) {
        return true;
      }

      return qty > maxCartonQty;
    }
  }

  // total qty column sum
  getTotalQtySum = (itemIndex: number): number => {
    const group = this.markaData[itemIndex]?.data;
    let res = 0;
    if (!group) return 0;

    res = group.reduce((sum, v) => {
        const qty = Number(v.qtyCarton) || 0;
        const pcs = Number(v.piecesPerCarton) || 0;
        let res = sum + (qty * pcs);
        res = res + (Number(v.qtyLoose) || 0);
        return res;
      }, 0);
    return res;
  }

  getTotalCartonQtySumItemWise = (item: any): number => {
    const qty = Number(item.qtyCarton) || 0;
    const pcs = Number(item.piecesPerCarton) || 0;
    let res = (qty * pcs);
    res = res + (Number(item.qtyLoose) || 0);
    return res;
  }

  // carton input sum
  getCartonInputSum = (itemIndex: number, type: string): number => {
    const group = this.markaData[itemIndex]?.data;
    if (!group) return 0;

    if (type == 'carton') {
      return group.reduce((sum, v) => { const qty = Number(v.qtyCarton || 0) || 0; return sum + qty; }, 0);
    } 
    if (type == 'loose') {
      return group.reduce((sum, v) => { const qty = Number(v.qtyLoose || 0) || 0; return sum + qty; }, 0);
    }
    if (type == 'cartonQty') {
      return group.reduce((sum, v) => { const qty = Number(v.qtyCarton || 0) * Number(v?.piecesPerCarton || 0) || 0; return sum + qty; }, 0);
    }
    return 0;
  }

  // Save marka data from modal
  onSaveMarkaData = (itemIndex: number) => {
    const totalQtySum = this.getTotalQtySum(itemIndex);
    const isMarkaSelected = totalQtySum > 0;

    this.items.at(itemIndex)?.patchValue({
      qty: isMarkaSelected ? totalQtySum : null,
      isMarkaSelected,
      looseQty: null,
      cartonQty: null,
    });
  }

  checkAssignToReportTo: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    let assignTo = group.get('assignTo').value;
    let reportTo = group.get('reportTo').value
    if(assignTo === null || reportTo === null) return null;
    return assignTo !== reportTo ? null : { isSame: true }
  }

  // Clear cache/key acc to index or itemId
  commonCacheClear = (itemId: number, index: number) => {
    this.items.at(index).patchValue({ qty: null })
    if (!itemId && index >= 0) {
      this.availableQtyCache.forEach((_, key) => {
        if (key.endsWith(`-${index}`)) {
          this.availableQtyCache.delete(key);
        }
      });
    };
    this.availableQtyCache.forEach((_, key) => {
      if (key.startsWith(`${itemId}-`)) {
        this.availableQtyCache.delete(key);
      }
    });
  };

  onItemChange = (selectedItem: StockShiftingItemDropdown, index: number) => {
    this.items.at(index).patchValue({ qty: null, rackId: null, aisleId: null, aisleList: [], cLrackList: [] })
    this.commonCacheClear(null, index);
    this.getAisleByWarehouseOrItem(this.form.get('warehouseId').value, selectedItem.id, index);
  }
}

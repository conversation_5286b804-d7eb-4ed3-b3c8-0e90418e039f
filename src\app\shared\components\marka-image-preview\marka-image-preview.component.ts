import { Component, OnInit } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { ActivatedRoute } from '@angular/router';
import { ItemImageObj } from 'src/app/models/ItemImageObj';
import { Deserialize } from 'cerialize';

@Component({
  selector: 'app-marka-image-preview',
  templateUrl: './marka-image-preview.component.html',
  styleUrls: ['./marka-image-preview.component.css']
})
export class MarkaImagePreviewComponent implements OnInit {

  linkId: number;
  imageList: ItemImageObj[] = []
  displayName = ''
  skuId = ''

  constructor(public utilsService: UtilsService, private route: ActivatedRoute) {
    this.linkId = +this.route.snapshot.params['linkGroupId'];
  }

  ngOnInit() {
    this.getLinkAPI();
  }

  getLinkAPI = () => {

    let API = `${this.utilsService.serverVariableService.PREVIEW_MARKA_IMG}?linkGroupId=${this.linkId}`
    this.utilsService.postMethodAPI(false, API, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.imageList = Deserialize(response, ItemImageObj);
        this.displayName = response[0]?.displayName ?? '';
        this.skuId = response[0]?.skuId ?? '';
      }
    })

  }
}

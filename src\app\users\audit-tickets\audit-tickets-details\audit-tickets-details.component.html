<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.AUDIT_TICKET, action: utilsService.enumForPage.VIEW_AUDIT_TICKET, view: true}">
    <div class="content-area details-content-area">
        <div class="details-page">
            <div class="details-page-left">
                <div class="details-page-left-header">
                    <div class="details-page-header-left gap-4">
                        <!-- <div class="form-group form-group-sm theme-ngselect form-border-less">
                            <ng-select class="audit-detail-dropdown" (change)="onChangeFilter('status')" [(ngModel)]="selectedStatus"
                                placeholder="Status" [multiple]="false" [clearable]="true" [items]="dropdown.status"
                                bindLabel="value" bindValue="key">
                            </ng-select>
                        </div> -->
                        <div class="form-group form-group-sm theme-ngselect form-border-less">
                            <ng-select class="audit-detail-dropdown" (change)="onChangeFilter('branch')" [(ngModel)]="branchId" placeholder="Branch"
                                [multiple]="false" [clearable]="false" [items]="dropdown.branch" bindLabel="branchName"
                                bindValue="branchId">
                            </ng-select>
                        </div>
                    </div>
                    <div class="details-page-header-right">
                        <button [pageAccess]="{page: utilsService.enumForPage.AUDIT_TICKET, action: utilsService.enumForPage.ADD_AUDIT_TICKET}"
                            [ngbTooltip]="'Add New'" placement="bottom" container="body" triggers="hover"
                            class="btn btn-icon-text btn-primary btn-sm" [routerLink]="['/users/audit-tickets/new-audit-tickets']"> <i
                                class="th th-outline-add-circle"></i>
                        </button>
                    </div>
                </div>
                <div class="details-page-left-header-body items-details-list">
                    <ul class="items-details-list-wrapper">
                        @for(ticket of auditTicketList$ | async; let i = $index; track $index) {
                        <li (click)="selectedIndex() !== i ? onChangeTicket(i, ticket.id) : null" class="items-details-list-item"
                            [class]="i === selectedIndex() ? 'active' : ''">
                            <div class="details-list-item-content">
                                <h6>ID: {{ticket.ticketId}}</h6>
                                <p>{{ticket.ticketType}}</p>
                            </div>
                            <div class="details-list-item-status">
                                <p [class]="ticket.ticketActionType !== 'Auto' ? 'text-secondary' : 'text-primary'">
                                    {{ticket.ticketActionType}}</p>
                            </div>
                        </li>
                        } @empty {
                            <li class="items-details-list-item">
                                <div class="details-list-item-content text-center">
                                    <h6>{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</h6>
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            </div>
            @if(auditTicketDetails()) {
            <div class="details-page-right">
                <div class="items-details-content-wrapper">
                    <div class="page-title-wrapper ">
                        <div class="page-title-left">
                            <h4>#{{auditTicketDetails().ticketId}}</h4>
                        </div>
                        <div class="page-title-right">
                            @if(auditTicketDetails().subjectStatus === enumForTicketStatus.PENDING) {
                            <button [pageAccess]="{page: utilsService.enumForPage.AUDIT_TICKET, action: utilsService.enumForPage.EDIT_AUDIT_TICKET}"
                                class="btn btn-sm btn-outline-white btn-icon-text"
                                [routerLink]="['/users/audit-tickets/edit-audit-tickets/', auditTicketDetails().id]">
                                <i class="th th-outline-edit"></i>Edit
                            </button>
                            }
                            <button class="btn btn-sm btn-icon btn-outline-white"
                                [routerLink]="['/users/audit-tickets/']">
                                <i class="th th-close"></i>
                            </button>
                        </div>
                    </div>
                    
                    @let isQCTicket = auditTicketDetails().ticketType === enumForTicketType.QC_CHECK;
                    @let isRealPhotoTicket = auditTicketDetails().ticketType === enumForTicketType.REAL_PHOTO;
                    @let isScTicket = auditTicketDetails().ticketType === enumForTicketType.SC;
                    @let isCaptureDimensionTicket = auditTicketDetails().ticketType === enumForTicketType.CAPTURE_DIMENSION;
                    <div class="details-page-right-body ">
                        <div class="card card-theme mt-3">
                            <div class="card-body pt-0">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card card-theme4">
                                            <div class="card-header">
                                                <div class="card-header-left">
                                                    <div class="card-header-title">
                                                        <h2>Ticket Details</h2>
                                                    </div>
                                                </div>
                                                <div class="card-header-right"></div>
                                            </div>
                                            <div class="card-body">
                                                <ul class="inline-list">
                                                    @if(isScTicket) {
                                                        <li>
                                                            <label>Ticket Subject</label>
                                                            <span
                                                                class="text-primary">{{auditTicketDetails().ticketSubject}}</span>
                                                        </li>
                                                    }
                                                    <li>
                                                        <label>Subject Status</label>
                                                        <span
                                                            [ngClass]="{'text-success': auditTicketDetails().status === enumForTicketStatus.COMPLETED,
                                                                        'text-warning': auditTicketDetails().status === enumForTicketStatus.IN_PROGRESS,
                                                                        'text-info': auditTicketDetails().status === enumForTicketStatus.PENDING}">
                                                            {{auditTicketDetails().status}}
                                                        </span>
                                                    </li>
                                                    <li>
                                                        <label>Ticket Type</label>
                                                        <span class="text-primary">{{auditTicketDetails().ticketType}}</span>
                                                    </li>
                                                    <li>
                                                        <label>Branch</label>
                                                        <span>{{auditTicketDetails().branchName}}</span>
                                                    </li>
                                                    <li>
                                                        <label>Warehouse</label>
                                                        <span>{{auditTicketDetails().warehouseName}}</span>
                                                    </li>
                                                    <li>
                                                        <label>Assign To</label>
                                                        <span>{{auditTicketDetails()?.assignedTo}}</span>
                                                    </li>
                                                    <li>
                                                        <label>Re-Assign To</label>
                                                        <span>{{auditTicketDetails()?.reassignTo || '-'}}</span>
                                                    </li>
                                                    <li>
                                                        <label>Note</label>
                                                        <span>{{auditTicketDetails()?.ticketNote || '-'}}</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        @if(auditTicketDetails()?.ticketDocuments?.length > 0) {
                                        <div class="card card-theme4">
                                            <div class="card-header">
                                                <div class="card-header-left">
                                                    <div class="card-header-title">
                                                        <h2>Attached Files</h2>
                                                    </div>
                                                </div>
                                                <div class="card-header-right"></div>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <div
                                                        class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                                        <div class='attachments-upload-row'>
                                                            @for(item of auditTicketDetails()?.ticketDocuments; track $index) {
                                                            <div class='attachments-upload-col'>
                                                                <div class='card-attachments-upload'>
                                                                    <div class='attachments-image'>
                                                                        <img [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null"
                                                                            alt="valamji" loading="lazy" />
                                                                    </div>
                                                                    <div class="attachments-text">
                                                                        <h6 class="file-name">{{item.formattedName}}
                                                                        </h6>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        }
                                    </div>
                                    
                                    @if(isScTicket) {
                                        <div class="col-12">
                                            <div class="card card-theme card-table-sticky3">
                                                <div class="card-body px-0 gap-3">
                                                    <div class="accordion accordion-group without-border-accordion">
                                                        @for(main of auditTicketDetails()?.result; let j = $index; track $index) {
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button [ngClass]="{'collapsed': !main.isExpand}" (click)="onExpand($index)"
                                                                    class="accordion-button" type="button">
                                                                    <div class="accordion-header-left">
                                                                        @if(main.user) {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Updated By {{main.user}} {{main.dateTime ? ((main.dateTime | date: 'dd/MM/yyyy, h:mm a')) : ''}}
                                                                            </li>
                                                                        </ul>
                                                                        } @else {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Not Assigned</li>
                                                                        </ul>
                                                                        }
                                                                    </div>
                                                                    <div class="accordion-header-right">
                                                        
                                                                    </div>
                                                                </button>
                                                            </h2>
                                                            @if(main.isExpand) {
                                                            <div #collapse="ngbCollapse" [ngbCollapse]="!main.isExpand" [ngClass]="{'collapse show': main.isExpand}">
                                                                <div class="accordion-body tbl-accordion-body p-0">
                                                                    <div class="table-responsive">
                                                                        <table class="table-theme table-hover table table-bordered table-sticky">
                                                                            <thead class="border-less">
                                                                                <tr>
                                                                                    <th>Item</th>
                                                                                    <th>Current <br> Location</th>
                                                                                    <th>Available <br>Carton</th>
                                                                                    <!-- <th>Total<br> Carton Qty</th> -->
                                                                                    <th>Loose Qty</th>
                                                                                    <!-- <th>Total Qty</th> -->
                                                                                    <th>Updated <br>Carton Qty</th>
                                                                                    <!-- <th>Total<br> Updated <br>Carton Qty</th> -->
                                                                                    <th>Updated<br> Loose Qty</th>
                                                                                    <!-- <th>Updated<br> Total Qty</th> -->
                                                                                    <th class="text-center">Note</th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                @for(item of main.newArray; let i = $index; track $index) {
                                                                                <tr>
                                                                                    <td class="tbl-user">
                                                                                        <div class="tbl-user-checkbox-srno">
                                                                                            <span>{{(i + 1) | padNum}}.</span>
                                                                                            <div class="tbl-user-wrapper">
                                                                                                <div class="tbl-user-image">
                                                                                                    <img [src]="item.itemImage ? (utilsService.imgPath + item.itemImage) : ''"
                                                                                                        alt="valamji">
                                                                                                </div>
                                                                                                <div class="tbl-user-text-action">
                                                                                                    <div class="tbl-user-text">
                                                                                                        <p>{{item.skuId}}</p>
                                                                                                        <span>{{item.itemName}}</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>{{item.locationName}}</td>
                                                                                    <td>
                                                                                        <div>
                                                                                            <app-available-qty-modal
                                                                                                (openModal)="getAvailableQtyCommonAPI(item, i, j, true)"
                                                                                                [value]="item.cartonQtyDisplay" [data]="item.availableQtyModalData || []" />
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        <div>
                                                                                            <app-available-qty-modal
                                                                                                (openModal)="getAvailableQtyCommonAPI(item, i, j, true)"
                                                                                                [value]="item.looseQtyDisplay" [data]="item.availableQtyModalData || []" />
                                                                                        </div>
                                                                                    </td>
                                                                                    <td>
                                                                                        <span>{{item.cartonQtyAvailable}}</span>
                                                                                    </td>
                                                                                    <td>
                                                                                        <span>{{item.looseQtyAvailable}}</span>
                                                                                    </td>
                                                                                    <td class="tbl-action ">
                                                                                        <div class="tbl-action-group ">
                                                                                            @if(item.notes) {
                                                                                            <button (click)="openNotesModal(viewNotesModal, item.notes)"
                                                                                                class="btn btn-xs btn-light-info btn-icon">
                                                                                                <i class="th th-outline-eye"></i>
                                                                                            </button>
                                                                                            }
                                                                                            <div>
                                                                                                <app-attachment-download-dropdown [fileList]="(item.documents || [])"
                                                                                                    [isDelete]="false" />
                                                                                            </div>
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                                } @empty {
                                                                                <tr>
                                                                                    <td colspan="20" class="text-center">
                                                                                        <span
                                                                                            class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                                                    </td>
                                                                                </tr>
                                                                                }
                                                                            </tbody>
                                                        
                                                                        </table>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            }
                                                        </div>
                                                        }
                                                    </div>
                                                    @if(auditTicketDetails().updateHistory?.length > 0) {
                                                    <div class="accordion accordion-group without-border-accordion">
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button class="accordion-button" [ngClass]="{'collapsed': !updateHistoryExpand()}" type="button"
                                                                    (click)="updateHistoryExpand.set(!updateHistoryExpand())">
                                                                    <div class="accordion-header-left">
                                                                        <ul class="accordion-header-item">
                                                                            <li>Update History </li>
                                                                        </ul>
                                                                    </div>
                                                                    <div class="accordion-header-right">
                                                
                                                                    </div>
                                                                </button>
                                                            </h2>
                                                            @if(updateHistoryExpand() && auditTicketDetails().updateHistory?.length > 0) {
                                                            <div #collapse="ngbCollapse" [ngbCollapse]="!updateHistoryExpand()"
                                                                [ngClass]="{'collapse show': updateHistoryExpand()}">
                                                                <div class="accordion-body tbl-accordion-body p-0">
                                                                    <div class="row">
                                                                        <div class="col-lg-9">
                                                                            <div class="update-history-wrapper">
                                                                                @for(item of auditTicketDetails()?.updateHistory || []; track $index) {
                                                                                <div class="update-history-item">
                                                                                    <div class="card-details-left">
                                                                                        <div class="card-user-image">
                                                                                            <img [src]="item.userProfileUrl ? (utilsService.imgPath + item.userProfileUrl) : 
                                                                                                'assets/images/avatar-default.svg'"
                                                                                                alt="valamji">
                                                                                        </div>
                                                                                        <div class="card-details">
                                                                                            <p>{{item.updatedBy}}</p>
                                                                                            <span>{{item.updateDescription}}</span>
                                                                                        </div>
                                                
                                                                                    </div>
                                                                                    <div class="card-details-right">
                                                                                        <span class="card-date">
                                                                                            {{item.updatedDate | date: 'dd/MM/yyyy, h:mm a'}}
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                                }
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                
                                                                </div>
                                                            </div>
                                                            }
                                                        </div>
                                                    </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    <!-- @if(isQCTicket) {
                                        <div class="col-12">
                                            <div class="card card-theme card-table-sticky3">
                                                <div class="card-body px-0 gap-3">
                                                    <div class="accordion accordion-group without-border-accordion">
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button [ngClass]="{'collapsed': !isQcExpand()}" class="accordion-button" type="button" (click)="onExpandQC()">
                                                                    <div class="accordion-header-left">
                                                                        @if(auditTicketDetails()?.assignedTo) {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Updated By {{auditTicketDetails()?.assignedTo}} {{auditTicketDetails()?.updatedDate ? ((auditTicketDetails()?.updatedDate | date: 'dd/MM/yyyy, h:mm a')) : ''}}
                                                                            </li>
                                                                        </ul>
                                                                        } @else {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Not Assigned</li>
                                                                        </ul>
                                                                        }
                                                                    </div>
                                                                </button>
                                                            </h2>
                                                            <div #collapse="ngbCollapse" [ngbCollapse]="!isQcExpand()" [ngClass]="{'collapse show': isQcExpand()}">
                                                                <div class="accordion-body">
                                                                    
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    } -->
                                    @if (isRealPhotoTicket || isQCTicket || isCaptureDimensionTicket) {
                                        <div class="col-12">
                                            <div class="card card-theme card-table-sticky3">
                                                <div class="card-body px-0 gap-3">
                                                    <div class="accordion accordion-group without-border-accordion">
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button [ngClass]="{'collapsed': !isQcExpand()}" class="accordion-button" type="button" (click)="onExpandQC()">
                                                                    <div class="accordion-header-left">
                                                                        @if(auditTicketDetails()?.assignedTo) {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Updated By {{auditTicketDetails()?.assignedTo}} {{auditTicketDetails()?.updatedDate ? ((auditTicketDetails()?.updatedDate | date: 'dd/MM/yyyy, h:mm a')) : ''}}
                                                                            </li>
                                                                        </ul>
                                                                        } @else {
                                                                        <ul class="accordion-header-item">
                                                                            <li>Not Assigned</li>
                                                                        </ul>
                                                                        }
                                                                    </div>
                                                                </button>
                                                            </h2>
                                                            <div #collapse="ngbCollapse" [ngbCollapse]="!isQcExpand()" [ngClass]="{'collapse show': isQcExpand()}">
                                                                <div class="accordion-body">
                                                                    @if(auditTicketDetails()?.marka) {
                                                                        <div class="row">
                                                                            <div class="col-12 col-md-6 mb-3">
                                                                                <div class="card card-theme4">
                                                                                    <div class="card-header">
                                                                                        <div class="card-header-left">
                                                                                            <div class="card-header-title">
                                                                                                <h2>{{auditTicketDetails()?.marka}}</h2>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="card-body">
                                                                                        <div class="form-group">
                                                                                            <label>Location : {{auditTicketDetails()?.location}}</label>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    }
                                                                    @if(isQCTicket || isCaptureDimensionTicket) {
                                                                        @if(auditTicketDetails()?.isFailed) {
                                                                            <div class="row">
                                                                                <div class="col-12 col-md-6 mb-3">
                                                                                    <div class="card card-theme4">
                                                                                        <div class="card-header">
                                                                                            <div class="card-header-left">
                                                                                                <div class="card-header-title">
                                                                                                    <h2>Marked As Failed</h2>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="card-body bg-danger-color">
                                                                                            <div class="form-group">
                                                                                                <label>Reason : {{auditTicketDetails()?.failedReason}}</label>
                                                                                            </div>
                                                                                            <div class="attachments-upload-grid-container attachments-upload-grid-container2">
                                                                                                <div class="attachments-upload-row">
                                                                                                    @for(image of auditTicketDetails()?.failedDocs; track $index) {
                                                                                                    <div class="attachments-upload-col">
                                                                                                        <div class="card-attachments-upload">
                                                                                                            <div class="attachments-image">
                                                                                                                <img [src]="image ? (utilsService.imgPath + image) : null" alt="valamji"
                                                                                                                    loading="lazy" />
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    }
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        }
                                                                        <div class="row">
                                                                            @for(item of auditTicketDetails()?.qcProperties; track $index) {
                                                                            <div class="col-12 col-md-6 mb-3">
                                                                                <div class="card card-theme4">
                                                                                    <div class="card-header">
                                                                                        <div class="card-header-left">
                                                                                            <div class="card-header-title">
                                                                                                @switch(item.value) {
                                                                                                @case(enumForQC.ITEM_DIMENSION){ <h2>Item Dimension</h2> }
                                                                                                @case(enumForQC.ITEM_WEIGHT){ <h2>Item Weight</h2> }
                                                                                                @case(enumForQC.ITEM_WEIGHT_WITH_BOX){ <h2>Item Weight With Box</h2> }
                                                                                                @case(enumForQC.CARTON_DIMENSION){ <h2>Carton Dimension</h2> }
                                                                                                @case(enumForQC.CARTON_WEIGHT){ <h2>Carton Weight</h2> }
                                                                                                @case(enumForQC.COLOR){ <h2>Color</h2> }
                                                                                                @case(enumForQC.PACKING_TYPES){ <h2>Packing Types</h2> }
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                
                                                                                    <div class="card-body">
                                                                                        @if(item.value === enumForQC.ITEM_DIMENSION) {
                                                                                        <div class="form-group">
                                                                                            <div class="form-control-wrapper">
                                                                                                <div class="input-group">
                                                                                                    <span class="form-control bg-light">
                                                                                                        {{ item.actualItemLength }} × {{ item.actualItemWidth }} × {{
                                                                                                        item.actualItemHeight }}
                                                                                                    </span>
                                                                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                                                                        id="button-INR">{{item.actualUnit}}</button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item.value === enumForQC.ITEM_WEIGHT) {
                                                                                        <div class="form-group">
                                                                                            <div class="form-control-wrapper">
                                                                                                <div class="input-group">
                                                                                                    <input disabled [ngModel]="item.actualItemWeight" type="text" class="form-control">
                                                                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                                                                        id="button-INR">{{item.actualUnit}}</button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item.value === enumForQC.CARTON_WEIGHT) {
                                                                                        <div class="form-group">
                                                                                            <div class="form-control-wrapper">
                                                                                                <div class="input-group">
                                                                                                    <input disabled [ngModel]="item.actualItemWeight" type="text" class="form-control">
                                                                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                                                                        id="button-INR">{{item.actualUnit}}</button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item.value === enumForQC.CARTON_DIMENSION) {
                                                                                        <div class="form-group">
                                                                                            <div class="form-control-wrapper">
                                                                                                <div class="input-group">
                                                                                                    <span class="form-control bg-light">
                                                                                                        {{ item.actualItemLength }} × {{ item.actualItemWidth }} × {{
                                                                                                        item.actualItemHeight }}
                                                                                                    </span>
                                                                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                                                                        id="button-INR">{{item.actualUnit}}</button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item.value === enumForQC.ITEM_WEIGHT_WITH_BOX) {
                                                                                        <div class="form-group">
                                                                                            <div class="form-control-wrapper">
                                                                                                <div class="input-group">
                                                                                                    <input disabled [ngModel]="item.itemWithBoxWeight" type="text" class="form-control">
                                                                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                                                                        id="button-INR">{{item.actualUnit}}</button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item?.qcResultDocs?.length > 0) {
                                                                                        <div class="attachments-upload-grid-container attachments-upload-grid-container2">
                                                                                            <div class="attachments-upload-row">
                                                                                                @for(image of item?.qcResultDocs; track $index) {
                                                                                                <div class="attachments-upload-col">
                                                                                                    <div class="card-attachments-upload">
                                                                                                        <div class="attachments-image">
                                                                                                            <img [src]="image ? (utilsService.imgPath + image) : null" alt="valamji"
                                                                                                                loading="lazy" />
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                        }
                                                                                        @if(item?.actualColors?.length > 0) {
                                                                                        <div class="d-flex align-items-center gap-2">
                                                                                            @for(color of item?.actualColors; track $index) {
                                                                                            <div class="badge badge-primary-light">{{color.colorName}}</div>
                                                                                            }
                                                                                        </div>
                                                                                        }
                                                                                        @if(item?.actualPackingTypes?.length > 0) {
                                                                                        <div class="d-flex align-items-center gap-2">
                                                                                            @for(packingType of item?.actualPackingTypes; track $index) {
                                                                                            <div class="badge badge-primary-light">{{packingType.packingName}}</div>
                                                                                            }
                                                                                        </div>
                                                                                        }
                                                                                    </div>
                                                                
                                                                                </div>
                                                                            </div>
                                                                            }
                                                                        </div>
                                                                    }
                                                                    @if (isRealPhotoTicket || (isCaptureDimensionTicket && auditTicketDetails()?.photos?.length > 0)) {
                                                                        <div class="row">
                                                                            <div class="col-12 col-md-6 mb-3">
                                                                                <div class="card card-theme4">
                                                                                    <div class="card-header">
                                                                                        <div class="card-header-left">
                                                                                            <div class="card-header-title">
                                                                                                <h2>
                                                                                                    @if (isRealPhotoTicket) { Item Real Photos } @else { Photos } </h2>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    <div class="card-body">
                                                                                        <div class="attachments-upload-grid-container attachments-upload-grid-container2">
                                                                                            <div class="attachments-upload-row">
                                                                                                @for(image of auditTicketDetails()?.photos; track $index) {
                                                                                                <div class="attachments-upload-col">
                                                                                                    <div class="card-attachments-upload">
                                                                                                        <div class="attachments-image">
                                                                                                            <img [src]="image ? (utilsService.imgPath + image) : null" alt="valamji"
                                                                                                                loading="lazy" />
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                }
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            } @else {
            <div class="details-page-right">
                <div class="items-details-content-wrapper">
                    <div class="page-title-wrapper ">
                        <div class="page-title-left">
                            
                        </div>
                        <div class="page-title-right">
                            <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/audit-tickets/']">
                                <i class="th th-close"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="details-page-right-body ">
                    <app-no-record />
                </div>
            </div>
            }
        </div>
    </div>
</div>

<!-- view notes modal -->
<ng-template #viewNotesModal let-modal>
    <div class="modal-theme">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Note</h5>
                <button type="button" class="btn-close" (click)="modal.close()"><i class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <p>{{notesModalData()}}</p>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn  btn-primary btn-icon-text" (click)="modal.close()"> ok</button>
                </div>
            </div>
        </div>
    </div>
</ng-template>
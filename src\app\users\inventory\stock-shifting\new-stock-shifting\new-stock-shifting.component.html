<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Add New Stock Shifting </h4>
    </div>
    <div class="page-title-right">
      <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/inventory/stock-shifting']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">`
      <div class="card-body" [formGroup]="form">
        <div class="row mb-3">
          <div class="col-lg-5 col-md-5 col-sm-12">
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Assign To</label>
              <div class="form-control-wrapper ">
                <ng-select class="" placeholder="Select User" [multiple]="false" [clearable]="true"
                  [items]="dropdown.user" bindLabel="label" bindValue="value" formControlName="assignTo">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['assignTo'].hasError('required') && form.controls['assignTo'].touched">
                  {{utilsService.validationService.ASSIGN_TO_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Report to </label>
              <div class="form-control-wrapper ">
                <ng-select class="" placeholder="Select User" [multiple]="false" [clearable]="true"
                  [items]="dropdown.user" bindLabel="label" bindValue="value" formControlName="reportTo">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['reportTo'].hasError('required') && form.controls['reportTo'].touched">
                  {{utilsService.validationService.REPORT_TO_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="form.hasError('isSame') && form.controls['assignTo'].touched && !form.controls['reportTo'].hasError('required')">
                  {{utilsService.validationService.ASSIGN_TO_REPORT_TO_SAME}}
                </div>
              </div>
            </div>
            @let notAllowPastDate = moment().startOf('day').format('DD-MM-YYYY');
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Shifting Date</label>
              <div class="form-control-wrapper ">
                <app-date-time-picker formControlName="shiftingDate" [placeholder]="'Select Shifting Date'"
                  [timer]="false" [displayFormat]="'DD/MM/YYYY'" [outputDateFormat]="'YYYY-MM-DD'" [minDate]="notAllowPastDate" />
                <div class="message error-message"
                  *ngIf="form.controls['shiftingDate'].hasError('required') && form.controls['shiftingDate'].touched">
                  {{utilsService.validationService.SHIFTING_DATE_REQ}}
                </div>
              </div>
            </div>

            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Warehouse </label>
              <div class="form-control-wrapper ">
                <ng-select (change)="onChangeWarehouse()" (open)="captureOldValue()" class=""
                  placeholder="Select Warehouse" [multiple]="false" [clearable]="false" [items]="dropdown.warehouse"
                  bindLabel="label" bindValue="value" formControlName="warehouseId">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['warehouseId'].hasError('required') && form.controls['warehouseId'].touched">
                  {{utilsService.validationService.WAREHOUSE_REQ}}
                </div>
              </div>
            </div>

            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Reason </label>
              <div class="form-control-wrapper ">
                <ng-select class="custom-ng-select" placeholder="Select Reason" [multiple]="false" [clearable]="false"
                  [items]="dropdown.reason" bindLabel="label" bindValue="value" formControlName="reasonMasterId">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['reasonMasterId'].hasError('required') && form.controls['reasonMasterId'].touched">
                  {{utilsService.validationService.REASON_REQ}}
                </div>
              </div>
            </div>

            <div class="form-group form-group-inline-control required">
              <label class="form-label">Note</label>
              <div class="form-control-wrapper ">
                <textarea placeholder="Enter Note" class="form-control" formControlName="note"
                  [maxLength]="utilsService.validationService.MAX_500">
                </textarea>
                <div class="message error-message"
                  *ngIf="form.controls['note'].hasError('required') && form.controls['note'].touched">
                  {{utilsService.validationService.NOTE_REQ}}
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-4 col-md-4 col-sm-12" (dragover)="onSelectAttachments($event);doc.value = ''"
            (drop)="onSelectAttachments($event);doc.value = ''" (paste)="onSelectAttachments($event)">
            <div class="attachments-wrapper">
              <div class='attachments-container'>
                <div class='attachments-content'>
                  <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                  <p>Drag and Drop Images & Videos here or <span class='text-primary'>Choose
                      file</span></p>
                </div>
                <input #doc type="file" ref={imageRef} multiple (change)="onSelectAttachments($event);doc.value = ''" />
              </div>
              <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                <div class='attachments-upload-row'>
                  @for (item of attachmentsList.get(); track $index; let i = $index) {
                  <div class="attachments-upload-col">
                    <div class="card-attachments-upload">
                      <div class="attachments-image">
                        @if (utilsService.isImage(item.originalName)) {
                        <img loading="lazy"
                          (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                          [src]="item.formattedName ? (item.file ? item.formattedName : utilsService.imgPath + item.formattedName) : null"
                          alt="valamji" />
                        } @else if (utilsService.isMedia(item.originalName)) {
                        <img
                          (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                          src="assets/images/files/file-video.svg" alt="valamji" />
                        } @else if (utilsService.isExcel(item.originalName)) {
                        <img
                          (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                          src="assets/images/files/file-excel.svg" alt="valamji" />
                        } @else if (utilsService.isDocument(item.originalName)) {
                        <img
                          (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                          src="assets/images/files/file-pdf.svg" alt="valamji" />
                        }
                      </div>
                      <div class="attachments-text" [ngbTooltip]="item.originalName" placement="bottom" container="body"
                        triggers="hover">
                        <h6 class="file-name">{{ item.originalName }}</h6>
                      </div>
                      <button (click)="removeAttachment(i)" class="btn-close" variant="close"><i
                          class="th th-close"></i></button>
                    </div>
                  </div>
                  }
                </div>
              </div>
            </div>
          </div>


        </div>

        @if(form.get('warehouseId').value){
        <div class="col-lg-12 col-md-12 col-sm-12">
          <div class="inner-title-wrapper">
            <div class="inner-title-left">
              <div class="inner-title-text">
                <h6 class="">Associated Item*</h6>
              </div>
            </div>
            <div class="inner-title-rigth">
            </div>
          </div>
        </div>
        <div class="col-lg-12 col-md-12 col-sm-12">
          <div class="table-responsive">
            <table class="table-theme table-hover table table-bordered table-sticky"
              formArrayName="associateItemReqList">
              <thead class="border-less">
                <tr>
                  <th rowspan="2">Item Details</th>
                  <th colspan="2" class="border-bottom text-center">Current Location</th>
                  <th colspan="2" class="border-bottom text-center">Transfer Location</th>
                  <th rowspan="2">Transfer Quantity</th>
                  <th rowspan="2">Carton Qty</th>
                  <th rowspan="2">Loose Qty</th>
                  <th rowspan="2" class="text-center">Action</th>
                </tr>
                <tr>
                  <th>Aisle</th>
                  <th>Rack</th>
                  <th>Aisle</th>
                  <th>Rack</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let contact of items.controls; index as i" [formGroupName]="i"
                  class="tbl-add-row tbl-bg-white">
                  @let currentLocationRack = contact.get('cLrackList').value;
                  @let itemDropdown = contact.get('itemDropdown').value;
                  @let transferLocationRack = contact.get('tLrackList').value;
                  @let aisleList = contact.get('aisleList').value;
                  <td class="tbl-user tbl-form-group">
                    <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100">
                      <div class="">
                        <ng-select (change)="onItemChange($event, i)" id="itemId-{{i}}" class="stock-shifting-class" appendTo=".theme-ngselect"
                          [items]="itemDropdown" [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}"
                          formControlName="itemId" bindLabel="name" placeholder="Select Item" bindValue="id" [virtualScroll]="true"
                          [searchFn]="customSearchFn" [clearable]="false">
                          <ng-template ng-header-tmp>
                            <div class="d-flex fw-bold bg-light border-bottom py-2 px-3">
                              <div class="fs-14">Item</div>
                            </div>
                          </ng-template>
                          <ng-template ng-label-tmp let-item="item">
                            <div class="tbl-user">
                              <div class="tbl-user-checkbox-srno">
                                <div class="tbl-user-wrapper">
                                  <div class="tbl-user-image">
                                    <img loading="lazy" [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                      alt="valamji">
                                  </div>
                                  <div class="tbl-user-text-action">
                                    <div class="tbl-user-text">
                                      <p>{{ item.displayName }} </p>
                                      <span>{{item.skuId}}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item">
                            <div class="">
                              <div>
                                <div class="tbl-user">
                                  <div class="tbl-user-checkbox-srno">
                                    <div class="tbl-user-wrapper">
                                      <div class="tbl-user-image">
                                        <img loading="lazy" [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                          alt="valamji">
                                      </div>
                                      <div class="tbl-user-text-action">
                                        <div class="tbl-user-text po-description">
                                          <p [title]="item.displayName">{{ item.displayName }} </p>
                                          <span>{{ item.skuId }}</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </ng-template>
                          <ng-template ng-notfound-tmp>
                            <div class="d-flex align-items-center border-bottom py-2 px-3 justify-content-center fs-12">
                              <div>No Item Found</div>
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group theme-ngselect form-border-less">
                      <ng-select
                        [ngClass]="{'required': contact.get('aisleId').invalid && contact.get('aisleId').touched}"
                        appendTo=".theme-ngselect" (change)="onChangeAisle(i, true, $event)" class="" placeholder="Select"
                        [multiple]="false" [clearable]="true" [items]="aisleList" bindLabel="label"
                        bindValue="value" formControlName="aisleId">
                      </ng-select>
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group theme-ngselect form-border-less">
                      <ng-select
                        [ngClass]="{'required': contact.get('rackId').invalid && contact.get('rackId').touched}"
                        appendTo=".theme-ngselect" class="" placeholder="Select" [multiple]="false" [clearable]="true"
                        [items]="currentLocationRack" bindLabel="label" bindValue="value" formControlName="rackId">
                      </ng-select>
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group theme-ngselect form-border-less">
                      <ng-select
                        [ngClass]="{'required': contact.get('aisleIdTo').invalid && contact.get('aisleIdTo').touched}"
                        appendTo=".theme-ngselect" (change)="onChangeAisle(i, false, null)" class="" placeholder="Select"
                        [multiple]="false" [clearable]="true" [items]="dropdown.aisle" bindLabel="label"
                        bindValue="value" formControlName="aisleIdTo">
                      </ng-select>
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group theme-ngselect form-border-less">
                      <ng-select
                        [ngClass]="{'required': contact.get('rackIdTo').invalid && contact.get('rackIdTo').touched}"
                        appendTo=".theme-ngselect" class="" placeholder="Select" [multiple]="false" [clearable]="true"
                        [items]="transferLocationRack" bindLabel="label" bindValue="value" formControlName="rackIdTo">
                      </ng-select>
                    </div>
                  </td>
                  @let aisleId = contact.get('aisleId').value;
                  @let rackId = contact.get('rackId').value;
                  @let itemId = contact.get('itemId').value;
                  @let rackExists = currentLocationRack.length > 0;
                  @let aisleExists = currentLocationRack.length === 0;
                  <td class="d-flex align-items-center gap-2 justify-content-center">
                    <div class="form-group form-group-100" [ngClass]="{'form-error': contact.get('qty').invalid && contact.get('qty').touched}">
                      <input type="text" [disableControl]="true" class="form-control" formControlName="qty" placeholder="Qty">
                    </div>
                    <div class="dropdown dropdown-with-tabs-tables dropdown-no-arrow" ngbDropdown placement="auto"
                      #dropdownInput="ngbDropdown">
                      <button [disabled]="(rackExists && !rackId) || (aisleExists && !aisleId) || !itemId" (click)="getAvailableQty(i)"
                        class="btn btn-icon btn-transparent text-primary btn-link" ngbDropdownToggle>
                        <i class="th th-outline-box ms-4 fs-13"></i>
                      </button>
                      <div class="dropdown-menu" ngbDropdownMenu *ngIf="dropdownInput.isOpen">
                        <div class="card-dropdown-with-tabs-tables">
                          <div class="card-header">
                            <div class='nav-tabs-outer nav-tabs-style2'>
                              <nav>
                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                  <button class="nav-link active" id="nav-dropdowntab1-tab" data-bs-toggle="tab"
                                    data-bs-target="#nav-dropdowntab1" type="button" role="tab"
                                    aria-controls="nav-dropdowntab1" aria-selected="true">
                                    <i class="th th-outline-house-2"></i>Select Marka</button>
                                  <div class="dropdown-with-tabs-tables-close ms-auto">
                                    <button (click)="dropdownInput.close()" class="btn btn-transparent btn-icon">
                                      <i class="th th-close m-0"></i>
                                    </button>
                                  </div>
                                </div>
                              </nav>
                            </div>
                          </div>
                          <div class="card-body">
                            <div class='nav-tabs-outer nav-tabs-style2'>
                              <div class="tab-content" id="nav-tabContent">
                                <div class="tab-pane fade show active" id="nav-dropdowntab1" role="tabpanel"
                                  aria-labelledby="nav-dropdowntab1-tab">
                                  <div class="table-responsive ">
                                    <table class="table-theme table-hover table table-bordered table-sticky">
                                      <thead class="border-less">
                                        <tr>
                                          <th class="">Marka</th>
                                          <th>Available <br /> Carton</th>
                                          <th>Available <br /> Loose</th>
                                          <th>Carton</th>
                                          <th>Total <br /> Carton Qty</th>
                                          <th>Loose</th>
                                          <th>Total Qty</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        @for(item of markaData[i]?.data; let childIndex = $index; track $index) {
                                          <tr>
                                            <td class="tbl-user w-50">
                                              <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                  <div class="tbl-user-text">
                                                    <p>{{item?.marka || '-'}}</p>
                                                  </div>
                                                </div>
                                              </div>
                                            </td>
                                            <td class="tbl-level">
                                              <span>{{item?.cartonQty || 0}}</span>
                                              <span>{{item?.piecesPerCarton || 0}} PCS/Carton</span>
                                              <span>{{(item?.cartonQty * item?.piecesPerCarton )|| 0}}</span>
                                            </td>
                                            <td>{{item?.looseQty || 0}}</td>
                                            <td>
                                              <div class="form-group form-group-100" [ngClass]="{'form-error': isQtyInvalid(childIndex, i, false)}">
                                                <input [(ngModel)]="item.qtyCarton"
                                                  [ngModelOptions]="{standalone: true}" type="text" class="form-control" placeholder="Enter" mask="separator.0"
                                                  thousandSeparator="" [maxLength]="utilsService.validationService.MAX_5">
                                              </div>
                                            </td>
                                            <td>{{(item.qtyCarton || 0) * (item?.piecesPerCarton || 0)}}</td>
                                            <td>
                                              @if(item.looseQty) {
                                              <div class="form-group form-group-100" [ngClass]="{'form-error': isQtyInvalid(childIndex, i, true)}">
                                                <input [(ngModel)]="item.qtyLoose" [ngModelOptions]="{standalone: true}"
                                                  type="text" class="form-control" placeholder="Enter" mask="separator.0" thousandSeparator=""
                                                  [maxLength]="utilsService.validationService.MAX_5">
                                              </div>
                                              }
                                              @else {
                                              <span>-</span>
                                              }
                                            </td>
                                            <td>
                                              {{getTotalCartonQtySumItemWise(item)}}
                                            </td>
                                          </tr>
                                        } @empty {
                                          <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(markaData[i]?.data)">
                                            <td colspan="20" class="text-center">
                                              <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                            </td>
                                          </tr>
                                        }
                                      </tbody>
                                      @if(markaData[i]?.data?.length > 0) {
                                        <tfoot>
                                          <tr class="fw-semibold bg-white border-top">
                                            <td>-</td>
                                            <td>{{markaData[i]?.data[0]?.cartonQtySum}}</td>
                                            <td>{{markaData[i]?.data[0]?.looseQtySum}}</td>
                                            <td>{{getCartonInputSum(i, 'carton')}}</td>
                                            <td>{{getCartonInputSum(i, 'cartonQty')}}</td>
                                            <td>{{markaData[i]?.data[0]?.looseQtySum? getCartonInputSum(i, 'loose') : '-'}}</td>
                                            <td>{{getTotalQtySum(i)}}</td>
                                          </tr>
                                        </tfoot>
                                      }
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="card-footer">
                            <div class="button-group">
                              <button (click)="onSaveMarkaData(i); dropdownInput.close()"
                                [disabled]="disableIfInvalid()" type="button"
                                class="btn btn-sm btn-primary btn-icon-text">
                                <i class="th th-outline-tick-circle"></i>
                                Save
                              </button>
                              <button (click)="dropdownInput.close()" type="button"
                                class="btn btn-sm btn-outline-white btn-icon-text">
                                <i class="th th-outline-close-circle"></i>
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  @let isMarkaSelected = contact.get('isMarkaSelected').value;
                  <td>
                    <div class="form-group form-group-100"
                      [ngClass]="{'form-error': contact.get('cartonQty').invalid && contact.get('cartonQty').touched}">
                      <input mask="separator.0" thousandSeparator="" [maxLength]="utilsService.validationService.MAX_5"
                        [disableControl]="isMarkaSelected" type="text" class="form-control" formControlName="cartonQty"
                        placeholder="Enter">
                    </div>
                  </td>
                  <td>
                    <div class="form-group form-group-100"
                      [ngClass]="{'form-error': contact.get('looseQty').invalid && contact.get('looseQty').touched}">
                      <input mask="separator.0" thousandSeparator="" [maxLength]="utilsService.validationService.MAX_5"
                        [disableControl]="isMarkaSelected" type="text" class="form-control" formControlName="looseQty"
                        placeholder="Enter">
                    </div>
                  </td>
                  <td class="tbl-action">
                    <div class="tbl-action-group">
                      <button (click)="openRemoveStockShiftingItemModal(i)" class="btn btn-xs btn-light-danger btn-icon"
                        ngbTooltip="Delete" placement="bottom" container="body" triggers="hover"><i
                          class="th th-outline-trash"></i>
                      </button>
                    </div>
                  </td>

                </tr>
              </tbody>

              <tfoot>
                <tr class="tbl-add-new">
                  <td colspan="100">
                    <button (click)="addItems()" class="btn btn-sm btn-link btn-icon-text text-primary">
                      <i class="th-bold-add-circle"></i> Add New Row
                    </button>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
        }
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>
            Save</button>
          <button [routerLink]="['/users/inventory/stock-shifting']" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ---------------------------- Delete Modal ----------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="stockShiftingDeleteItemModal" tabindex="-1"
  aria-labelledby="stockShiftingDeleteItemModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Remove <br /> <b>{{itemName() ? itemName() : "Selected"}}</b> Associated Item</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeStockShiftingItem()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i> Remove</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ---------------------------- Delete Modal ----------------------------- -->

<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="warehouseShiftChangeModal" tabindex="-1"
  aria-labelledby="warehouseShiftChangeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to switch Warehouse? </p>
            <p><b>Note:</b> Existing Items will be removed.</p>
          </div>
        </div>

        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="warehouseChanged()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>
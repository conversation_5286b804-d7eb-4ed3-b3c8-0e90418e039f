<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.purchaseOrderSearch" type="text"
                    class="form-control" placeholder="Search by ID">
            </div>
        </div>
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearchPOItemComments($event)" [(ngModel)]="paginationRequest.searchPurchaseComments"
                    type="text" class="form-control" placeholder="Search by SKU & Comment">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true" [alwaysShowCalendars]="true"
                    [ranges]="utilsService.ranges" [linkedCalendars]="false" [showClearButton]="false"
                    [autoApply]="true" [showRangeLabelOnInput]="true" startKey="start" placeholder="PO Date"
                    endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Supplier" [multiple]="false" [clearable]="true"
                [items]="dropdown?.supplierDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.supplierId">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Customer" [multiple]="false" [clearable]="true"
                [items]="dropdown?.customerDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.customerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>

    </div>
    <div class="page-filters-right">
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme table table-bordered tbl-collapse">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Shop No
                        </th>
                        <th>Supplier Name</th>
                        <th>Mobile No</th>
                        <th>Amount Due (RMB)</th>
                        <th>Total Amount (RMB)</th>
                        <th>Action</th>
                    </tr>
                </thead>


                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.supplierShortCode}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.supplierName}}</td>
                            <td>{{item.countryExtension}} {{item.phone}}</td>
                            <td>
                                <span class="fw-600"
                                    [ngClass]="{'text-success': item.supplierBalance > 0, 'text-danger': item.supplierBalance < 0}">
                                    {{Math.abs(item.supplierBalance) ? (Math.abs(item.supplierBalance) | indianCurrency) : '-'}}
                                </span>
                            </td>
                            <td>
                                <span class="fw-600">{{item.supplierAmount ? (item.supplierAmount | indianCurrency) : '-'}}</span>
                            </td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button (click)="downloadPOPDF(item.supplierId, item.supplierShortCode)"
                                        class="btn btn-outline-primary btn-icon-text btn-sm">
                                        <i class="bi bi-download "></i>
                                        Download PO
                                    </button>
                                    <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_RC_CHINA}"
                                        (click)="openPoToChina(item, null, false)" class="btn btn-xs btn-light-primary btn-icon"
                                        ngbTooltip="Move To Receive China" placement="left" container="body" triggers="hover">
                                        <i class="th-outline-arrow-right-1"></i>
                                    </button>
                                    <button (click)="toggleExpand(i)" class="btn btn-xs btn-light-white btn-icon"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [ngClass]="{'show': item.isExpand}" [id]="'table-collapse-2' + i">
                            <td colspan="30" class="tbl-collapse-child tbl-collapse-child-responsive">
                        
                                <div class="table-responsive">
                                    <table class="table-theme table table-bordered table-sticky">
                                        <thead class="border-less">
                                            <tr class="tbl-bg-light-three">
                                                <ng-container
                                                    *ngFor="let th of headerObj?.optionsArray; index as i">
                                                    <th *ngIf="th.show" [class]="th.class">
                                                        {{th.header}}
                                                    </th>
                                                </ng-container>
                                            </tr>
                                            <tr>
                                                <ng-container
                                                    *ngFor="let th of headerObj?.optionsArray; index as k">
                                                    <th *ngIf="th.show" [class]="th.class" [innerHTML]="th.displayName"></th>
                                                </ng-container>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr [ngClass]="{'tbl-bg-danger': child.purchaseRationFlag}" *ngFor="let child of item.poImportItemList; index as l; trackBy: trackByChild">
                                                <ng-container *ngFor="let column of headerObj.columnArr;">
                                                    <td class="tbl-user" *ngIf="column.show">
                                                        <ng-container>
                        
                                                            <ng-container *ngIf="column.key =='item'">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <span>{{(l + 1) | padNum}}.</span>
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image" *ngIf="child?.item">
                                                                            <img *ngIf="child.item?.formattedName" loading="lazy"
                                                                                [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                alt="valamji">
                                                                            <ng-container *ngIf="!child.item?.formattedName">{{
                                                                                child.displayName?.charAt(0).toUpperCase()
                                                                                }}
                                                                            </ng-container>
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{child.item?.skuId}}
                                                                                </p>
                                                                                <span class="tbl-description">{{child.item.displayName}}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown"
                                                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM, this.utilsService.enumForPage.EDIT_PO])">
                                                                            <button class="btn btn-xs btn-light-white btn-icon"
                                                                                id="actionDropDown" data-bs-toggle="dropdown"
                                                                                aria-expanded="false"
                                                                                data-bs-popper-config='{"strategy":"fixed"}'
                                                                                ngbTooltip="More Option" placement="bottom" container="body"
                                                                                triggers="hover" ngbTooltip="More Option" placement="bottom"
                                                                                container="body" triggers="hover">
                                                                                <i class="th th-outline-more"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_PO}">
                                                                                    <a class="dropdown-item" (click)="redirectToDetails.emit()"
                                                                                        [routerLink]="'/users/purchases/po-import/edit-po-created/' + child.poId"><i
                                                                                            class="th th-outline-edit"></i>
                                                                                        Edit
                                                                                    </a>
                                                                                </li>
                                                                                <li
                                                                                    [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                    <a class="dropdown-item"
                                                                                        (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                        <i class="th th-outline-eye"></i>View Item Details
                                                                                    </a>
                                                                                </li>
                                                                                <ng-container>
                                                                                    <hr class="dropdown-divider"
                                                                                        *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                    <li
                                                                                        [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO_ITEM}">
                                                                                        <a (click)="onItemDel(child, null)"
                                                                                            class="dropdown-item text-danger">
                                                                                            <i class="th th-outline-trash"></i>Delete
                                                                                        </a>
                                                                                    </li>
                                                                                </ng-container>
                                                                            </ul>
                        
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                        
                        
                                                            <ng-container *ngIf="column.key =='purchaseBreachQty'">
                                                                <div>{{(child.item.levelBreachQtys
                                                                    &&
                                                                    child.item.levelBreachQtys.breachQtys)
                                                                    ?
                                                                    (child.item.levelBreachQtys.levelName
                                                                    + ': ' +
                                                                    child.item.levelBreachQtys.breachQtys)
                                                                    : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='toFrom'">
                                                                {{child?.item?.seasonMaster?.fromDate
                                                                ?
                                                                ((child.item.seasonMaster.fromDate |
                                                                date:
                                                                'dd-MM') + ' to ' +
                                                                (child.item.seasonMaster.toDate |
                                                                date:
                                                                'dd-MM')) : '-'}}
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='advanceDate'">
                                                                <span class="text-center">
                                                                    {{child.item?.seasonMaster?.advanceDate
                                                                    ?
                                                                    (child.item?.seasonMaster?.advanceDate
                                                                    |
                                                                    date:
                                                                    'dd-MM') : '-'}}</span>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='averagePriceWithoutGST'">
                                                                {{child?.item?.averagePriceWithoutGST ? child?.item?.averagePriceWithoutGST : '-'}}
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key =='averagePriceWithGST'">
                                                                {{child?.item?.averagePriceWithGST ? child?.item?.averagePriceWithGST : '-'}}
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key =='poNumber'">
                                                                <span class="tbl-bold">{{child.poNumber ? child.poNumber : '-'}}</span>
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key =='poCreatedDate'">
                                                                <span>{{child.poCreatedDate ? (child.poCreatedDate | date: 'dd/MM/yyyy') : '-'}}</span>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='salesPrice'">
                                                                <div>
                                                                    {{child.item.itemPrice ?
                                                                    child.item.itemPrice : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='marka'">
                                                                <span class="d-flex flex-column align-items-start">
                                                                    <div>{{child.marka ? child.marka
                                                                        : '-'}}</div>
                                                                    <div>
                                                                        <ng-container>
                                                                            {{child.cartonLength ?
                                                                            child.cartonLength :
                                                                            '-'}} X
                                                                            {{child.cartonWidth ?
                                                                            child.cartonWidth :
                                                                            '-'}} X
                                                                            {{child.cartonHeight ?
                                                                            child.cartonHeight :
                                                                            '-'}}
                                                                            {{child?.cartonDimensionUnit ?
                                                                            child?.cartonDimensionUnit?.shortCode :
                                                                            ''}}
                                                                        </ng-container>
                                                                    </div>
                                                                    <div>
                                                                        <p>{{child.pricePerCarton ?
                                                                            child.pricePerCarton :
                                                                            '-'}}</p>
                                                                    </div>
                                                                    <div>
                                                                        <p class="tbl-po-notes">{{child.chinaComment ?
                                                                            child.chinaComment :
                                                                            ''}}</p>
                                                                    </div>
                                                                    <div class="fs-14">
                                                                        <b>{{child.item?.skuId || ''}}</b>
                                                                    </div>
                                                                </span>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='color'">
                                                                <span class="w-100 d-block" *ngFor="let v of child.colorName">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                </span>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='note'">
                                                                <div class="tbl-po-notes">
                                                                    {{child.note ? child.note :
                                                                    '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='englishComment'">
                                                                <div class="tbl-po-notes">
                                                                    {{child.englishComment ?
                                                                    child.englishComment : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='chinaComment'">
                                                                <div class="tbl-po-notes">
                                                                    {{child.chinaComment ?
                                                                    child.chinaComment : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='expectedDeliveryDate'">
                                                                <div>
                                                                    {{child.expectedDeliveryDate ?
                                                                    (child.expectedDeliveryDate |
                                                                    date: 'dd/MM/yyyy') : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='poCarton'">
                                                                {{child.poCarton ? child.poCarton :
                                                                '-'}}
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='pricePerCarton'">
                                                                {{child.pricePerCarton ?
                                                                child.pricePerCarton : '-'}}
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='totalPcsQty'">
                                                                {{(child.totalPcsQty) ?
                                                                (child.totalPcsQty)
                                                                : '-'}}
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='pendingQty'">
                                                                <div>
                                                                    {{child.pendingQtyCarton ?
                                                                    (child.pendingQtyCarton) : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='totalPendingQty'">
                                                                <div>
                                                                    {{child.totalPendingQty ?
                                                                    (child.totalPendingQty) : '-'}}
                                                                </div>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='pricePerItem'">
                                                                {{child.pricePerItem ?
                                                                (child.pricePerItem | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalAmount'">
                                                                {{child.Total_Price ? (child.Total_Price | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='expDeliveryCost'">
                                                                {{child.expDeliveryCost ?
                                                                child.expDeliveryCost : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalAmountWithExp'">
                                                                {{child.totalAmountWithExp ?
                                                                (child.totalAmountWithExp | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='conversationRate'">
                                                                {{child.conversationRate ? child.conversationRate : '-'}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalAmountWithExpInINR'">
                                                                {{child.totalAmountWithExpInINR ?
                                                                (child.totalAmountWithExpInINR | indianCurrency) :
                                                                '-'}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='chinaFinalExpextedCode'">
                                                                {{child.chinaFinalExpextedCode ?
                                                                child.chinaFinalExpextedCode : '-'}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='shippingTypes'">
                                                                {{child.shippingTypes ? (child.shippingTypes.value) :'-'}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='itemDim'">
                                                                {{child.cartonLength ?
                                                                child.cartonLength : '-'}} x
                                                                {{child.cartonWidth ?
                                                                child.cartonWidth : '-'}}
                                                                x
                                                                {{child.cartonHeight ?
                                                                child.cartonHeight : '-'}}
                                                                {{child?.cartonDimensionUnit ?
                                                                child?.cartonDimensionUnit?.shortCode :
                                                                ''}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='dimAge'">
                                                                {{child.dimAge}}
                                                            </ng-container>
                        
                                                            <ng-container>
                                                                <ng-container *ngIf="column.key =='CBM/Carton'">
                                                                    {{child.cbmCarton ? child.cbmCarton : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='Total CBM'">
                                                                    {{child.totalCbm ? child.totalCbm :
                                                                    0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='CBM Price'">
                                                                    {{child.cbmPrice ?
                                                                    (child.cbmPrice | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='TotalCBMExpense'">
                                                                    {{child.totalCBMExpense ? (child.totalCBMExpense | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='ShippingExpense/PCS'">
                                                                    {{child.shippingExpense ? (child.shippingExpense | indianCurrency) : 0}}
                                                                </ng-container>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='transportCharges'">
                                                                {{child.transportCharges ?
                                                                (child.transportCharges | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalTransportationChargesM2S'">
                                                                {{child.TotalTransportationCharges ?
                                                                (child.TotalTransportationCharges | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='transportationChargesM2SperPCS'">
                                                                {{child.Transportationcharges_Mumbai_to_Surat ?
                                                                (child.Transportationcharges_Mumbai_to_Surat | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalInsurance'">
                                                                {{child.TotalInsurance ?
                                                                (child.TotalInsurance | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='insurancePerPcs'">
                                                                {{child.insurancePCS ?
                                                                (child.insurancePCS | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='gstAmtPerPcs'">
                                                                {{child.GSTAmount_PCS ? (child.GSTAmount_PCS | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='craneExpense'">
                                                                {{child.craneExpense ?
                                                                (child.craneExpense | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='craneExpPcs'">
                                                                {{child.CraneExpense_PCS ? (child.CraneExpense_PCS | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalExp'">
                                                                {{child.Total_Expense ? (child.Total_Expense | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='chinaToSuratPadtar'">
                                                                {{child.ChinaToSuratFinalPrice ? (child.ChinaToSuratFinalPrice | indianCurrency)
                                                                : 0}}
                                                            </ng-container>
                        
                                                            <ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeight'">
                                                                    {{child.Weight_Carton ?
                                                                    child.Weight_Carton : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalWeight'">
                                                                    {{child.total_Weight ? child.total_Weight : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeightRu'">
                                                                    {{child.Weight_kg ?
                                                                    child.Weight_kg : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalLoadAmt'">
                                                                    {{child.total_load_amt_Ru ? child.total_load_amt_Ru : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='shippingCostperPieceINR'">
                                                                    {{child.shipingCost ? child.shipingCost : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalShippingExpWeight'">
                                                                    {{child.totalShippingExpense ? child.totalShippingExpense : 0}}
                                                                </ng-container>
                                                            </ng-container>
                        
                                                            <ng-container>
                        
                                                                <ng-container *ngIf="column.key =='percentage'">
                                                                    {{child.Percentage ? child.Percentage :
                                                                    0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalExpPCSper'">
                                                                    {{child.totalExpense ?
                                                                    child.totalExpense : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalFinalCostPCSper'">
                                                                    {{child.totalFinalCost ?
                                                                    child.totalFinalCost : 0}}
                                                                </ng-container>
                        
                                                            </ng-container>
                        
                        
                                                            <ng-container>
                                                                <ng-container *ngIf="column.key ==50">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key ==51">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='expensePcs'">
                                                                {{child.Expense_PCS ?
                                                                (child.Expense_PCS | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalItemAmt'">
                                                                {{child.totalItemAmount ? (child.totalItemAmount | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                        
                                                            <ng-container *ngIf="column.key =='lastRecord'">
                                                                <ng-container *ngFor="let ls of child?.lastRecord">
                                                                    {{ls?.supplierShortCode}},
                                                                    {{ls?.pricePerCarton}},
                                                                    {{ls?.poCarton}} <br />
                                                                </ng-container>
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key ==55">
                                                                @for(ls of child?.inquiryShopWithPrice; track $index){
                                                                    <span class="w-100 d-block">{{ls?.supplierShortCode}}, {{ls?.pricePerItem}}</span>
                                                                } @empty{
                                                                  <span>-</span>
                                                                }
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key ==56">
                                                                <div *ngIf="child.customerCount && child.inquiryCount; else elseBlockInq">
                                                                    {{child.customerCount}} {{'[' + child.inquiryCount + ']'}}
                                                                </div>
                                                                <ng-template #elseBlockInq>
                                                                    -
                                                                </ng-template>
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key ==57">
                                                                {{child.gst_amounts ? (child.gst_amounts | indianCurrency ): 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key ==58">
                                                                {{child.gstPers ? (child.gstPers ): 0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='extraExpense'">
                                                                {{child.extraExpense ?
                                                                (child.extraExpense | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='purchaseRation'">
                                                                {{child.purchaseRatio ?
                                                                child.purchaseRatio : 0}}
                                                            </ng-container>

                                                            <!-- New Cols -->
                                                            <ng-container *ngIf="column.key == 'itemDimension'">
                                                                @if(child?.itemLength || child?.itemWidth || child?.itemHeight){
                                                                {{child.itemLength ? child.itemLength : '-'}} x
                                                                {{child.itemWidth ? child.itemWidth : '-'}} x
                                                                {{child.itemHeight ? child.itemHeight : '-'}}
                                                                {{child?.itemDimUnitMasterName ?child?.itemDimUnitMasterName : ''}}
                                                                } @else {
                                                                -
                                                                }
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'itemWeight'">
                                                                {{child.itemWeight ? child.itemWeight : '-'}} {{child.itemWeight ? "KG" : ''}}
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'itemDimensionWithBox'">
                                                                @if(child?.itemWithBoxLength || child?.itemWithBoxWidth || child?.itemWithBoxHeight){
                                                                {{child.itemWithBoxLength ? child.itemWithBoxLength : '-'}} x
                                                                {{child.itemWithBoxWidth ? child.itemWithBoxWidth : '-'}} x
                                                                {{child.itemWithBoxHeight ? child.itemWithBoxHeight : '-'}}
                                                                {{child?.itemWithBoxDimUnitMasterName ?child?.itemWithBoxDimUnitMasterName : ''}}
                                                                } @else {
                                                                -
                                                                }
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'itemWeightWithBox'">
                                                                {{child.itemWithBoxWeight ? child.itemWithBoxWeight : '-'}} {{child.itemWithBoxWeight ? "KG" : ''}}
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'tag'">
                                                                {{child.tag ? child.tag : '-'}}
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'packingType'">
                                                                @for(item of child.packingTypes; track $index){
                                                                    <span class="w-100 d-block">{{item}}</span>
                                                                }
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'measurementCode'">
                                                                {{child.measurementCode ? child.measurementCode : '-'}}
                                                            </ng-container>
                                                            
                                                            <ng-container *ngIf="column.key == 'isPhotosRequired'">
                                                                <strong>{{child.isPhotosRequired ? 'Yes' : 'No'}}</strong>
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key == 'isQCRequired'">
                                                                <strong>{{child.isQCRequired ? 'Yes' : 'No'}}</strong>
                                                            </ng-container>
                        
                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                        
                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>

            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
</div>
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { SaveResMarkaWise } from '@modal/SalesOrder';
import { UPCOMING_SO_STOCK } from '../../constants/constant';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-qty-sales-modal',
  templateUrl: './qty-sales-modal.component.html',
  styleUrls: ['./qty-sales-modal.component.css']
})
export class QtySalesModalComponent implements OnInit, OnChanges {

  upcomingFlag = UPCOMING_SO_STOCK

  @Input({ alias: 'inputBooleansObj', required: true }) inputBooleansObj: { isMarkaPresent: boolean, isLoose: boolean };
  @Input({ alias: 'dropdown', required: true }) dropdown: NgbDropdown;
  @Input({ alias: 'activeTabQtyTab', required: true }) activeTabQtyTab: number;
  @Input({ alias: 'saveResMarkaWise', required: true }) saveResMarkaWise: SaveResMarkaWise[];
  @Input({ alias: 'isUpcomingTab', required: false }) isUpcomingTab: boolean = false;
  @Input({ alias: 'isListingPage', required: true }) isListingPage: boolean;

  constructor() { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['saveResMarkaWise'] && changes['saveResMarkaWise'].currentValue?.length === 1 && changes['saveResMarkaWise'].currentValue[0].branchName === this.upcomingFlag) {
      this.onOpenQty(0, this.saveResMarkaWise[0]);
    }

    if (changes['saveResMarkaWise'] && !changes['saveResMarkaWise'].currentValue?.find((a: SaveResMarkaWise) => a.branchName === this.upcomingFlag)) {
      this.onOpenQty(0, this.saveResMarkaWise[0]);
    }
  }

  ngOnInit() {
  }

  onOpenQty = (b: number, tab: SaveResMarkaWise) => {
    this.activeTabQtyTab = b;
    if(tab?.branchName) {
      tab.branchName === this.upcomingFlag ? this.isUpcomingTab = true : this.isUpcomingTab = false
    }
  }

}

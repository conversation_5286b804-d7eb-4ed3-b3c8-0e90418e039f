import { deserializeAs, serializeAs } from 'cerialize';
import { POImportItem } from './POImportItem';

export class CartonMapping {

    @serializeAs('importedItem')
    @deserializeAs('importedItem')
    private _importedItem: POImportItem;

    @serializeAs('dropLocations')
    @deserializeAs('dropLocations')
    private _dropLocations: any[];

    @serializeAs('dropLocationId')
    @deserializeAs('dropLocationId')
    private _dropLocationId: number;

    @serializeAs('item')
    @deserializeAs('item')
    private _item: any;

    @serializeAs('totalCarton')
    @deserializeAs('totalCarton')
    private _totalCarton: number;

    @serializeAs('loadedItemMapID')
    @deserializeAs('loadedItemMapID')
    private _loadedItemMapID: number;

    @serializeAs('customerOrderQty')
    @deserializeAs('customerOrderQty')
    private _customerOrderQty: number;

    @serializeAs('branches')
    @deserializeAs('branches')
    private _branches: any[];

    @deserializeAs('branchHeaderList')
    private _branchHeaderList: any[];

    @deserializeAs('pending')
    private _pending: number;

    @serializeAs('cartonMappingID')
    @deserializeAs('cartonMappingID')
    private _cartonMappingID: number;

    @serializeAs('assignedCarton')
    @deserializeAs('assignedCarton')
    private _assignedCarton: number;

    @serializeAs('dimUnitShortCode')
    @deserializeAs('dimUnitShortCode')
    private _dimUnitShortCode: string;

    @serializeAs('warehouses')
    @deserializeAs('warehouses')
    private _warehouses: WarehouseMapping[];

    @serializeAs('branchCartonMappingID')
    @deserializeAs('branchCartonMappingID')
    private _branchCartonMappingID: number;

    @serializeAs('locations')
    @deserializeAs('locations')
    private _locations: any[];
   
    @serializeAs('releasedDate')
    @deserializeAs('releasedDate')
    private _releasedDate: any;
   
    @serializeAs('containerName')
    @deserializeAs('containerName')
    private _containerName: any;

    @serializeAs('customerOrderQtyList')
    @deserializeAs('customerOrderQtyList')
    private _customerOrderQtyList: any[];

    constructor() {
        this.branches = [];
        this.locations = [];
        this.warehouses = [];
        this.dropLocations = [];
        this.customerOrderQtyList = [];
    }


    /**
     * Getter containerName
     * @return {any}
     */
	public get containerName(): any {
		return this._containerName;
	}

    /**
     * Setter containerName
     * @param {any} value
     */
	public set containerName(value: any) {
		this._containerName = value;
	}


    /**
     * Getter customerOrderQtyList
     * @return {any[]}
     */
	public get customerOrderQtyList(): any[] {
		return this._customerOrderQtyList;
	}

    /**
     * Setter customerOrderQtyList
     * @param {any[]} value
     */
	public set customerOrderQtyList(value: any[]) {
		this._customerOrderQtyList = value;
	}


    /**
     * Getter dropLocations
     * @return {any[]}
     */
	public get dropLocations(): any[] {
		return this._dropLocations;
	}

    /**
     * Setter dropLocations
     * @param {any[]} value
     */
	public set dropLocations(value: any[]) {
		this._dropLocations = value;
	}

    /**
     * Getter dropLocationId
     * @return {number}
     */
	public get dropLocationId(): number {
		return this._dropLocationId;
	}

    /**
     * Setter dropLocationId
     * @param {number} value
     */
	public set dropLocationId(value: number) {
		this._dropLocationId = value;
	}


    /**
     * Getter locations
     * @return {any[]}
     */
	public get locations(): any[] {
		return this._locations;
	}

    /**
     * Setter locations
     * @param {any[]} value
     */
	public set locations(value: any[]) {
		this._locations = value;
	}


    /**
     * Getter importedItem
     * @return {POImportItem}
     */
	public get importedItem(): POImportItem {
		return this._importedItem;
	}

    /**
     * Getter item
     * @return {any}
     */
	public get item(): any {
		return this._item;
	}

    /**
     * Getter totalCarton
     * @return {number}
     */
	public get totalCarton(): number {
		return this._totalCarton;
	}

    /**
     * Getter loadedItemMapID
     * @return {number}
     */
	public get loadedItemMapID(): number {
		return this._loadedItemMapID;
	}

    /**
     * Getter customerOrderQty
     * @return {number}
     */
	public get customerOrderQty(): number {
		return this._customerOrderQty;
	}

    /**
     * Getter branches
     * @return {any[]}
     */
	public get branches(): any[] {
		return this._branches;
	}

    /**
     * Getter branchHeaderList
     * @return {any[]}
     */
	public get branchHeaderList(): any[] {
		return this._branchHeaderList;
	}

    /**
     * Getter pending
     * @return {number}
     */
	public get pending(): number {
		return this._pending;
	}

    /**
     * Getter cartonMappingID
     * @return {number}
     */
	public get cartonMappingID(): number {
		return this._cartonMappingID;
	}

    /**
     * Getter assignedCarton
     * @return {number}
     */
	public get assignedCarton(): number {
		return this._assignedCarton;
	}

    /**
     * Getter dimUnitShortCode
     * @return {string}
     */
	public get dimUnitShortCode(): string {
		return this._dimUnitShortCode;
	}

    /**
     * Getter warehouses
     * @return {WarehouseMapping[]}
     */
	public get warehouses(): WarehouseMapping[] {
		return this._warehouses;
	}

    /**
     * Getter branchCartonMappingID
     * @return {number}
     */
	public get branchCartonMappingID(): number {
		return this._branchCartonMappingID;
	}

    /**
     * Setter importedItem
     * @param {POImportItem} value
     */
	public set importedItem(value: POImportItem) {
		this._importedItem = value;
	}

    /**
     * Setter item
     * @param {any} value
     */
	public set item(value: any) {
		this._item = value;
	}

    /**
     * Setter totalCarton
     * @param {number} value
     */
	public set totalCarton(value: number) {
		this._totalCarton = value;
	}

    /**
     * Setter loadedItemMapID
     * @param {number} value
     */
	public set loadedItemMapID(value: number) {
		this._loadedItemMapID = value;
	}

    /**
     * Setter customerOrderQty
     * @param {number} value
     */
	public set customerOrderQty(value: number) {
		this._customerOrderQty = value;
	}

    /**
     * Setter branches
     * @param {any[]} value
     */
	public set branches(value: any[]) {
		this._branches = value;
	}

    /**
     * Setter branchHeaderList
     * @param {any[]} value
     */
	public set branchHeaderList(value: any[]) {
		this._branchHeaderList = value;
	}

    /**
     * Setter pending
     * @param {number} value
     */
	public set pending(value: number) {
		this._pending = value;
	}

    /**
     * Setter cartonMappingID
     * @param {number} value
     */
	public set cartonMappingID(value: number) {
		this._cartonMappingID = value;
	}

    /**
     * Setter assignedCarton
     * @param {number} value
     */
	public set assignedCarton(value: number) {
		this._assignedCarton = value;
	}

    /**
     * Setter dimUnitShortCode
     * @param {string} value
     */
	public set dimUnitShortCode(value: string) {
		this._dimUnitShortCode = value;
	}

    /**
     * Setter warehouses
     * @param {WarehouseMapping[]} value
     */
	public set warehouses(value: WarehouseMapping[]) {
		this._warehouses = value;
	}

    /**
     * Setter branchCartonMappingID
     * @param {number} value
     */
	public set branchCartonMappingID(value: number) {
		this._branchCartonMappingID = value;
	}

    /**
     * Getter releasedDate
     * @return {any}
     */
	public get releasedDate(): any {
		return this._releasedDate;
	}

    /**
     * Setter releasedDate
     * @param {any} value
     */
	public set releasedDate(value: any) {
		this._releasedDate = value;
	}


}

export class WarehouseMapping {

    @serializeAs('allAisleRacks')
    @deserializeAs('allAisleRacks')
    private _allAisleRacks: any[];

    @serializeAs('aisleRackQty')
    @deserializeAs('aisleRackQty')
    private _aisleRackQty: any[];

    @serializeAs('breachQty')
    @deserializeAs('breachQty')
    private _breachQty: number;

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('sortOrder')
    @deserializeAs('sortOrder')
    private _sortOrder: number;

    @serializeAs('isMainWarehouse')
    @deserializeAs('isMainWarehouse')
    private _isMainWarehouse: boolean;

    @serializeAs('warehouseName')
    @deserializeAs('warehouseName')
    private _warehouseName: string;

    @serializeAs('transferQty')
    @deserializeAs('transferQty')
    private _transferQty: number;

    @serializeAs('totalAvailQty')
    @deserializeAs('totalAvailQty')
    private _totalAvailQty: number;

    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: number;

    @serializeAs('availableQty')
    @deserializeAs('availableQty')
    private _availableQty: number;

    @serializeAs('wareHouseTransferID')
    @deserializeAs('wareHouseTransferID')
    private _wareHouseTransferID: number;

    @serializeAs('dropLocationId')
    @deserializeAs('dropLocationId')
    private _dropLocationId: number;

    constructor() {
        this.allAisleRacks = []
        this.aisleRackQty = [];
    }


    /**
     * Getter dropLocationId
     * @return {number}
     */
	public get dropLocationId(): number {
		return this._dropLocationId;
	}

    /**
     * Setter dropLocationId
     * @param {number} value
     */
	public set dropLocationId(value: number) {
		this._dropLocationId = value;
	}
    

    /**
     * Getter sortOrder
     * @return {number}
     */
	public get sortOrder(): number {
		return this._sortOrder;
	}

    /**
     * Setter sortOrder
     * @param {number} value
     */
	public set sortOrder(value: number) {
		this._sortOrder = value;
	}


    /**
     * Getter allAisleRacks
     * @return {any[]}
     */
	public get allAisleRacks(): any[] {
		return this._allAisleRacks;
	}

    /**
     * Setter allAisleRacks
     * @param {any[]} value
     */
	public set allAisleRacks(value: any[]) {
		this._allAisleRacks = value;
	}

    /**
     * Getter aisleRackQty
     * @return {any[]}
     */
	public get aisleRackQty(): any[] {
		return this._aisleRackQty;
	}

    /**
     * Setter aisleRackQty
     * @param {any[]} value
     */
	public set aisleRackQty(value: any[]) {
		this._aisleRackQty = value;
	}

    /**
     * Getter breachQty
     * @return {number}
     */
	public get breachQty(): number {
		return this._breachQty;
	}

    /**
     * Setter breachQty
     * @param {number} value
     */
	public set breachQty(value: number) {
		this._breachQty = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Getter isMainWarehouse
     * @return {boolean}
     */
	public get isMainWarehouse(): boolean {
		return this._isMainWarehouse;
	}

    /**
     * Setter isMainWarehouse
     * @param {boolean} value
     */
	public set isMainWarehouse(value: boolean) {
		this._isMainWarehouse = value;
	}

    /**
     * Getter warehouseName
     * @return {string}
     */
	public get warehouseName(): string {
		return this._warehouseName;
	}

    /**
     * Setter warehouseName
     * @param {string} value
     */
	public set warehouseName(value: string) {
		this._warehouseName = value;
	}

    /**
     * Getter transferQty
     * @return {number}
     */
	public get transferQty(): number {
		return this._transferQty;
	}

    /**
     * Setter transferQty
     * @param {number} value
     */
	public set transferQty(value: number) {
		this._transferQty = value;
	}

    /**
     * Getter totalAvailQty
     * @return {number}
     */
	public get totalAvailQty(): number {
		return this._totalAvailQty;
	}

    /**
     * Setter totalAvailQty
     * @param {number} value
     */
	public set totalAvailQty(value: number) {
		this._totalAvailQty = value;
	}

    /**
     * Getter displayName
     * @return {number}
     */
	public get displayName(): number {
		return this._displayName;
	}

    /**
     * Setter displayName
     * @param {number} value
     */
	public set displayName(value: number) {
		this._displayName = value;
	}

    /**
     * Getter availableQty
     * @return {number}
     */
	public get availableQty(): number {
		return this._availableQty;
	}

    /**
     * Setter availableQty
     * @param {number} value
     */
	public set availableQty(value: number) {
		this._availableQty = value;
	}

    /**
     * Getter wareHouseTransferID
     * @return {number}
     */
	public get wareHouseTransferID(): number {
		return this._wareHouseTransferID;
	}

    /**
     * Setter wareHouseTransferID
     * @param {number} value
     */
	public set wareHouseTransferID(value: number) {
		this._wareHouseTransferID = value;
	}


}

export class CartonMappingAR {

    @serializeAs('warehouses')
    @deserializeAs('warehouses')
    private _warehouses: WarehouseMapping[];

    @serializeAs('itemList')
    @deserializeAs('itemList')
    private _itemList: CartonMappingItemList[];

    constructor() {
        this.itemList = []
    }


    /**
     * Getter warehouses
     * @return {WarehouseMapping[]}
     */
	public get warehouses(): WarehouseMapping[] {
		return this._warehouses;
	}

    /**
     * Setter warehouses
     * @param {WarehouseMapping[]} value
     */
	public set warehouses(value: WarehouseMapping[]) {
		this._warehouses = value;
	}

    /**
     * Getter itemList
     * @return {CartonMappingItemList[]}
     */
	public get itemList(): CartonMappingItemList[] {
		return this._itemList;
	}

    /**
     * Setter itemList
     * @param {CartonMappingItemList[]} value
     */
	public set itemList(value: CartonMappingItemList[]) {
		this._itemList = value;
	}


}

export class CartonMappingItemList {

    @serializeAs('wareHouseId')
    @deserializeAs('wareHouseId')
    private _wareHouseId: number;

    @serializeAs('items')
    @deserializeAs('items')
    private _items: CartonMapping[];

    @serializeAs('warehouses')
    @deserializeAs('warehouses')
    private _warehouses: WarehouseMapping;

    constructor() {
        this.items = []
    }


    /**
     * Getter items
     * @return {CartonMapping[]}
     */
	public get items(): CartonMapping[] {
		return this._items;
	}

    /**
     * Setter items
     * @param {CartonMapping[]} value
     */
	public set items(value: CartonMapping[]) {
		this._items = value;
	}

    /**
     * Getter warehouses
     * @return {WarehouseMapping}
     */
	public get warehouses(): WarehouseMapping {
		return this._warehouses;
	}

    /**
     * Setter warehouses
     * @param {WarehouseMapping} value
     */
	public set warehouses(value: WarehouseMapping) {
		this._warehouses = value;
	}

    /**
     * Getter wareHouseId
     * @return {number}
     */
	public get wareHouseId(): number {
		return this._wareHouseId;
	}

    /**
     * Setter wareHouseId
     * @param {number} value
     */
	public set wareHouseId(value: number) {
		this._wareHouseId = value;
	}

}
import { computed, effect, inject, Injectable, signal, TemplateRef } from '@angular/core';
import { EnumForStockWarehouseTransfer } from '@enums/EnumForStockWarehouseTransfer';
import { EnumForSWTStatus } from '@enums/EnumForSWTStatus';
import { EnumForTicketStatus } from '@enums/EnumForTicketStatus';
import { createArraySignal, createObjectSignal } from '@libs';
import { WarehouseTransferPagination } from '@modal/request/WarehouseTransferPagination';
import { StockShiftingTicketStatusData } from '@modal/StockShiftingList';
import { SWTListingItem, SWTListingMain, SWTMarkaData, SWTMarkaDataOriginal } from '@modal/StockWarehouseTransfer';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgbModalService } from '@service/ngb-modal.service';
import { UtilsService } from '@service/utils.service';
import dayjs from 'dayjs';
import { map, Subject, takeUntil, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StockWarehouseTransferService {

  utilsService = inject(UtilsService)
  modalService = inject(NgbModalService)

  enumForTab = EnumForStockWarehouseTransfer;
  enumForTicketStatus = EnumForTicketStatus;
  enumForSWTStatus = EnumForSWTStatus;
  selectedTab = signal<string>(null);

  paginationRequest = createObjectSignal({} as WarehouseTransferPagination);
  swtList = createArraySignal([] as SWTListingMain[]);
  swtObj = createObjectSignal({} as SWTListingMain);
  isSwtListEmpty = computed(() => this.swtList.get().length === 0)

  dropdown = { warehouse: [] }
  destroy$ = new Subject<void>();
  private dateCallCount = 0;

  constructor() {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: "100", isRequestedByUs: true }));
  }

  onChangeTab(tab: string) {
    this.selectedTab.set(tab);
    this.swtList.set([]);

    switch (tab) {
      case this.enumForTab.REQUESTED_TO_US:
        this.paginationRequest.update(a => ({ ...a, isRequestedByUs: true }))
        break;
      case this.enumForTab.REQUESTED_BY_SELF:
        this.paginationRequest.update(a => ({ ...a, isRequestedByUs: false }))
        break;
    }

    this.onClear()
  }

  initPagination = () => {
    this.getRequiredData()
    this.onChangeTab(this.enumForTab.REQUESTED_BY_SELF)
  }

  getSWTListing = () => {
    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])

    this.utilsService.post(this.utilsService.serverVariableService.SWT_LISTING, param).pipe(
      map((res) => res.data),
      tap((res) => {
        this.paginationRequest.update(a => ({ ...a, totalData: res?.['totalElements'], pagination: res.pagination }));
      }),
      map((res) => res?.['content']),
      tap((data: SWTListingMain[]) => {
        this.swtList.set(data);
        this.expandOnFilter(data);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getSWTAssociatedItem = (item: SWTListingMain, index: number) => {

    // check if already loaded
    const current = this.swtList.get()[index];
    if (current?.loadedChild) return;

    const param = this.utilsService.removeKeys(this.paginationRequest.get(), ['dateRange', 'totalData', 'pagination'])
    param.warehouseTransferId = item.id;
    param.warehouseToId = item.warehouseToId;

    this.utilsService.post(this.utilsService.serverVariableService.SWT_ASSOCIATED_ITEM_LISTING, param).pipe(
      map((res) => res.data),
      tap((data: SWTListingItem[]) => {
        this.swtList.updateAt(index, a => ({
          ...a,
          items: data.map(v => ({ ...v, marka: v?.marka ? v.marka.split(',').map((x: string) => x.trim()) : [] })),
          loadedChild: true
        }))
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onExpand = (index: number) => {
    this.swtList.updateAt(index, a => ({ ...a, isExpand: !a.isExpand }))
  }

  onExpandChild = (index: number, item: SWTListingMain) => {
    this.swtList.updateAt(index, a => ({ ...a, isExpandItem: !a.isExpandItem }))
    if (this.swtList.get()[index].isExpandItem) {
      this.getSWTAssociatedItem(item, index)
    }
  }

  onExpandToggle = () => {
    this.swtList.update(a => a.map(item => ({ ...item, isExpand: !item.isExpand })))
  }

  // Expand data if any filter applied and data found
  expandOnFilter = (list: SWTListingMain[] = this.swtList.get(), expandCount: number = 5) => {
    const { searchText, fromDate, toDate, warehouseId } = this.paginationRequest.get();
    const hasFilters = searchText || fromDate || toDate || warehouseId;

    if (hasFilters && list.length > 0) {
      this.swtList.update(items =>
        items.map((item, index) => ({ ...item, isExpandItem: index < expandCount, isExpand: index < expandCount }))
      );

      const limitedList = list.slice(0, expandCount);
      for (const item of limitedList) {
        const index = this.swtList.get().findIndex(i => i.id === item.id);
        if (index !== -1) {
          this.getSWTAssociatedItem(item, index);
        }
      }
    }
  };

  getRequiredData = () => {
    this.utilsService.get(this.utilsService.serverVariableService.SWT_WAREHOUSE_LISTING_DROPDOWN, null, true).pipe(
      tap(res => {
        this.dropdown.warehouse = res;
      })
    ).subscribe()
  }

  addPageSizeData = (event: any) => {
    this.paginationRequest.update(a => ({ ...a, pageNo: 1, pageSize: event }));
    this.getSWTListing();
  }

  pageNumber = (event: any) => {
    this.paginationRequest.update(a => ({ ...a, pageNo: event }));
    this.getSWTListing();
  }

  onChangeFilter = (event: any, type: 'date' | 'search' | 'warehouseId', isDateClear?: boolean, isAllClear = false) => {
    switch (type) {
      case 'date':
        this.dateCallCount++;

        // Skip first 2 calls
        if (this.dateCallCount <= 2 && (!event || (event?.start === null && event?.end === null)) && !isAllClear) {
          return;
        }
        if (event?.start === null && event?.end === null && !isDateClear) {
          return;
        }
        const fromDate = event?.start ? dayjs(event['start']).format('YYYY-MM-DD') : null;
        const toDate = event?.end ? dayjs(event['end']).format('YYYY-MM-DD') : null;
        this.paginationRequest.update(a => ({ ...a, dateRange: event, fromDate: fromDate, toDate: toDate }));
        break;
      case 'search':
        this.paginationRequest.update(a => ({ ...a, searchText: event.target.value }));
        break;
      case 'warehouseId':
        this.paginationRequest.update(a => ({ ...a, warehouseId: event }));
        break;
    }
    this.destroy$.complete()
    if (!isAllClear) {
      this.getSWTListing();
    }
  }

  onClearDateOnly = () => {
    this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null }));
    this.onChangeFilter(null, 'date', true);
  }

  // Filters Clear
  onClear = () => {
    this.dateCallCount = 0;
    switch (this.selectedTab()) {
      case this.enumForTab.REQUESTED_TO_US:
      case this.enumForTab.REQUESTED_BY_SELF:
        this.paginationRequest.update(a => ({ ...a, fromDate: null, toDate: null, dateRange: null, searchText: null, warehouseId: null }));
        this.onChangeFilter(null, 'date', true, true);
        this.getSWTListing();
        break;
    }
  }

  // Marka Popup Details
  getMarkaDataSWT = (item: SWTListingMain, child: SWTListingItem, index: number, childIndex: number) => {

    const API = `${this.utilsService.serverVariableService.SWT_GET_MARKA_DATA}`

    const param = {
      isCompulsoryMarka: true,
      warehouseId: item.warehouseFromId,
      itemId: child.itemId,
      associatedItemId: child.id
    }

    this.utilsService.post(API, param, null, true).pipe(
      tap((res: SWTMarkaDataOriginal[]) => {
        const groupedData: SWTMarkaData[] = Object.values(
          res.reduce((acc, curr) => {
            if (!acc[curr.marka]) {
              acc[curr.marka] = {
                marka: curr.marka,
                age: curr.age,
                totalLooseQty: 0,
                totalCartonQty: 0,
                locations: [],
              };
            }
        
            acc[curr.marka].locations.push({
              locationName: curr.locationName,
              locationType: curr.locationType,
              looseQty: curr.looseQty ?? 0,
              cartonQty: curr.cartonQty ?? 0,
              age: curr.age,
              piecesPerCarton: curr.piecesPerCarton || 0,
              reqQty: curr.reqQty,
              approveQty: curr.approveQty
            });
        
            acc[curr.marka].totalLooseQty += curr.looseQty ?? 0;
            acc[curr.marka].totalCartonQty += curr.cartonQty ?? 0;
        
            return acc;
          }, {} as Record<string, SWTMarkaData>)
        );
        this.swtList.updateAt(index, a => ({ ...a, items: a.items.map((item, idx) => idx === childIndex ? { ...item, markaDataConverted: groupedData } : item) }))
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  openRejectModal = (item: SWTListingMain, content: TemplateRef<any>) => {
    this.swtObj.set(item);
    this.modalService.open(content);
  }

  onReject = (modal: NgbModalRef) => {
    const id = this.swtObj.get().id;
    this.utilsService.post(`${this.utilsService.serverVariableService.SWT_REJECT_WAREHOUSE_TRANSFER}?id=${id}`, null, { toast: true }).pipe(
      tap(() => {
        this.modalService.close(modal);
        this.getSWTListing();
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  getTicketsStatusByTicketTypeAndAssociatedItem = (id: number, associatedItemId: number, index: number, subIndex: number) => {

    // const current = this.stockShiftingList.get()[index].items[subIndex];
    // if (current?.loadedChild) return;

    const param = {
      ticketType: "WT",
      ticketTypeId: id,
      associateItemId: associatedItemId
    }

    this.utilsService.post(this.utilsService.serverVariableService.GET_TICKETS_STATUS_BY_TICKET_TYPE_AND_ASSOCIATED_ITEM, param).pipe(
      map((res) => res.data),
      tap((data: StockShiftingTicketStatusData[]) => {
        this.swtList.updateAt(index, (item) => {
          const updatedItems = [...item.items];
          updatedItems[subIndex] = { ...updatedItems[subIndex], ticketData: data };
          return { ...item, items: updatedItems, loadedChild: true };
        });
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onRefresh = () => {
    this.getSWTListing();
  }

  openDeleteReqModal = (item: SWTListingMain, content: TemplateRef<any>) => {
    this.swtObj.set(item);
    this.modalService.open(content);
  }

  onDeleteReq = (modal: NgbModalRef) => {
    const id = this.swtObj.get().id;
    this.utilsService.delete(`${this.utilsService.serverVariableService.SWT_DELETE_WAREHOUSE_TRANSFER}?id=${id}`, { toast: true }).pipe(
      tap(() => {
        this.modalService.close(modal);
        this.getSWTListing();
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }
}

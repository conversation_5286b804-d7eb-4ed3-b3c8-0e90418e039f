import { InquiryItemDropdown } from "./SalesInquiry"
import { SalesOrderItemList } from "./SalesOrder"

export interface SalesOrderDropdowns {
  supplierTypes: { label: string, value: string }[]
  travellers: any[]
  transporters: { id: number, transporterName: string, isActive: boolean }[]
  deliveryPerson: DeliveryPerson[]
  waitForPaymentOptions: { label: string, value: string }[]
  filteredWaitForPaymentOptions: { label: string, value: string }[]
  gstAddress: any[]
  courierCompany: { id: number, courierName: string, isActive: boolean, zipCode: string }[]
  transportBranches: TransportBranch[]
  filteredTransportBranches: TransportBranch[]
  packingTypes: { id: number, packingName: string, isActive: boolean }[]
  shippingAddress: ShippingAddress[]
  bankGroups: { id: number, bankName: string, gstNo: string, isActive: boolean }[]
  customers: Customer[]
  paymentTypes: { id: number, paymentTypeName: string, isActive: boolean, defaultCode: string }[]
  orderUnit: { label: string, value: string }[]
  item: InquiryItemDropdown[]
  paymentTerms: { id: number, paymentTermName: string, isActive: boolean, noOfDays: number }[]
  deliveryTypes: { label: string, value: string }[]
  warehouses: { id: number, isActive: boolean, isMainWarehouse: boolean, warehouseName: string }[]
  countryExtensions: { label: string, value: number, isActive: boolean }[]
  salesOrderItems?: SalesOrderItemList[]
  deliveryType : any;
  shippingAddressId: any;
  transportedId: any;
  transportBranchFromId :any;
  transportBranchToId: any;
  courierMasterId: any;
  travellerId : any;
}

export interface DeliveryPerson {
  id: number
  name: string
  mobileNo: string
  isActive: boolean
  countryExtensionId: number
  countryExtension: string
}

export interface TransportBranch {
  id: number
  branchCode: string
  city: string
  address: string
  pinCode: string
  isActive: boolean
  displayName: string
  transportMasterId: number
}

export interface ShippingAddress {
  id: number
  zipCode: string
  isDefault: boolean
  isActive: boolean
  addressLine: {
    id: number
    addressName: string
    isActive: boolean
  }
  country: {
    id: number
    name: string
    code: string
    countryExtension: string
    originalName: string
    isActive: boolean
    isMarkAsPurchaseCountry: boolean
  }
  state: {
    id: number
    name: string
    code: string
    shippingDays: number
    isActive: boolean
    markAsPurchaseCity: boolean
    stateMaster: {
      id: number
      name: string
      code: string
      isActive: boolean
      markAsPurchaseState: boolean
    }
  }
  city: {
    id: number
    name: string
    code: string
    shippingDays: number
    isActive: boolean
    markAsPurchaseCity: boolean
    stateMaster: {
      id: number
      name: string
      code: string
      isActive: boolean
      markAsPurchaseState: boolean
    }
  }
}

export interface Customer {
  id?: number
  companyName?: string
  displayName?: string
  phone?: string
  isActive?: boolean
  isSaveAsDraft?: boolean
  formattedName?: string
  fullName?: string
  registrationTypeName?: string
  countryId?: number
  countryExtension?: string
}
import { ASC_ORDER, DESC_ORDER } from "../constants/constant";
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams, HttpResponse } from "@angular/common/http";
import { BranchSideBar } from "../constants/interface";
import { Deserialize } from "cerialize";
import { EnumForPOImportStatus } from "@enums/EnumForPOImportStatus.enum";
import { EnumForPages } from "@enums/EnumForPages";
import { Injectable } from "@angular/core";
import { ResponseWrapperDTO } from "@modal/response/ResponseWrapperDTO";
import { Router } from "@angular/router";
import { ServerVariableService } from "./server-variable.service";
import { catchError, filter, finalize, map, Observable, Subscription, tap, throwError } from "rxjs";
import { ToastrService } from "ngx-toastr";
import { ValidationService } from "./validation.service";
import { environment } from "@env/environment";
import moment from "moment";
import { saveAs } from 'file-saver';

type HttpConfig = {
  toast?: boolean;
  loader?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UtilsService {

  /** Variable to store the user's profile picture for display in navbar*/
  userProfilePicture: string = null;

  roleName: string = null;

  /** Variable to store the username for display in navbar*/
  username: string;

  /** Variable to store the username for display in navbar*/
  userId: number;

  /**List of API endpoints where the token is not required in the request header*/
  apisExcludingToken: string[] = [this.serverVariableService.LOGIN_API, this.serverVariableService.OTP_GENERATION, this.serverVariableService.OTP_VERIFICATION, this.serverVariableService.CHANGE_PASSWORD_FORGOT, this.serverVariableService.PREVIEW_MARKA_IMG];

  /**Initial configuration settings for the toast notifications*/
  toastConfig = {
    disableTimeOut: false,
    timeOut: 10000,
    positionClass: 'toast-top-right',
    closeButton: true,
  };

  userBranchList: BranchSideBar[] = [];
  defaultBranch: BranchSideBar = null;

  enumForPage = EnumForPages

  /** Variable for showing loader*/
  showLoader = 0;

  /** App Version */
  version: string = environment.VERSION

  enumForSortOrder = {
    D: DESC_ORDER,
    A: ASC_ORDER
  } as const

  imgPath: any = environment.API_URL + environment.IMG_FOLDER;
  blob: Blob;

  pageSize: string = '100'

  ranges: any = {
    'Today': [moment(), moment()],
    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'Last 14 Days': [moment().subtract(13, 'days'), moment()],
    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Previous Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
    'This Year': [moment().startOf('year'), moment().endOf('year')],
    'Previous Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
  }

  poImportStatus = EnumForPOImportStatus

  constructor(public http: HttpClient, public router: Router, public toasterService: ToastrService,
    public serverVariableService: ServerVariableService,
    public validationService: ValidationService,) { }

  /**
   * Handles a POST request to an API endpoint.
   * 
   * @param isDisplayToast Indicates whether to display toast messages on response.
   * @param apiName The name or URL of the API endpoint.
   * @param params The parameters to be sent with the POST request.
   * @param callback Response from server
   * @param isCallbackRequired Indicates whether a callback function is required.
   * @param noLoaderRequire Indicates whether to skip showing loader.
 */
  postMethodAPI(isDisplayToast: boolean, apiName: any, params: any, callback: (response: any, isRoute: boolean) => void, isCallbackRequired?: boolean, noLoaderRequire?: boolean): Subscription {
    this.showLoader++;
    if (noLoaderRequire) {
      this.showLoader--;
    }
    this.customJsonInclude(params);
    let headers = new HttpHeaders();
    if (this.apisExcludingToken.indexOf(apiName) < 0) {
      headers = headers.set('Authorization', `Bearer ${this.getToken()}`);
    }
    apiName = environment.API_URL + apiName;

    /**
     * Subscribing to observable
     */
    return this.http.post(apiName, params, { headers, observe: 'response' }).subscribe({
      next: (response: HttpResponse<any>) => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }

        /**
         *Response from the server
         */
        const serverResponse: ResponseWrapperDTO = Deserialize(response.body, ResponseWrapperDTO);

        // Validates successful server response status (200-299).
        // If successful, optionally displays a success toast and executes callback.
        if (!(serverResponse.status < 200 || serverResponse.status >= 300)) {
          if (isDisplayToast) {
            this.toasterService.success(serverResponse.message, '', {
              positionClass: 'toast-top-right',
              closeButton: true
            });
          }
          if (isCallbackRequired) {
            callback(serverResponse, true);
          } else {
            callback(serverResponse.data, true);
          }
        }

      },
      error: (error: HttpErrorResponse) => {
        if (error.status === 0) {
          this.toasterService.error('Unable to Connect with server, Please check your internet connectivity or wait for the network to establish the connection.', '', this.toastConfig);
        }
        else {
          const errorDTO = Deserialize(error.error, ResponseWrapperDTO);

          if (errorDTO.status === 403) {
            this.goToSessionExpired();
          } else if (errorDTO.status === 500) {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          } else {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
            if (isCallbackRequired) {
              callback(errorDTO.message, true);
            }
          }
        }
        this.showLoader--;
      }
    })
  }

    /**
   * Handles a GET request to an API endpoint.
   * 
   * @param apiName The name or URL of the API endpoint.
   * @param params The parameters to be sent with the GET request.
   * @param callback Response from server
   * @param noLoaderRequire Indicates whether to skip showing loader.
  */
  getMethodAPI(isDisplayToast: boolean, apiName: any, params: any, callback: (response: any) => void, noLoaderRequire?: boolean): Subscription {
    this.showLoader++;
    if (noLoaderRequire) {
      this.showLoader--;
    }
    let httpParams = new HttpParams();
    if (!this.isNullUndefinedOrBlank(params)) {
      Object.keys(params).forEach(key => {
        if (key && params[key] && params.hasOwnProperty(key) && !this.isEmptyObjectOrNullUndefined(params[key])) {
          httpParams = httpParams.append(key, params[key]);
        }
      });
    }
    let headers = new HttpHeaders();
    if (this.apisExcludingToken.indexOf(apiName) < 0) {
      headers = headers.set('Authorization', `Bearer ${this.getToken()}`);
    }
    apiName = environment.API_URL + apiName;

    /**
     * Subscribing to observable
     */
    return this.http.get(apiName, { params: httpParams, headers, observe: 'response' }).subscribe({
      next: (response: HttpResponse<any>) => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }

        /**
         *Response from the server
          */
        const serverResponse: ResponseWrapperDTO = Deserialize(response.body, ResponseWrapperDTO);

        // Validates successful server response status (200-299).
        // If successful, optionally displays a success toast and executes callback.
        if (!(serverResponse.status < 200 || serverResponse.status >= 300)) {
          if (isDisplayToast) {
            this.toasterService.success(serverResponse.message, '', {
              positionClass: 'toast-top-right',
              closeButton: true
            });
          }
        }
        if (serverResponse.status < 200 || serverResponse.status >= 300) {
          this.toasterService.error(serverResponse.message, '', this.toastConfig);
        }
        else {
          callback(serverResponse.data);
        }

      },
      error: (error: HttpErrorResponse) => {
        if (error.status === 0) {
          this.toasterService.error('Unable to Connect with server, Please check your internet connectivity or wait for the network to establish the connection.', '', this.toastConfig);
        }
        else {
          const errorDTO = Deserialize(error.error, ResponseWrapperDTO);

          if (errorDTO.status === 403) {
            this.goToSessionExpired();
          } else if (errorDTO.status === 500) {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          } else {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          }
        }
        this.showLoader--;
      }
    })
  }

  /**
   * Handles a PUT request to an API endpoint.
   * @param isDisplayToast Indicates whether to display toast messages on response.
   * @param apiName The name or URL of the API endpoint.
   * @param id
   * @param params The parameters to be sent with the PUT request.
   * @param callback Response from server
   * @param noLoaderRequire Indicates whether to skip showing loader.
   * @param isCallbackRequired Indicates whether a callback function is required.
 */
  putMethodAPI(isDisplayToast: boolean, apiName: any, params: any, id: any, callback: (responseData: any, isRoute: boolean) => void, isCallbackRequired?: boolean, noLoaderRequire?: boolean): Subscription {
    this.showLoader++;
    if (noLoaderRequire) {
      this.showLoader--;
    }
    let headers = new HttpHeaders();
    if (this.apisExcludingToken.indexOf(apiName) < 0) {
      headers = headers.set('Authorization', `Bearer ${this.getToken()}`);
    }
    apiName = id ? (environment.API_URL + apiName + '/' + id) : environment.API_URL + apiName;

    /**
     * Subscribing to observable
     */
    return this.http.put(apiName, params, { headers, observe: 'response' }).subscribe({
      next: (response: HttpResponse<any>) => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }

        /**
         *Response from the server
         */
        const serverResponse: ResponseWrapperDTO = Deserialize(response.body, ResponseWrapperDTO);

        // Validates successful server response status (200-299).
        // If successful, optionally displays a success toast and executes callback.
        if (!(serverResponse.status < 200 || serverResponse.status >= 300)) {
          if (isDisplayToast) {
            this.toasterService.success(serverResponse.message, '', {
              positionClass: 'toast-top-right',
              closeButton: true
            });
          }
          if (isCallbackRequired) {
            callback(serverResponse, true);
          } else {
            callback(serverResponse.data, true);
          }
          // callback(serverResponse.data, true);
        }

      },
      error: (error: HttpErrorResponse) => {
        if (error.status === 0) {
          this.toasterService.error('Unable to Connect with server, Please check your internet connectivity or wait for the network to establish the connection.', '', this.toastConfig);
        }
        else {
          const errorDTO = Deserialize(error.error, ResponseWrapperDTO);

          if (errorDTO.status === 403) {
            this.goToSessionExpired();
          } else if (errorDTO.status === 500) {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          } else {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
            if (isCallbackRequired) {
              callback(errorDTO.message, true);
            }
          }
        }
        this.showLoader--;
      }
    })
  }

  /**
   * Handles a DELETE request to an API endpoint.
   * @param isDisplayToast Indicates whether to display toast messages on response.
   * @param apiName The name or URL of the API endpoint.
   * @param params The parameters to be sent with the DELETE request.
   * @param callback Response from server
  */
  deleteMethodAPI(isDisplayToast: boolean, apiName: any, params: any, callback: (response: any) => void, noLoaderRequire?: boolean): Subscription {
    this.showLoader++;
    if (noLoaderRequire) {
      this.showLoader--;
    }
    let headers = new HttpHeaders();
    if (this.apisExcludingToken.indexOf(apiName) < 0) {
      headers = headers.set('Authorization', `Bearer ${this.getToken()}`);
    }
    apiName = environment.API_URL + apiName;

    /**
     * Subscribing to observable
     */
    return this.http.delete(apiName, { headers, observe: 'response', body: params },).subscribe({
      next: (response: HttpResponse<any>) => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }

        /**
         *Response from the server
         */
        const serverResponse: ResponseWrapperDTO = Deserialize(response.body, ResponseWrapperDTO);

        // Validates successful server response status (200-299).
        // If successful, optionally displays a success toast and executes callback.
        if (!(serverResponse.status < 200 || serverResponse.status >= 300)) {
          if (isDisplayToast) {
            this.toasterService.success(serverResponse.message, '', {
              positionClass: 'toast-top-right',
              closeButton: true
            });
          }
          callback(serverResponse.data);
        }

      },
      error: (error: HttpErrorResponse) => {
        if (error.status === 0) {
          this.toasterService.error('Unable to Connect with server, Please check your internet connectivity or wait for the network to establish the connection.', '', this.toastConfig);
        }
        else {
          const errorDTO = Deserialize(error.error, ResponseWrapperDTO);

          if (errorDTO.status === 403) {
            this.goToSessionExpired();
          } else if (errorDTO.status === 500) {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          } else {
            this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
          }
        }
        this.showLoader--;
      }
    })
  }

  /**
   * Retrieves the authentication token from the local storage.
   * 
   * @returns {string | null} The authentication token if it exists in the local storage, otherwise null.
   */

  getToken(): string | null {
    return localStorage.getItem('token') ? localStorage.getItem('token') : null;
  }

  /**
   * Checks if a value, array, object, string, or any other type, contains any elements that are either null, undefined, blank, or empty objects.
   * 
   * @param {any} value - The value to be checked.
   * @returns {boolean} True if the value contains null, undefined, blank, or empty objects; otherwise, false.
   */

  isEmptyObjectOrNullUndefined(...value): boolean {
    if (value && value.length > 0) {
      for (let i = 0; i < value.length; i++) {
        if (this.isNullUndefinedOrBlank(value[i]) || this.isEmptyObject(value[i])) {
          return true;
        }
      }
    }
    return false;
  }

  /** 
  *
  * Used to check if Object is empty, undefined or blank!
  * @param obj Object which needs to be checked
  * @returns {boolean} True if object is empty, undefined or blank.
  */
  isNullUndefinedOrBlank(obj): boolean {
    if (obj == null || obj === undefined || (obj === '' && obj !== 0)) {
      return true;
    }
    return false;
  }

  /** 
  *
  * Used to check if Object is empty or not..!
  * @param obj Object which needs to be checked
  * @returns {boolean} True if object is empty.
  */
  isEmptyObject(obj): boolean {
    return (obj && (Object.keys(obj).length === 0));
  }

  /**
   * Redirects to the specified route.
   * @param route - Contains Route path segments.
   *                Example: ['/dashboard', '/profile']
   */
  redirectTo(...route): void {
    this.router.navigate(route);
  }

  /**
  * This Method Is Use For Remove Blank And Null Key From Object.
  * @param obj - Object in which blank or null keys to be removed.
  */
  customJsonInclude(obj): void {
    for (const key in obj) {
      if (typeof obj[key] === 'object') {
        if (obj[key] && obj[key].length > 0) {
          obj[key] = this.removeEmptyElementsFromArray(obj[key]);
        }
        if (this.isEmptyObject(obj[key])) {
          delete obj[key];
        } else {
          this.customJsonInclude(obj[key]);
        }
      } else {
        if (obj[key] === undefined || obj[key] === null) {
          delete obj[key];
        }
      }
    }
  }

  emptyingNullValuesJSON(obj: any): void {
    for (const key in obj) {
      const value = obj[key];

      if (Array.isArray(value)) {
        const cleaned = this.removeEmptyElementsFromArray(value);
        cleaned.forEach(item => {
          if (typeof item === 'object' && item !== null) {
            this.customJsonInclude(item);
          }
        });

        if (cleaned.length === 0) {
          delete obj[key];
        } else {
          obj[key] = cleaned;
        }

      } else if (typeof value === 'object' && value !== null) {
        this.customJsonInclude(value);

        if (this.isEmptyObject(value)) {
          delete obj[key];
        }

      } else {
        if (value === null || value === undefined) {
          delete obj[key];
        }
      }
    }
  }

  /**
  * Method used to remove Empty Element From Array
  * @param arr  Selected Array.
  */
  removeEmptyElementsFromArray(arr): Array<any> {
    let index = -1;
    const arr_length = arr ? arr.length : 0;
    let resIndex = -1;
    const result = [];

    while (++index < arr_length) {
      const id = arr[index];
      if (id) {
        result[++resIndex] = id;
      }
    }
    return result;
  }

  /**
   * Function to retrieve logged-in user data from local storage
   */
  getLoggedInUser() {
    const userData = JSON.parse(localStorage.getItem('userData'));
    if (userData !== null) {
      return userData;
    }
  }

  /**
   * Function to retrieve a new refresh token from the server.
   */
  getRefreshToken() {

    const param = {
      refreshToken: this.getLoggedInUser()?.refreshToken
    }

    let headers = new HttpHeaders();
    headers = headers.set('Authorization', `Bearer ${this.getToken()}`);

    return this.http.post(environment.API_URL + this.serverVariableService.REFRESH_TOKEN, param, {headers})
  }

  /** 
  *
  * Used to decode token to get information
  * @param token Token which needs to be decoded
  * @returns {any} Returns information extracted from the token.
  */
  decodeToken(token): any {
    if (!this.isNullUndefinedOrBlank(token)) {
      const jwt = token.split('.')[1];
      const decodedJwtJsonData = JSON.parse(window.atob(jwt));
      return decodedJwtJsonData;
    }
  }

  /**
  * Clears Local Storage and Log out.
  */
  logout(toaster: boolean): void {
    if (toaster) {
      this.toasterService.success("Logged out successfully", '', {
        positionClass: 'toast-top-right',
        closeButton: true
      });
    }
    this.userProfilePicture = null;
    this.username = null;
    localStorage.clear();
    this.redirectTo('/auth/login');
  }

  /**
   * Stores data in the local storage with the provided key.
   * @param {string} key - The key under which the data will be stored in local storage.
   * @param {any} data - The data to be stored.
   */
  storeDataLocally(key: string, data: any): void {
    localStorage.setItem(key, data);
  }

  /**
   * Clear data in the local storage with the provided key.
   * @param {string} key - The key under which the data to be removed.
   */
  clearDataLocally(key: string): void {
    localStorage.removeItem(key);
  }

  /**
   * Formats a time string in 24-hour format to 12-hour format with AM/PM indication.
   * @param {string} timeString - The time string in the format "HH:MM" (24-hour format).
   * @returns {string} The formatted time string in the format "HH:MM AM/PM" (12-hour format).
  */
  formatTime(timeString) {
    const [hourString, minute] = timeString.split(":");
    const hour = +hourString % 24;
    return (hour % 12 || 12) + ":" + minute + (hour < 12 ? "AM" : "PM");
  }

  convertTimeToSeconds(timeString: string): number {
    if (timeString.includes(':')) {
      const [minutes, seconds] = timeString.split(':').map(Number);
      return (minutes * 60) + seconds;
    } else {
      const minutes = Number(timeString);
      return minutes * 60;
    }
  }
  
  /**
  * Redirect to Session Expired Page
  */
  goToSessionExpired() {
    this.redirectTo('/session-expired');
  }

  /**
  * Redirect to Access Denied Page
  */
  goToAccessDenied() {
    this.redirectTo('/access-denied');
  }

  /** Trim Object */
  trimObjectValues(obj) {
    return Object.keys(obj).reduce((acc, key) => {
      acc[key] = typeof obj[key] === 'string' ? obj[key].trim() : obj[key];
      return acc;
    }, {});
  }

  /**Export Report Function */
  exportReport(param, url) {

    let headers = new HttpHeaders();
    headers = headers.set('Authorization', `Bearer ${this.getToken()}`);

    const httpOptions = {
      responseType: 'blob' as 'json',
      headers
    };

    this.showLoader++;

    return this.http.post(`${environment.API_URL}${url}`, param, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        this.toasterService.error(error.message, '', this.toastConfig);
        return throwError(() => error);
      }),
      finalize(() => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }
      })
    );
  }

  exportReportGetApi(url: string) {

    let headers = new HttpHeaders();
    headers = headers.set('Authorization', `Bearer ${this.getToken()}`);

    const httpOptions = {
      responseType: 'blob' as 'json',
      headers
    };

    this.showLoader++;

    return this.http.get(`${environment.API_URL}${url}`, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        this.toasterService.error(error.message, '', this.toastConfig);
        return throwError(() => error);
      }),
      finalize(() => {
        if (this.showLoader > 0) {
          this.showLoader--;
        }
      })
    );
  }

  toInteger(value: any): number {
    return parseInt(`${value}`, 10);
  }

  isNumber(value: any): value is number {
    return !isNaN(this.toInteger(value));
  }

  padNumber(value: number) {
    if (this.isNumber(value)) {
      return `0${value}`.slice(-2);
    } else {
      return '';
    }
  }

  /*
  * Convert isActive key to disabled
  */
  transformDropdownItems(items: any[]) {
    return items.map(item => {
      return {
        ...item,
        disabled: !item.isActive
      };
    });
  }

  /*
  * Filter items based on id key
  */
  filterIsActive(items: any[], value: number) {
    if (!this.isEmptyObjectOrNullUndefined(items)) {
      if (value) {
        return items.filter(v => {
          return v.isActive || v.id === value;
        });
      }
      else {
        return items.filter(v => v.isActive)
      }
    }
    else return []
  }

  /*
  * Filter items based on label, value array key
  */
  filterIsActiveLV(items: any[], value: number) {
    if (!this.isEmptyObjectOrNullUndefined(items)) {
      if (value) {
        return items.filter(v => {
          return v.isActive || v.value === value;
        });
      }
      else {
        return items.filter(v => v.isActive)
      }
    } else return []
  }

  filterIsActiveMultiple(items: any[], value: number[]) {
    if (!this.isEmptyObjectOrNullUndefined(items)) {
      if (value) {
        return items.filter(v => v.isActive || value.includes(v.id));
      }
      else {
        return items.filter(v => v.isActive)
      }
    } return []
  }

  filterIsActiveMultipleLV(items: any[], value: number[]) {
    if (!this.isEmptyObjectOrNullUndefined(items)) {
      if (value) {
        return items.filter(v => v.isActive || value.includes(v.value));
      }
      else {
        return items.filter(v => v.isActive)
      }
    } else return []
  }

  filterIsActiveMultipleAndContains(items: any[], value: number[]) {
    if (!this.isEmptyObjectOrNullUndefined(items)) {
      if (value) {
        return items.filter(v => v.isActive && value.includes(v.value));
      }
      else {
        return items.filter(v => v.isActive)
      }
    } else return []
  }
  

  formatTimeArray(loggedInTime: [number, number]): string {
    return moment().hours(loggedInTime[0]).minutes(loggedInTime[1]).format('HH:mm');
  }

  convertToDateFromTime(time) {
    const splitTime = time.split(':');
    const date = new Date();
    date.setHours(+splitTime[0]);
    date.setMinutes(+splitTime[1]);
    date.setSeconds(0);
    return date.getTime();
  }

  convertToDate(dateStr: string): Date {
    const [day, month, year] = dateStr.split('-');
    return new Date(+year, +month - 1, +day);
  }

  convertTo24HourFormat(time: any): string {
    const date = new Date(time);
    
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    
    return `${hours}:${minutes}`;
  }

  openURL(link: string) {
    if (link) {
      const cleanUrl = link.replace(/^(https?:\/\/)?(localhost:\d+)?(127\.0\.0\.1:\d+)?/, '').trim();
      const fullUrl = cleanUrl.startsWith('http') ? cleanUrl : `https://${cleanUrl}`;
      window.open(fullUrl, '_blank');
    }
  }

  checkPageAccess([...name], pageName?) {
    
    const data = this.decodeToken(this.getToken())?.pageAccess;

    const page = data?.map(v => v?.userPrivileges?.filter(priv => name.includes(priv.privName) && (!pageName || priv.name === pageName))).reduce((acc, curr) => acc.concat(curr), []);

    return page?.length === 0 ? false : true;
  }

  getFileExtension(name) {
    const pathWithoutQuery = name.split('?')[0];
    const lastDotIndex = pathWithoutQuery.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return '';
    }
    const extension = pathWithoutQuery.slice(lastDotIndex);
    return extension.toLowerCase();
  }

  isImage(fileName: string): boolean {
    const ext = this.getFileExtension(fileName);
    return ext === '.jpg' || ext === '.png' || ext === ('.jpeg') || ext === '.gif' || ext === '.webp' || ext === '.jfif' || ext === '.avif';
  }

  isDocument(fileName: string): boolean {
    const ext = this.getFileExtension(fileName);
    return ext === '.pdf';
  }

  isExcel(fileName: string): boolean {
    const ext = this.getFileExtension(fileName);
    return ext === '.xlss' || ext === '.csv' || ext === '.xlsx' || ext === '.xls';
  }

  isMedia(fileName: string): boolean {
    const ext = this.getFileExtension(fileName);
    return ext === '.mp4' || ext === '.m4v';
  }

  isValidYouTubeLink(url: string): boolean {
    const youtubeRegex = /^(https?:\/\/)?(www\.|m\.)?(youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/|live\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(\S*)?$/;
    return youtubeRegex.test(url);
  }

  convertToEmbedYouTubeLink(url: string): string | null {
    const regex = /^(?:https?:\/\/)?(?:www\.|m\.)?(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/|live\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(?:\S*)?$/;
    const match = url.match(regex);
    if (match && match[1]) {
      return `https://youtube.com/embed/${match[1]}`;
    }
    return null;
  }

  // Check if all objects in array have unique values for given key
  isEverythingUnique(arr: any[], key: string) {
    const uniques = new Set(arr.map(item => item[key]));
    return [...uniques].length === arr.length;
  }

  // Check if all objects in array have unique values for given keys
  isEverythingUniqueMultiple(arr: any[], key1: string, key2?: string) {
    const filtered = arr.filter(item => item[key1] !== null && item[key1] !== undefined);
  
    const uniques = new Set(
      filtered.map(item => key2 ? `${item[key1]}-${item[key2]}` : item[key1])
    );
  
    return uniques.size === filtered.length;
  }

  // Check if all objects in array have same values for given keys dynamic
  isEverythingSameDynamic(arr: any[], keys: string[]) {
    if (!Array.isArray(arr) || !Array.isArray(keys) || keys.length === 0) return true;

    const seen = new Set<string>();

    for (const item of arr) {
      const keyParts = keys.map(k =>
        item[k] === null || item[k] === undefined ? '__MISSING__' : String(item[k])
      );

      const compositeKey = keyParts.join('|');

      if (seen.has(compositeKey)) return false;
      seen.add(compositeKey);
    }

    return true;
  }



  showSuccessMsg(msg) {
    this.toasterService.success(msg, '', {
      positionClass: 'toast-top-right',
      closeButton: true
    });
  }

  isDecimal(value: number): boolean {
    if (isNaN(value) || !isFinite(value)) {
      return false;
    }
    return value % 1 !== 0;
  }

  // download sample file
  downloadSampleFileHSN() {

    let data = null
    let name = null

    data = environment.FILE_PATH + environment.HSN_SAMPLE_FILE
    name = 'hsn_sample_file'

    this.blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
    saveAs(data, `${name}`);
  }

  downloadSampleFileInquiryItem() {
    let data = null
    let name = null

    data = environment.FILE_PATH + environment.INQUIRY_ITEM_SAMPLE_FILE
    name = 'inquiry_item_sample_file'

    this.blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
    saveAs(data, `${name}`);
  }

  errorHandler(error: HttpErrorResponse) {
    if (error.status === 0) {
      this.toasterService.error('Unable to Connect with server, Please check your internet connectivity or wait for the network to establish the connection.', '', this.toastConfig);
    }
    else {
      const errorDTO = Deserialize(error.error, ResponseWrapperDTO);

      if (errorDTO.status === 403) {
        this.goToSessionExpired();
      } else if (errorDTO.status === 500) {
        this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
      } else {
        this.toasterService.error(errorDTO.error ? errorDTO.message : errorDTO.message, '', this.toastConfig);
      }
    }
  }

  successToaster(msg: string) {
    this.toasterService.success(msg, '', this.toastConfig);
  }

  setHeaders(apiName: string) {
    let headers = new HttpHeaders();
    if (this.apisExcludingToken.indexOf(apiName) < 0) {
      headers = headers.set('Authorization', `Bearer ${this.getToken()}`);
    }
    return headers;
  }

  openItemDetailsInNewTab(itemId: number): void {
    const url = this.router.serializeUrl(this.router.createUrlTree(['/users/inventory/items/item-details', itemId]));
    window.open('#' + url, '_blank');
  }

  openStockDetailsInNewTab(id: number): void {
    const url = this.router.serializeUrl(this.router.createUrlTree(['/marka-image', id]));
    window.open('#' + url, '_blank');
  }

  // Full URL
  private fullUrl(apiName: string): string {
    return environment.API_URL + apiName;
  }

  // Handle API response and return Observable (with loader, toast params)
  private handleAPI(obs$: Observable<any>, config: HttpConfig = {}, onlyData = false): Observable<any> {
    const { toast = false, loader = true } = config || {};

    if (loader) this.showLoader++;

    return obs$.pipe(
      tap((res: HttpResponse<any>) => {
        const serverResponse: ResponseWrapperDTO = Deserialize(res, ResponseWrapperDTO);
        if (toast && serverResponse?.message) {
          this.successToaster(serverResponse.message);
        }
      }),
      map((dto: any) => { return onlyData ? dto['data'] : dto}),
      catchError((err: HttpErrorResponse) => {
        this.errorHandler(err);
        return throwError(() => err.message); 
      }),
      finalize(() => {
        if (loader && this.showLoader > 0) {
          this.showLoader--;
        }
      })
    );
  }

  skipEmitIfEmpty<T>() {
    return filter((res: T) => !this.isEmptyObjectOrNullUndefined(res));
  }

  get(api: string, config?: HttpConfig, onlyData = false): Observable<any> {
    const headers = this.setHeaders(api);
    const url = this.fullUrl(api);
    const request$ = this.http.get<ResponseWrapperDTO>(url, { headers });

    return this.handleAPI(request$, config, onlyData);
  }

  post(api: string, body: any, config?: HttpConfig, onlyData = false): Observable<any> {
    const headers = this.setHeaders(api);
    const url = this.fullUrl(api);
    this.skipNullUndefinedKeys(body);
    const request$ = this.http.post<ResponseWrapperDTO>(url, body, { headers });

    return this.handleAPI(request$, config, onlyData);
  }

  put(api: string, body: any, config?: HttpConfig, onlyData = false): Observable<any> {
    const headers = this.setHeaders(api);
    const url = this.fullUrl(api);
    const request$ = this.http.put<ResponseWrapperDTO>(url, body, { headers });

    return this.handleAPI(request$, config, onlyData);
  }

  delete(api: string, config?: HttpConfig, onlyData = false, body?: any): Observable<any> {
    const headers = this.setHeaders(api);
    const url = this.fullUrl(api);
    const request$ = this.http.delete<ResponseWrapperDTO>(url, { headers, body });

    return this.handleAPI(request$, config, onlyData);
  }

  skipNullUndefinedKeys(obj: any): void {
    if (this.isNullUndefinedOrBlank(obj)) {
      return;
    }
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      if (Array.isArray(value)) {
        const cleanedArr = value.map(item => {
          if (typeof item === 'object' && item !== null) {
            this.customJsonInclude(item);
          }
          return item;
        })
          .filter(item => {
            if (Array.isArray(item)) return item.length > 0;
            if (typeof item === 'object') return !this.isEmptyObject(item);
            if (typeof item === 'string') return item.trim() !== '';
            return item !== undefined && item !== null;
          });

        if (cleanedArr.length === 0) {
          delete obj[key];
        } else {
          obj[key] = cleanedArr;
        }
        return;
      }
      if (typeof value === 'object' && value !== null) {
        this.customJsonInclude(value);
        if (this.isEmptyObject(value)) {
          delete obj[key];
        }
        return;
      }
      if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {
        delete obj[key];
      }
    });
  }

  // Remove keys from object and return new object
  removeKeys<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
    const clone = { ...obj };
    for (const key of keys) {
      delete clone[key];
    }
    return clone;
  }

  // Converting webcam image base64 format to File Format
  base64ImageToFile(base64Data: string, itemName: string): File {
    const mimeTypeMatch = base64Data.match(/^data:(image\/(?:jpeg|jpg|png|gif|webp|bmp|svg\+xml));base64,/i);
    if (!mimeTypeMatch) {
      if (!/^[A-Za-z0-9+/]+=*$/.test(base64Data.replace(/\s/g, ''))) {
        throw new Error('Invalid base64 format. Expected image data URL or valid base64 string');
      }
      var mimeType = 'image/png';
      var cleanBase64 = base64Data;
    } else {
      var mimeType = mimeTypeMatch[1].toLowerCase();
      var cleanBase64 = base64Data.split(',')[1];
    }

    const sanitizedName = itemName.trim().replace(/[^\w\s-]/g, '').replace(/\s+/g, ' ');
    const filename = `${sanitizedName}.png`;
    const binaryString = atob(cleanBase64.replace(/\s/g, ''));
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return new File([bytes], filename, {
      type: mimeType,
      lastModified: Date.now()
    });
  }

  isValidValue = (val: any): boolean => {
    return val !== null && val !== undefined && val !== '' && val !== 0;
  };
  
}
 
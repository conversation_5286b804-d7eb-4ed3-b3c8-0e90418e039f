import { Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, computed, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { createArraySignal, createObjectSignal } from '@libs';
import { ExtraTime } from '@modal/ExtraTime';
import { ExtraTimePagination } from '@modal/request/ExtraTimePagination';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { NgbModalService } from '@service/ngb-modal.service';
import { UtilsService } from '@service/utils.service';
import { map, tap, takeUntil, Subject} from 'rxjs';

@Component({
  selector: 'app-extra-time-requests',
  templateUrl: './extra-time-requests.component.html',
  styleUrls: ['./extra-time-requests.component.scss']
})
export class ExtraTimeRequestsComponent implements OnInit, OnD<PERSON>roy {

  private route = inject(ActivatedRoute);
  private modalService = inject(NgbModalService);
  utilsService = inject(UtilsService)

  paginationRequest = createObjectSignal({} as ExtraTimePagination);
  extraTimeList = createArraySignal([] as ExtraTime[]);
  extraTimeObj = createObjectSignal({} as ExtraTime)

  isApprove = signal(false);

  destroy$ = new Subject<void>();

  private dropdown = toSignal(this.route.data);
  dropdownData = computed(() => this.dropdown()?.['dropdown'] || []);

  constructor() { }

  ngOnInit(): void {
    this.getExtraTimeListing();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getExtraTimeListing = () => {
    this.utilsService.get(this.utilsService.serverVariableService.USER_REQ_TIME_LIST).pipe(
      map((res) => res.data),
      tap((data: ExtraTime[]) => {
        this.extraTimeList.set(data);
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  onChangeFilter = (event: any, type: 'userId') => {
    switch (type) {
      case 'userId':
        this.paginationRequest.update(a => ({ ...a, userId: event }));
        break;
    }
    this.getExtraTimeListing();
  }

  onRefresh = () => {
    this.getExtraTimeListing()
  }

  openApproveDeclineModal = (content: TemplateRef<any>, item: ExtraTime, isApprove: boolean) => {
    this.modalService.open(content)
    this.isApprove.set(isApprove)
    this.extraTimeObj.set(item)
  }

  onApproveDecline = (modal: NgbModalRef) => {
    const API = `${this.utilsService.serverVariableService.USER_EXTEND_TIME_APPROVE}?id=${this.extraTimeObj.get().userId}`

    this.utilsService.get(API, { toast: true }, true).pipe(
      tap(() => {
        this.modalService.close(modal)
        this.getExtraTimeListing()
      })
    ).subscribe()
  }
}

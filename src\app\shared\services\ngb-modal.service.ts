import { Injectable, inject } from '@angular/core';
import { NgbModal, NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';

@Injectable({
  providedIn: 'root'
})
export class NgbModalService {

  private modal = inject(NgbModal);
  private defaultConfig: NgbModalOptions = {
    centered: true,
    windowClass: 'modal-md'
  };

  open(content: any, config?: NgbModalOptions) {
    const modalConfig = { ...this.defaultConfig, ...config };
    return this.modal.open(content, modalConfig);
  }

  close(modalRef: any, result?: any) {
    modalRef?.close(result);
  }

  closeAllModals() {
    this.modal?.dismissAll();
  }
}

import { AvailableQtyModalData } from "./SBTBranchStock"

export interface AuditTicketsSave {
    id: number
    ticketsId: number
    branchId: number
    warehouseId: number
    ticketSubjectId: number
    ticketNote: string
    isRequiredImage: boolean
    isItemWise: boolean
    userId: number
    auditTicketItemReqList: AuditTicketItemReq[]
    auditTicketLocReqList: AuditTicketLocReq[]
    documents?: AuditAttachments[]
    auditTicketItemReq?: AuditTicketItemReq[]
}

export interface AuditTicketItemReq {
    itemId: number
    availableTotalCartons: number
    availableTotalLooseQty: number
    availableTotalQty: number
    availableTotalPieces: number
    allLocation: string
    branchStockList: any;
}

export interface AuditTicketLocReq {
    location: string
    rackId: number
    aisleId: number
    locationType: string
}

export interface AuditAttachments {
    id: number;
    file: File;
    originalName: string;
    formattedName: string;
}

export interface ATDropdown {
    branches: { branchName: string, id: number, isActive: boolean }[]
    location: { label: string, value: number, other: number, other_2: string, isActive: boolean }[]
    ticketSubject: { label: string, value: number, isActive: boolean }[]
    warehouse: { id: number, warehouseName: string, isActive: boolean, branchId: number }[]
    users: { label: string, value: number, isActive: boolean }[]
    items: ATItemDropdown[]
    filteredWarehouse: { id: number, warehouseName: string, isActive: boolean, branchId: number }[]
    filteredLocation: { label: string, value: number, other: number, other_2: string, isActive: boolean }[]
}

export interface ATItemDropdown {
    allLocation: string
    availableTotalCartons: number
    availableTotalPieces: number
    availableTotalLooseQty: number
    displayName: string
    formattedName: string
    hsnCode: string
    id: number
    originalName: string
    skuId: string
}

export interface AuditTicketListing {
    branchId: number,
    branchName: string,
    isExpand: boolean
    tickets: TicketListing[]
}

export interface TicketListing {
    id: number,
    ticketId: string,
    ticketNumber: string,
    ticketSubject: string,
    warehouseName: string,
    assignDatetime: string,
    ticketType: string,
    status: string,
    createdBy: string,
    assignTo: string,
    assignToId : number,
    assignDay: string
    note: string,
    createdDate: string
    documents: AuditAttachments[]
    ticketActionType : string,
    childTicketId: number
}

export interface AuditDetailsList {
    id: number,
    ticketNumber: string,
    ticketSubject: string,
    status: string,
    ticketType: string,
    branchId: number,
    ticketActionType: string
    branchName: string,
    ticketNote: string,
    ticketId: string,
    subjectStatus: string
    assignTo: string
    assignedTo: string
    updatedDate: string
    reassignTo: string
    warehouseName: string
    ticketDocuments: AuditAttachments[]
    updateHistory: {
        updatedBy: string,
        updatedDate: string,
        updateDescription: string
        userProfileUrl: string
    }[]
    itemDetails: ItemChildTickets[]
    childTicket: {
        itemDetails: ItemChildTickets[]
        updatedBy: string
        updatedDate: string
    }
    auditHistory: {
        dateTime: string
        user: string
        locations: {
            locationName: string
            items: ItemChildTickets[]
        }[]
        isExpand: boolean
    }[]
    result?: {
        dateTime: string;
        user: string;
        newArray: ItemChildTickets[];
        isExpand?: boolean
    }[];
    isItemWise: boolean
    qcProperties: QCProperties[]
    photos: string[]
    failedDocs: string[];
    marka: string;
    location: string;
    isFailed : boolean;
    failedReason: string
}

export interface ItemChildTickets {
    itemName: string,
    skuId: string,
    itemId: number,
    locationName: string,
    locationType: string,
    cartonQtyDisplay: number,
    looseQtyDisplay: number,
    cartonQtyAvailable: number,
    looseQtyAvailable: number,
    notes: string,
    id: number,
    itemImage: string,
    documents: AuditAttachments[],
    availableQtyModalData: AvailableQtyModalData[],
    locationId: number
}

export interface QCProperties {
    label: string
    value: string
    unit: {
        unitName: string
        shortCode: string
        unitMasterCategory: {
            label: string
            value: string
            baseUnit: string
        }
    }
    cartonWeight: number
    actualItemWeight: number
    actualItemLength: number
    actualItemWidth: number
    actualItemHeight: number
    itemWithBoxWeight: number
    actualUnit: string
    qcResultDocs: string[]
    colors: {
        id: number
        colorName: string
    }[]
    actualColors: {
        id: number
        colorName: string
    }[]
    packingTypes: {
        id: number
        packingName: string
    }[]
    actualPackingTypes: {
        id: number
        packingName: string
    }[]
}
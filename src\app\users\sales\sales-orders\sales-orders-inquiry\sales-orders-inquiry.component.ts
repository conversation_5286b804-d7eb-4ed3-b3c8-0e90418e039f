import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { SalesOrderService } from '../sales-order.service';
import { UtilsService } from '@service/utils.service';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import moment from 'moment';

@Component({
  selector: 'app-sales-orders-inquiry',
  templateUrl: './sales-orders-inquiry.component.html',
  styleUrls: ['./sales-orders-inquiry.component.scss']
})
export class SalesOrdersInquiryComponent implements OnInit {

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective;
  
  salesOrderService = inject(SalesOrderService);
  utilsService = inject(UtilsService);
  moment = moment;

  constructor() { }

  ngOnInit(): void {
    
  }

  get salesService() {
    return this.salesOrderService;
  }

  open(): void {
    if(!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  // Debug method - can be called from browser console
  debugSelection() {
    this.salesOrderService.debugSelection();
  }

}

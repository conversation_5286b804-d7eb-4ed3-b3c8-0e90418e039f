import { deserializeAs, serializeAs } from 'cerialize';

export class RegistrationSA {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    // @serializeAs('shippingId')
    @deserializeAs('shippingId')
    private _shippingId: number;

    @serializeAs('addressLineName')
    @deserializeAs('addressLineName')
    private _addressLineName: string;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('link')
    @deserializeAs('link')
    private _link: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('stateId')
    @deserializeAs('stateId')
    private _stateId: number;

    @serializeAs('cityId')
    @deserializeAs('cityId')
    private _cityId: number;

    // @serializeAs('state')
    @deserializeAs('stateList')
    private _stateList: any[];

    // @serializeAs('city')
    @deserializeAs('cityList')
    private _cityList: any[];

    // @serializeAs('country')
    @deserializeAs('countryList')
    private _countryList: any[];

    @deserializeAs('country')
    private _country: any;

    @deserializeAs('state')
    private _state: any;

    @deserializeAs('city')
    private _city: any;

    @deserializeAs('addressLine')
    private _addressLine: any;

    constructor() {
        this.isDefault = false;
        this.cityList = [];
        this.stateList = [];
        this.countryList = [];
        this.isActive = false;
    }


    /**
     * Getter link
     * @return {string}
     */
	public get link(): string {
		return this._link;
	}

    /**
     * Setter link
     * @param {string} value
     */
	public set link(value: string) {
		this._link = value;
	}


    /**
     * Getter addressLine
     * @return {any}
     */
	public get addressLine(): any {
		return this._addressLine;
	}

    /**
     * Setter addressLine
     * @param {any} value
     */
	public set addressLine(value: any) {
		this._addressLine = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter shippingId
     * @return {number}
     */
	public get shippingId(): number {
		return this._shippingId;
	}

    /**
     * Getter addressLineName
     * @return {string}
     */
	public get addressLineName(): string {
		return this._addressLineName;
	}

    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter stateId
     * @return {number}
     */
	public get stateId(): number {
		return this._stateId;
	}

    /**
     * Getter cityId
     * @return {number}
     */
	public get cityId(): number {
		return this._cityId;
	}

    /**
     * Getter stateList
     * @return {any[]}
     */
	public get stateList(): any[] {
		return this._stateList;
	}

    /**
     * Getter cityList
     * @return {any[]}
     */
	public get cityList(): any[] {
		return this._cityList;
	}

    /**
     * Getter countryList
     * @return {any[]}
     */
	public get countryList(): any[] {
		return this._countryList;
	}

    /**
     * Getter country
     * @return {any}
     */
	public get country(): any {
		return this._country;
	}

    /**
     * Getter state
     * @return {any}
     */
	public get state(): any {
		return this._state;
	}

    /**
     * Getter city
     * @return {any}
     */
	public get city(): any {
		return this._city;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter shippingId
     * @param {number} value
     */
	public set shippingId(value: number) {
		this._shippingId = value;
	}

    /**
     * Setter addressLineName
     * @param {string} value
     */
	public set addressLineName(value: string) {
		this._addressLineName = value;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter stateId
     * @param {number} value
     */
	public set stateId(value: number) {
		this._stateId = value;
	}

    /**
     * Setter cityId
     * @param {number} value
     */
	public set cityId(value: number) {
		this._cityId = value;
	}

    /**
     * Setter stateList
     * @param {any[]} value
     */
	public set stateList(value: any[]) {
		this._stateList = value;
	}

    /**
     * Setter cityList
     * @param {any[]} value
     */
	public set cityList(value: any[]) {
		this._cityList = value;
	}

    /**
     * Setter countryList
     * @param {any[]} value
     */
	public set countryList(value: any[]) {
		this._countryList = value;
	}

    /**
     * Setter country
     * @param {any} value
     */
	public set country(value: any) {
		this._country = value;
	}

    /**
     * Setter state
     * @param {any} value
     */
	public set state(value: any) {
		this._state = value;
	}

    /**
     * Setter city
     * @param {any} value
     */
	public set city(value: any) {
		this._city = value;
	}
    


}